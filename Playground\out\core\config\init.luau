-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Configuration interfaces
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "config", "interfaces", "IConfigurationService") or {} do
	exports[_k] = _v
end
-- Configuration types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "config", "types", "ConfigurationTypes") or {} do
	exports[_k] = _v
end
-- Configuration services
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "config", "services", "ConfigurationService") or {} do
	exports[_k] = _v
end
return exports
