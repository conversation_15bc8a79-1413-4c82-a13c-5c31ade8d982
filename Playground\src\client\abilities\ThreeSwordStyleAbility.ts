import { AbilityBase } from "./AbilityBase";
import { TweenService, RunService, Workspace, Players, Lighting } from "@rbxts/services";

export class ThreeSwordStyleAbility extends AbilityBase {
	private swordEffects: Part[] = [];
	private swordConnection?: RBXScriptConnection;
	private cooldownEndTime = 0;
	private isActive = false;

	constructor() {
		super("THREE_SWORD_STYLE", 10);
	}

	public isOnCooldown(): boolean {
		return tick() < this.cooldownEndTime;
	}

	private startCooldown(): void {
		this.cooldownEndTime = tick() + this.getCooldownTime();
	}

	public activate(): void {
		if (this.isOnCooldown() || this.isActive) return;

		const player = Players.LocalPlayer;
		const character = player.Character;
		if (!character) return;

		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
		if (!humanoidRootPart) return;

		print("⚔️ Activating <PERSON>oro's Three Sword Style!");
		this.isActive = true;

		// Phase 1: Character sword preparation animation
		this.createThreeSwordStyleAnimation(character);

		// Phase 2: Create sword aura
		this.createSwordAura(character);

		// Phase 2: Create three swords
		this.createThreeSwords(character);

		// Phase 3: Execute triple slash sequence
		task.delay(1, () => {
			this.executeTripleSlash(humanoidRootPart);
		});

		// Phase 4: Create air blade attacks
		task.delay(1.5, () => {
			this.createAirBlades(humanoidRootPart);
		});

		// Phase 5: Environmental sword effects
		this.createEnvironmentalSwordEffects();

		// Duration: 4 seconds
		task.delay(4, () => {
			this.cleanupEffects();
			this.isActive = false;
		});

		this.startCooldown();
	}

	private createThreeSwordStyleAnimation(character: Model): void {
		print("⚔️ Creating Zoro's Three Sword Style animation");

		const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
		if (!humanoid) return;

		// Create proper Motor6D animation like QuakeAbility
		this.createCharacterAnimation(character);
	}

	private createCharacterAnimation(character: Model): void {
		const torso = character.FindFirstChild("Torso") as Part;
		const upperTorso = character.FindFirstChild("UpperTorso") as Part;

		if (torso) {
			// R6 character
			this.animateR6Character(torso);
		} else if (upperTorso) {
			// R15 character
			this.animateR15Character(upperTorso);
		}
	}

	private animateR6Character(torso: Part): void {
		const rightShoulder = torso.FindFirstChild("Right Shoulder") as Motor6D;
		const leftShoulder = torso.FindFirstChild("Left Shoulder") as Motor6D;
		const rightHip = torso.FindFirstChild("Right Hip") as Motor6D;
		const leftHip = torso.FindFirstChild("Left Hip") as Motor6D;
		const neck = torso.FindFirstChild("Neck") as Motor6D;

		if (!rightShoulder || !leftShoulder) return;

		// Store original C0 values
		const originalRightC0 = rightShoulder.C0;
		const originalLeftC0 = leftShoulder.C0;
		const originalRightHipC0 = rightHip ? rightHip.C0 : undefined;
		const originalLeftHipC0 = leftHip ? leftHip.C0 : undefined;
		const originalNeckC0 = neck ? neck.C0 : undefined;

		// Phase 1: Sword drawing stance (0-0.5s) - Arms crossed
		print("⚔️ Sword drawing stance");

		const rightArmDraw = TweenService.Create(
			rightShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalRightC0.mul(CFrame.Angles(-math.pi / 4, 0, -math.pi / 2)),
			},
		);
		const leftArmDraw = TweenService.Create(
			leftShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 4, 0, math.pi / 2)),
			},
		);

		rightArmDraw.Play();
		leftArmDraw.Play();

		// Phase 2: Three sword ready position (0.5-1s) - Classic Santoryu pose
		task.delay(0.5, () => {
			print("🗡️ Three Sword ready position");

			const rightArmReady = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 6, 0, -math.pi / 3)),
				},
			);
			const leftArmReady = TweenService.Create(
				leftShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 6, 0, math.pi / 3)),
				},
			);

			rightArmReady.Play();
			leftArmReady.Play();

			// Head slightly down for mouth sword
			if (neck && originalNeckC0) {
				const neckTilt = TweenService.Create(
					neck,
					new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalNeckC0.mul(CFrame.Angles(math.pi / 8, 0, 0)),
					},
				);
				neckTilt.Play();
			}
		});

		// Phase 3: Slash preparation (1-1.5s) - Arms back for power
		task.delay(1, () => {
			print("⚔️ Slash preparation");

			const rightArmPrep = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 2, 0, -math.pi / 2)),
				},
			);
			const leftArmPrep = TweenService.Create(
				leftShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 2, 0, math.pi / 2)),
				},
			);

			rightArmPrep.Play();
			leftArmPrep.Play();
		});

		// Phase 4: Triple slash execution (1.5-2.5s) - Sequential slashing
		task.delay(1.5, () => {
			print("💥 Triple slash execution!");

			// First slash - Right diagonal
			const rightSlash1 = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(math.pi / 4, 0, -math.pi / 4)),
				},
			);
			rightSlash1.Play();

			// Second slash - Left diagonal (0.2s delay)
			task.delay(0.2, () => {
				const leftSlash = TweenService.Create(
					leftShoulder,
					new TweenInfo(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
					{
						C0: originalLeftC0.mul(CFrame.Angles(math.pi / 4, 0, math.pi / 4)),
					},
				);
				leftSlash.Play();
			});

			// Third slash - Both arms horizontal (0.4s delay)
			task.delay(0.4, () => {
				const rightSlash2 = TweenService.Create(
					rightShoulder,
					new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
					{
						C0: originalRightC0.mul(CFrame.Angles(0, 0, -math.pi / 2)),
					},
				);
				const leftSlash2 = TweenService.Create(
					leftShoulder,
					new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
					{
						C0: originalLeftC0.mul(CFrame.Angles(0, 0, math.pi / 2)),
					},
				);

				rightSlash2.Play();
				leftSlash2.Play();
			});
		});

		// Phase 5: Final stance (2.5-3.5s) - Dramatic finish pose
		task.delay(2.5, () => {
			print("⚔️ Final sword stance");

			const rightArmFinal = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 3, 0, -math.pi / 6)),
				},
			);
			const leftArmFinal = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{
					C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 3, 0, math.pi / 6)),
				},
			);

			rightArmFinal.Play();
			leftArmFinal.Play();

			// Wide stance for dramatic finish
			if (rightHip && leftHip && originalRightHipC0 && originalLeftHipC0) {
				const rightLegStance = TweenService.Create(
					rightHip,
					new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalRightHipC0.mul(CFrame.Angles(0, 0, math.pi / 12)),
					},
				);
				const leftLegStance = TweenService.Create(
					leftHip,
					new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalLeftHipC0.mul(CFrame.Angles(0, 0, -math.pi / 12)),
					},
				);

				rightLegStance.Play();
				leftLegStance.Play();
			}
		});

		// Phase 6: Return to normal (3.5-4s)
		task.delay(3.5, () => {
			print("⚔️ Returning to normal stance");

			const rightArmReturn = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalRightC0,
				},
			);
			const leftArmReturn = TweenService.Create(
				leftShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0,
				},
			);

			rightArmReturn.Play();
			leftArmReturn.Play();

			// Restore legs and neck
			if (rightHip && originalRightHipC0) {
				const rightLegReturn = TweenService.Create(
					rightHip,
					new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalRightHipC0,
					},
				);
				rightLegReturn.Play();
			}

			if (leftHip && originalLeftHipC0) {
				const leftLegReturn = TweenService.Create(
					leftHip,
					new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalLeftHipC0,
					},
				);
				leftLegReturn.Play();
			}

			if (neck && originalNeckC0) {
				const neckReturn = TweenService.Create(
					neck,
					new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalNeckC0,
					},
				);
				neckReturn.Play();
			}
		});
	}

	private animateR15Character(upperTorso: Part): void {
		const rightShoulder = upperTorso.FindFirstChild("RightShoulder") as Motor6D;
		const leftShoulder = upperTorso.FindFirstChild("LeftShoulder") as Motor6D;

		if (!rightShoulder || !leftShoulder) return;

		// Store original C0 values
		const originalRightC0 = rightShoulder.C0;
		const originalLeftC0 = leftShoulder.C0;

		// Similar animation sequence for R15 (simplified)
		print("⚔️ R15 Three Sword Style animation");

		// Phase 1: Drawing
		const rightArmDraw = TweenService.Create(
			rightShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalRightC0.mul(CFrame.Angles(-math.pi / 4, 0, -math.pi / 2)),
			},
		);
		const leftArmDraw = TweenService.Create(
			leftShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 4, 0, math.pi / 2)),
			},
		);

		rightArmDraw.Play();
		leftArmDraw.Play();

		// Continue with similar phases...
		task.delay(0.5, () => {
			const rightArmReady = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 6, 0, -math.pi / 3)),
				},
			);
			const leftArmReady = TweenService.Create(
				leftShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 6, 0, math.pi / 3)),
				},
			);

			rightArmReady.Play();
			leftArmReady.Play();
		});

		// Return to normal (3.5s delay)
		task.delay(3.5, () => {
			const rightArmReturn = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalRightC0,
				},
			);
			const leftArmReturn = TweenService.Create(
				leftShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0,
				},
			);

			rightArmReturn.Play();
			leftArmReturn.Play();
		});
	}

	private createSwordAura(character: Model): void {
		print("⚔️ Creating Zoro's sword aura");

		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
		if (!humanoidRootPart) return;

		// Create sword aura around character
		const swordAura = new Instance("Part");
		swordAura.Name = "SwordAura";
		swordAura.Shape = Enum.PartType.Ball;
		swordAura.Size = new Vector3(8, 8, 8);
		swordAura.Color = Color3.fromRGB(200, 200, 255); // Steel blue
		swordAura.Material = Enum.Material.ForceField;
		swordAura.Transparency = 0.7;
		swordAura.CanCollide = false;
		swordAura.Anchored = true;
		swordAura.Position = humanoidRootPart.Position;
		swordAura.Parent = Workspace;
		this.swordEffects.push(swordAura);

		// Add sharp metallic light
		const auraLight = new Instance("PointLight");
		auraLight.Color = Color3.fromRGB(200, 220, 255);
		auraLight.Brightness = 4;
		auraLight.Range = 20;
		auraLight.Parent = swordAura;

		// Pulsing aura effect
		const pulseTween = TweenService.Create(
			swordAura,
			new TweenInfo(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
			{
				Size: new Vector3(10, 10, 10),
				Transparency: 0.5,
			},
		);
		pulseTween.Play();

		// Follow character
		const followConnection = RunService.Heartbeat.Connect(() => {
			if (humanoidRootPart.Parent && swordAura.Parent) {
				swordAura.Position = humanoidRootPart.Position;
			}
		});

		// Cleanup after duration
		task.delay(4, () => {
			followConnection.Disconnect();
			pulseTween.Cancel();

			const fadeTween = TweenService.Create(swordAura, new TweenInfo(1, Enum.EasingStyle.Quad), {
				Transparency: 1,
			});
			fadeTween.Play();
			fadeTween.Completed.Connect(() => {
				swordAura.Destroy();
				const index = this.swordEffects.indexOf(swordAura);
				if (index > -1) {
					this.swordEffects.remove(index);
				}
			});
		});
	}

	private createThreeSwords(character: Model): void {
		print("🗡️ Creating Zoro's three swords");

		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
		if (!humanoidRootPart) return;

		// Create three swords around the character
		const swordPositions = [
			new Vector3(2, 1, 0), // Right sword
			new Vector3(-2, 1, 0), // Left sword
			new Vector3(0, 2, 0), // Mouth sword (above)
		];

		const swordColors = [
			Color3.fromRGB(255, 255, 255), // White
			Color3.fromRGB(220, 220, 220), // Light gray
			Color3.fromRGB(200, 200, 200), // Gray
		];

		for (let i = 0; i < 3; i++) {
			const sword = new Instance("Part");
			sword.Name = `ZoroSword${i + 1}`;
			sword.Shape = Enum.PartType.Block;
			sword.Size = new Vector3(0.3, 6, 0.8);
			sword.Color = swordColors[i];
			sword.Material = Enum.Material.Metal;
			sword.Transparency = 0.1;
			sword.CanCollide = false;
			sword.Anchored = true;

			const swordPosition = humanoidRootPart.Position.add(swordPositions[i]);
			sword.Position = swordPosition;

			// Rotate sword to look like a blade
			sword.CFrame = sword.CFrame.mul(CFrame.Angles(0, 0, math.pi / 6));

			sword.Parent = Workspace;
			this.swordEffects.push(sword);

			// Add metallic shine
			const swordLight = new Instance("PointLight");
			swordLight.Color = Color3.fromRGB(255, 255, 255);
			swordLight.Brightness = 2;
			swordLight.Range = 8;
			swordLight.Parent = sword;

			// Floating animation
			const floatTween = TweenService.Create(
				sword,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
				{
					Position: swordPosition.add(new Vector3(0, 0.5, 0)),
				},
			);
			floatTween.Play();

			// Spinning animation
			const spinTween = TweenService.Create(
				sword,
				new TweenInfo(2, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1),
				{
					CFrame: sword.CFrame.mul(CFrame.Angles(0, math.pi * 2, 0)),
				},
			);
			spinTween.Play();

			// Create sword trail effect
			this.createSwordTrail(sword);
		}
	}

	private createSwordTrail(sword: Part): void {
		// Create attachment for trail
		const attachment1 = new Instance("Attachment");
		attachment1.Name = "TrailAttachment1";
		attachment1.Position = new Vector3(0, 3, 0); // Top of sword
		attachment1.Parent = sword;

		const attachment2 = new Instance("Attachment");
		attachment2.Name = "TrailAttachment2";
		attachment2.Position = new Vector3(0, -3, 0); // Bottom of sword
		attachment2.Parent = sword;

		// Create trail
		const trail = new Instance("Trail");
		trail.Name = "SwordTrail";
		trail.Attachment0 = attachment1;
		trail.Attachment1 = attachment2;
		trail.Color = new ColorSequence([
			new ColorSequenceKeypoint(0, Color3.fromRGB(255, 255, 255)), // White
			new ColorSequenceKeypoint(0.5, Color3.fromRGB(200, 220, 255)), // Light blue
			new ColorSequenceKeypoint(1, Color3.fromRGB(100, 150, 255)), // Blue
		]);
		trail.Transparency = new NumberSequence([new NumberSequenceKeypoint(0, 0.2), new NumberSequenceKeypoint(1, 1)]);
		trail.Lifetime = 0.5;
		trail.MinLength = 0;
		trail.Parent = sword;
	}

	private executeTripleSlash(humanoidRootPart: Part): void {
		print("💥 Executing triple slash sequence!");

		const direction = humanoidRootPart.CFrame.LookVector;

		// Create three slash effects in sequence
		for (let i = 0; i < 3; i++) {
			task.delay(i * 0.2, () => {
				this.createSlashEffect(humanoidRootPart.Position, direction, i);
			});
		}
	}

	private createSlashEffect(position: Vector3, direction: Vector3, slashIndex: number): void {
		// Create slash visual effect
		const slash = new Instance("Part");
		slash.Name = `SlashEffect${slashIndex + 1}`;
		slash.Shape = Enum.PartType.Block;
		slash.Size = new Vector3(0.2, 8, 12);
		slash.Color = Color3.fromRGB(255, 255, 255);
		slash.Material = Enum.Material.Neon;
		slash.Transparency = 0.3;
		slash.CanCollide = false;
		slash.Anchored = true;

		// Position slash in front of character
		const slashPosition = position.add(direction.mul(5 + slashIndex * 3));
		slash.Position = slashPosition;

		// Rotate slash based on index for different angles
		const rotationAngles = [
			CFrame.Angles(0, 0, math.pi / 4), // Diagonal right
			CFrame.Angles(0, 0, -math.pi / 4), // Diagonal left
			CFrame.Angles(0, 0, 0), // Horizontal
		];
		slash.CFrame = CFrame.lookAt(slashPosition, slashPosition.add(direction)).mul(rotationAngles[slashIndex]);

		slash.Parent = Workspace;
		this.swordEffects.push(slash);

		// Add slash light
		const slashLight = new Instance("PointLight");
		slashLight.Color = Color3.fromRGB(255, 255, 255);
		slashLight.Brightness = 6;
		slashLight.Range = 15;
		slashLight.Parent = slash;

		// Slash animation - expand and fade
		const slashTween = TweenService.Create(
			slash,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				Size: new Vector3(0.2, 12, 18),
				Transparency: 1,
			},
		);
		slashTween.Play();

		// Create slash impact effect
		this.createSlashImpact(slashPosition);

		// Remove slash after animation
		slashTween.Completed.Connect(() => {
			slash.Destroy();
			const index = this.swordEffects.indexOf(slash);
			if (index > -1) {
				this.swordEffects.remove(index);
			}
		});
	}

	private createSlashImpact(position: Vector3): void {
		// Create impact sparkles
		for (let i = 0; i < 8; i++) {
			const sparkle = new Instance("Part");
			sparkle.Name = "SlashSparkle";
			sparkle.Shape = Enum.PartType.Block;
			sparkle.Size = new Vector3(0.2, 0.2, 0.2);
			sparkle.Color = Color3.fromRGB(255, 255, 255);
			sparkle.Material = Enum.Material.Neon;
			sparkle.Transparency = 0.2;
			sparkle.CanCollide = false;
			sparkle.Anchored = true;
			sparkle.Position = position.add(new Vector3(math.random(-3, 3), math.random(-3, 3), math.random(-3, 3)));
			sparkle.Parent = Workspace;
			this.swordEffects.push(sparkle);

			// Sparkle animation
			const sparkleTween = TweenService.Create(
				sparkle,
				new TweenInfo(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					Size: new Vector3(0.05, 0.05, 0.05),
					Transparency: 1,
				},
			);
			sparkleTween.Play();
			sparkleTween.Completed.Connect(() => {
				sparkle.Destroy();
				const index = this.swordEffects.indexOf(sparkle);
				if (index > -1) {
					this.swordEffects.remove(index);
				}
			});
		}
	}

	private createAirBlades(humanoidRootPart: Part): void {
		print("🌪️ Creating air blade attacks!");

		const direction = humanoidRootPart.CFrame.LookVector;

		// Create multiple air blades
		for (let i = 0; i < 5; i++) {
			task.delay(i * 0.1, () => {
				this.launchAirBlade(humanoidRootPart.Position, direction, i);
			});
		}
	}

	private launchAirBlade(startPosition: Vector3, direction: Vector3, bladeIndex: number): void {
		// Create air blade projectile
		const airBlade = new Instance("Part");
		airBlade.Name = `AirBlade${bladeIndex + 1}`;
		airBlade.Shape = Enum.PartType.Block;
		airBlade.Size = new Vector3(0.3, 4, 8);
		airBlade.Color = Color3.fromRGB(200, 220, 255);
		airBlade.Material = Enum.Material.ForceField;
		airBlade.Transparency = 0.4;
		airBlade.CanCollide = false;
		airBlade.Anchored = true;

		// Slight angle variation for each blade
		const angleVariation = (bladeIndex - 2) * 0.2; // -0.4 to 0.4 radians
		const variedDirection = CFrame.fromAxisAngle(new Vector3(0, 1, 0), angleVariation).mul(
			new Vector3(direction.X, direction.Y, direction.Z),
		);

		airBlade.Position = startPosition.add(direction.mul(3));
		airBlade.CFrame = CFrame.lookAt(airBlade.Position, airBlade.Position.add(variedDirection));
		airBlade.Parent = Workspace;
		this.swordEffects.push(airBlade);

		// Add air blade light
		const bladeLight = new Instance("PointLight");
		bladeLight.Color = Color3.fromRGB(200, 220, 255);
		bladeLight.Brightness = 3;
		bladeLight.Range = 12;
		bladeLight.Parent = airBlade;

		// Create air blade trail
		this.createAirBladeTrail(airBlade);

		// Move air blade
		const speed = 60;
		const maxDistance = 100;
		let travelDistance = 0;

		const moveConnection = RunService.Heartbeat.Connect((deltaTime) => {
			if (!airBlade.Parent) {
				moveConnection.Disconnect();
				return;
			}

			const moveDistance = speed * deltaTime;
			travelDistance += moveDistance;

			if (travelDistance >= maxDistance) {
				// Create impact effect at max range
				this.createAirBladeImpact(airBlade.Position);
				moveConnection.Disconnect();
				airBlade.Destroy();
				return;
			}

			// Move air blade
			airBlade.Position = airBlade.Position.add(variedDirection.mul(moveDistance));

			// Check for collision
			this.checkAirBladeCollision(airBlade, variedDirection, moveConnection);
		});

		// Safety cleanup
		task.delay(2, () => {
			if (moveConnection.Connected) {
				moveConnection.Disconnect();
			}
			if (airBlade.Parent) {
				this.createAirBladeImpact(airBlade.Position);
				airBlade.Destroy();
			}
		});
	}

	private createAirBladeTrail(airBlade: Part): void {
		// Create trail attachments
		const attachment1 = new Instance("Attachment");
		attachment1.Name = "BladeTrailAttachment1";
		attachment1.Position = new Vector3(0, 0, 4); // Front of blade
		attachment1.Parent = airBlade;

		const attachment2 = new Instance("Attachment");
		attachment2.Name = "BladeTrailAttachment2";
		attachment2.Position = new Vector3(0, 0, -4); // Back of blade
		attachment2.Parent = airBlade;

		// Create air trail
		const trail = new Instance("Trail");
		trail.Name = "AirBladeTrail";
		trail.Attachment0 = attachment1;
		trail.Attachment1 = attachment2;
		trail.Color = new ColorSequence([
			new ColorSequenceKeypoint(0, Color3.fromRGB(255, 255, 255)), // White
			new ColorSequenceKeypoint(0.5, Color3.fromRGB(200, 220, 255)), // Light blue
			new ColorSequenceKeypoint(1, Color3.fromRGB(150, 180, 255)), // Blue
		]);
		trail.Transparency = new NumberSequence([new NumberSequenceKeypoint(0, 0.3), new NumberSequenceKeypoint(1, 1)]);
		trail.Lifetime = 0.8;
		trail.MinLength = 0;
		trail.Parent = airBlade;
	}

	private checkAirBladeCollision(airBlade: Part, direction: Vector3, moveConnection: RBXScriptConnection): void {
		// Simple collision detection
		const raycast = Workspace.Raycast(airBlade.Position, direction.mul(3), new RaycastParams());

		if (raycast && raycast.Instance) {
			const hitPart = raycast.Instance;

			// Skip if it's our own sword effects or player characters
			if (
				hitPart.Name.find("Sword")[0] !== undefined ||
				hitPart.Name.find("Slash")[0] !== undefined ||
				hitPart.Name.find("Air")[0] !== undefined ||
				Players.GetPlayerFromCharacter(hitPart.Parent as Model) !== undefined
			) {
				return;
			}

			// Create impact effect
			this.createAirBladeImpact(raycast.Position);

			// Remove air blade
			moveConnection.Disconnect();
			airBlade.Destroy();
			const index = this.swordEffects.indexOf(airBlade);
			if (index > -1) {
				this.swordEffects.remove(index);
			}
		}
	}

	private createAirBladeImpact(position: Vector3): void {
		print("💥 Air blade impact!");

		// Create impact explosion
		const impact = new Instance("Part");
		impact.Name = "AirBladeImpact";
		impact.Shape = Enum.PartType.Ball;
		impact.Size = new Vector3(3, 3, 3);
		impact.Color = Color3.fromRGB(255, 255, 255);
		impact.Material = Enum.Material.Neon;
		impact.Transparency = 0.3;
		impact.CanCollide = false;
		impact.Anchored = true;
		impact.Position = position;
		impact.Parent = Workspace;
		this.swordEffects.push(impact);

		// Impact expansion
		const impactTween = TweenService.Create(
			impact,
			new TweenInfo(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				Size: new Vector3(12, 12, 12),
				Transparency: 1,
			},
		);
		impactTween.Play();

		// Create impact sparkles
		this.createSlashImpact(position);

		// Remove impact after animation
		impactTween.Completed.Connect(() => {
			impact.Destroy();
			const index = this.swordEffects.indexOf(impact);
			if (index > -1) {
				this.swordEffects.remove(index);
			}
		});
	}

	private createEnvironmentalSwordEffects(): void {
		print("⚔️ Creating environmental sword effects");

		// Create sword atmosphere
		this.createSwordAtmosphere();

		// Create floating sword energy
		this.createFloatingSwordEnergy();

		// Create ground sword marks
		this.createGroundSwordMarks();
	}

	private createSwordAtmosphere(): void {
		// Change lighting to sharp metallic atmosphere
		const originalAmbient = Lighting.Ambient;
		const originalOutdoorAmbient = Lighting.OutdoorAmbient;
		const originalBrightness = Lighting.Brightness;

		const lightingTween = TweenService.Create(
			Lighting,
			new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				Ambient: Color3.fromRGB(150, 160, 200), // Cool metallic ambient
				OutdoorAmbient: Color3.fromRGB(170, 180, 220), // Cool metallic outdoor
				Brightness: 1.8, // Brighter for metallic shine
			},
		);
		lightingTween.Play();

		// Restore lighting after duration
		task.delay(3, () => {
			const restoreLightingTween = TweenService.Create(
				Lighting,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					Ambient: originalAmbient,
					OutdoorAmbient: originalOutdoorAmbient,
					Brightness: originalBrightness,
				},
			);
			restoreLightingTween.Play();
		});
	}

	private createFloatingSwordEnergy(): void {
		// Create floating sword energy orbs
		for (let i = 0; i < 8; i++) {
			task.delay(i * 0.2, () => {
				const randomPosition = new Vector3(math.random(-30, 30), math.random(5, 20), math.random(-30, 30));

				const swordEnergy = new Instance("Part");
				swordEnergy.Name = "FloatingSwordEnergy";
				swordEnergy.Shape = Enum.PartType.Block;
				swordEnergy.Size = new Vector3(0.5, 3, 0.5);
				swordEnergy.Color = Color3.fromRGB(200, 220, 255);
				swordEnergy.Material = Enum.Material.Neon;
				swordEnergy.Transparency = 0.4;
				swordEnergy.CanCollide = false;
				swordEnergy.Anchored = true;
				swordEnergy.Position = randomPosition;
				swordEnergy.Parent = Workspace;
				this.swordEffects.push(swordEnergy);

				// Add energy light
				const energyLight = new Instance("PointLight");
				energyLight.Color = Color3.fromRGB(200, 220, 255);
				energyLight.Brightness = 2;
				energyLight.Range = 10;
				energyLight.Parent = swordEnergy;

				// Floating and spinning animation
				const floatTween = TweenService.Create(
					swordEnergy,
					new TweenInfo(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
					{
						Position: randomPosition.add(new Vector3(0, 3, 0)),
					},
				);
				floatTween.Play();

				const spinTween = TweenService.Create(
					swordEnergy,
					new TweenInfo(1.5, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1),
					{
						CFrame: swordEnergy.CFrame.mul(CFrame.Angles(0, math.pi * 2, 0)),
					},
				);
				spinTween.Play();
			});
		}
	}

	private createGroundSwordMarks(): void {
		// Create sword slash marks on the ground
		for (let i = 0; i < 6; i++) {
			task.delay(i * 0.3, () => {
				const randomPosition = new Vector3(math.random(-25, 25), 0.1, math.random(-25, 25));

				const swordMark = new Instance("Part");
				swordMark.Name = "GroundSwordMark";
				swordMark.Shape = Enum.PartType.Block;
				swordMark.Size = new Vector3(math.random(8, 15), 0.2, 1);
				swordMark.Color = Color3.fromRGB(255, 255, 255);
				swordMark.Material = Enum.Material.Neon;
				swordMark.Transparency = 0.5;
				swordMark.CanCollide = false;
				swordMark.Anchored = true;
				swordMark.Position = randomPosition;
				swordMark.CFrame = swordMark.CFrame.mul(CFrame.Angles(0, math.random() * math.pi * 2, 0));
				swordMark.Parent = Workspace;
				this.swordEffects.push(swordMark);

				// Fading animation
				const fadeTween = TweenService.Create(
					swordMark,
					new TweenInfo(3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{ Transparency: 1 },
				);
				fadeTween.Play();
				fadeTween.Completed.Connect(() => {
					swordMark.Destroy();
					const index = this.swordEffects.indexOf(swordMark);
					if (index > -1) {
						this.swordEffects.remove(index);
					}
				});
			});
		}
	}

	private cleanupEffects(): void {
		print("🧹 Cleaning up Three Sword Style effects");

		// Clean up all sword effects
		for (const effect of this.swordEffects) {
			if (effect.Parent) {
				// Fade out effect
				const fadeTween = TweenService.Create(effect, new TweenInfo(1, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeTween.Play();
				fadeTween.Completed.Connect(() => effect.Destroy());
			}
		}
		this.swordEffects = [];

		// Disconnect any remaining connections
		if (this.swordConnection) {
			this.swordConnection.Disconnect();
			this.swordConnection = undefined;
		}
	}
}
