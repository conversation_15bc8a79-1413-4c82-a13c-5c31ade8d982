// Simple placeholder DropdownButton component
import * as React from "@rbxts/react";
import { COLORS } from "../../design";

export interface DropdownOption {
	id: string;
	text: string;
	icon?: string;
}

interface DropdownButtonProps {
	options: DropdownOption[];
	selectedOption?: DropdownOption;
	onOptionSelect: (option: DropdownOption) => void;
	placeholder?: string;
	size?: UDim2;
	layoutOrder?: number;
	disabled?: boolean;
}

export function DropdownButton(props: DropdownButtonProps): React.ReactElement {
	return (
		<frame Size={props.size ?? new UDim2(0, 150, 0, 40)} BackgroundTransparency={1} LayoutOrder={props.layoutOrder}>
			<textlabel
				Text={props.selectedOption?.text ?? props.placeholder ?? "Select..."}
				Size={new UDim2(1, 0, 1, 0)}
				BackgroundTransparency={1}
				TextColor3={Color3.fromHex(COLORS.text.main)}
			/>
		</frame>
	);
}
