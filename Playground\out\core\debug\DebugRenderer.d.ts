export * from "./types/DebugPrimitives";
export declare class DebugRenderer {
    private lines;
    private spheres;
    private texts;
    private debugFolder?;
    private guiFolder?;
    private isVisible;
    constructor();
    show(): void;
    hide(): void;
    drawLine(from: Vector3, to: Vector3, color?: Color3, duration?: number): void;
    drawSphere(position: Vector3, radius?: number, color?: Color3, duration?: number): void;
    drawText(position: Vector3, text: string, color?: Color3, duration?: number): void;
    render(): void;
    private setupDebugFolder;
    private setupGUI;
    private createLinePart;
    private createSpherePart;
    private create3DText;
    private unifiedDebugFrame?;
    private unifiedDebugLabels;
    createUnifiedDebugPanel(): void;
    addDebugSection(sectionName: string, text: string, layoutOrder: number): TextLabel | undefined;
    updateDebugSection(sectionName: string, text: string): void;
    setUnifiedDebugPosition(position: UDim2, size: UDim2): void;
    createGUIElement(name: string, position: UDim2, size: UDim2, text: string): TextLabel | undefined;
    updateGUIElement(name: string, text: string): void;
    drawPath(points: Vector3[], color?: Color3, duration?: number): void;
    drawVisionCone(position: Vector3, direction: Vector3, range: number, angle: number, color?: Color3, duration?: number): void;
    cleanup(): void;
}
