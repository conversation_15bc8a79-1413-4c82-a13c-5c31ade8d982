import * as React from "@rbxts/react";
export interface ToastData {
    id: string;
    title: string;
    message?: string;
    type: "success" | "error" | "warning" | "info";
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}
interface ToastProps {
    toast: ToastData;
    onDismiss: (id: string) => void;
    index: number;
}
export declare function Toast(props: ToastProps): React.ReactElement;
export {};
