import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
import { ClientGameState, ClientStateAction, ClientStateUpdater } from "./types/index";
export * from "./types/index";
declare class ClientStateManager {
    private static instance?;
    private state;
    private listeners;
    private constructor();
    static getInstance(): ClientStateManager;
    private getInitialState;
    getState(): ClientGameState;
    dispatch<T>(action: ClientStateAction<T>, updater: ClientStateUpdater<T>): Result<ClientGameState, Error>;
    subscribe(listener: (state: ClientGameState) => void): () => void;
    updateUI<K extends keyof ClientGameState["ui"]>(section: K, updates: Partial<ClientGameState["ui"][K]>): Result<ClientGameState, Error>;
    updatePlayer<K extends keyof ClientGameState["player"]>(field: K, value: ClientGameState["player"][K]): Result<ClientGameState, Error>;
    updateWorld<K extends keyof ClientGameState["world"]>(field: K, value: ClientGameState["world"][K]): Result<ClientGameState, Error>;
}
export declare const ClientState: ClientStateManager;
export declare function useClientState(): ClientGameState;
export declare function useUIState<K extends keyof ClientGameState["ui"]>(section: K): ClientGameState["ui"][K];
