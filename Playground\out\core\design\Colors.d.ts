export declare const COLORS: {
    text: {
        main: string;
        secondary: string;
        muted: string;
        inverse: string;
    };
    bg: {
        base: string;
        secondary: string;
        surface: string;
        "surface-hover": string;
        "surface-pressed": string;
        avatar: string;
        badge: string;
        hover: string;
        modal: string;
    };
    label: {
        text: string;
        focus: string;
        bg: string;
        border: string;
        muted: string;
        hover: string;
    };
    span: {
        default: string;
        muted: string;
        highlight: string;
        subtle: string;
        hover: string;
    };
    border: {
        base: string;
        l1: string;
        l2: string;
        l3: string;
        strong: string;
        focus: string;
        danger: string;
    };
    primary: string;
    "primary-dark": string;
    "primary-light": string;
    success: string;
    "success-dark": string;
    warning: string;
    "warning-dark": string;
    error: string;
    "error-dark": string;
    info: string;
    "info-dark": string;
    "progress-bg": string;
    "progress-fill": string;
    "account-active": string;
    "account-active-hover": string;
    badge: {
        bg: string;
        text: string;
        border: string;
    };
    ring: {
        "focus-accent": string;
    };
    shadow: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
    };
};
