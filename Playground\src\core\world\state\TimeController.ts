import { Lighting, TweenService } from "@rbxts/services";
import { TimeOptions, TimeOfDay } from "./interfaces/TimeOptions";
import { EffectTweenBuilder } from "../../effects/EffectTweenBuilder";

/**
 * TimeController - Manages day/night cycle and time of day
 * Provides simple methods for changing time with smooth transitions
 */
export class TimeController {
	private static instance: TimeController;
	private currentTimeOfDay: TimeOfDay = "noon";
	private activeTweens: Map<string, Tween> = new Map();

	private constructor() {}

	public static getInstance(): TimeController {
		if (!TimeController.instance) {
			TimeController.instance = new TimeController();
		}
		return TimeController.instance;
	}

	/**
	 * Set time of day with smooth transition
	 */
	public setTime(options: TimeOptions): void {
		this.currentTimeOfDay = options.timeOfDay;
		const transitionDuration = options.transitionDuration || 2;

		// Clear existing tweens
		this.clearActiveTweens();

		// Get target clock time
		const targetClockTime = options.clockTime || this.getClockTimeForTimeOfDay(options.timeOfDay);

		// Create smooth transition
		this.tweenToTime(targetClockTime, transitionDuration);
		this.tweenLightingForTimeOfDay(options.timeOfDay, transitionDuration);

		print(`🌅 Time changed to: ${options.timeOfDay} (${targetClockTime}:00)`);
	}

	/**
	 * Get current time of day
	 */
	public getCurrentTimeOfDay(): TimeOfDay {
		return this.currentTimeOfDay;
	}

	/**
	 * Get current clock time (0-24)
	 */
	public getCurrentClockTime(): number {
		return Lighting.ClockTime;
	}

	private getClockTimeForTimeOfDay(timeOfDay: TimeOfDay): number {
		switch (timeOfDay) {
			case "dawn":
				return 6;
			case "noon":
				return 12;
			case "dusk":
				return 18;
			case "night":
				return 0;
			default:
				return 12;
		}
	}

	private tweenToTime(targetTime: number, duration: number): void {
		// Use Core framework EffectTweenBuilder instead of raw TweenService
		const timeTween = EffectTweenBuilder.for(Lighting)
			.duration(duration)
			.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			.play();

		// Manually animate clock time since EffectTweenBuilder doesn't have clockTime method
		const startTime = Lighting.ClockTime;
		const startTick = tick();

		const animateClockTime = () => {
			const elapsed = tick() - startTick;
			const progress = math.min(elapsed / duration, 1);

			// Smooth interpolation
			const currentTime = startTime + (targetTime - startTime) * progress;
			Lighting.ClockTime = currentTime;

			if (progress < 1) {
				task.wait();
				animateClockTime();
			}
		};

		task.spawn(() => animateClockTime());
		this.activeTweens.set("clockTime", timeTween);
	}

	private tweenLightingForTimeOfDay(timeOfDay: TimeOfDay, duration: number): void {
		// Get target lighting values
		let targetLighting: {
			brightness: number;
			ambient: Color3;
			colorShiftBottom: Color3;
			colorShiftTop: Color3;
			fogColor: Color3;
		};

		switch (timeOfDay) {
			case "dawn":
				targetLighting = {
					brightness: 0.8,
					ambient: new Color3(0.8, 0.5, 0.3),
					colorShiftBottom: new Color3(1, 0.7, 0.4),
					colorShiftTop: new Color3(1, 0.8, 0.6),
					fogColor: new Color3(1, 0.8, 0.6),
				};
				break;
			case "noon":
				targetLighting = {
					brightness: 1,
					ambient: new Color3(0.5, 0.5, 0.5),
					colorShiftBottom: new Color3(0, 0, 0),
					colorShiftTop: new Color3(0, 0, 0),
					fogColor: new Color3(0.76, 0.76, 0.76),
				};
				break;
			case "dusk":
				targetLighting = {
					brightness: 0.6,
					ambient: new Color3(0.6, 0.4, 0.3),
					colorShiftBottom: new Color3(1, 0.5, 0.2),
					colorShiftTop: new Color3(0.8, 0.6, 0.4),
					fogColor: new Color3(0.9, 0.6, 0.4),
				};
				break;
			case "night":
				targetLighting = {
					brightness: 0.2,
					ambient: new Color3(0.1, 0.1, 0.3),
					colorShiftBottom: new Color3(0, 0, 0.2),
					colorShiftTop: new Color3(0, 0, 0.1),
					fogColor: new Color3(0.2, 0.2, 0.4),
				};
				break;
		}

		// Use Core framework EffectTweenBuilder for brightness
		const lightingTween = EffectTweenBuilder.for(Lighting)
			.brightness(targetLighting.brightness)
			.duration(duration)
			.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			.play();

		// Manually animate other lighting properties since EffectTweenBuilder doesn't have them
		const startValues = {
			ambient: Lighting.Ambient,
			colorShiftBottom: Lighting.ColorShift_Bottom,
			colorShiftTop: Lighting.ColorShift_Top,
			fogColor: Lighting.FogColor,
		};

		const startTick = tick();

		const animateLighting = () => {
			const elapsed = tick() - startTick;
			const progress = math.min(elapsed / duration, 1);

			// Smooth interpolation for colors
			Lighting.Ambient = startValues.ambient.Lerp(targetLighting.ambient, progress);
			Lighting.ColorShift_Bottom = startValues.colorShiftBottom.Lerp(targetLighting.colorShiftBottom, progress);
			Lighting.ColorShift_Top = startValues.colorShiftTop.Lerp(targetLighting.colorShiftTop, progress);
			Lighting.FogColor = startValues.fogColor.Lerp(targetLighting.fogColor, progress);

			if (progress < 1) {
				task.wait();
				animateLighting();
			}
		};

		task.spawn(() => animateLighting());
		this.activeTweens.set("lighting", lightingTween);
	}

	/**
	 * Start automatic day/night cycle
	 */
	public startDayNightCycle(cycleDuration: number = 240): void {
		// 4 minute full cycle by default (60 seconds per time period)
		const timePerPhase = cycleDuration / 4;

		const cycleSequence = ["dawn", "noon", "dusk", "night"] as TimeOfDay[];
		let currentIndex = 0;

		const nextPhase = () => {
			this.setTime({
				timeOfDay: cycleSequence[currentIndex],
				transitionDuration: timePerPhase * 0.1, // 10% of phase time for transition
			});

			currentIndex = (currentIndex + 1) % cycleSequence.size();

			task.wait(timePerPhase);
			nextPhase();
		};

		task.spawn(() => nextPhase());
		print(`🔄 Day/night cycle started (${cycleDuration}s full cycle)`);
	}

	/**
	 * Stop automatic day/night cycle
	 */
	public stopDayNightCycle(): void {
		this.clearActiveTweens();
		print("⏹️ Day/night cycle stopped");
	}

	private clearActiveTweens(): void {
		for (const [key, tween] of this.activeTweens) {
			if (tween) {
				tween.Cancel();
			}
		}
		this.activeTweens.clear();
	}

	/**
	 * Cleanup all time effects
	 */
	public cleanup(): void {
		this.clearActiveTweens();
		// Reset to default noon lighting
		this.setTime({ timeOfDay: "noon", transitionDuration: 0 });
	}
}
