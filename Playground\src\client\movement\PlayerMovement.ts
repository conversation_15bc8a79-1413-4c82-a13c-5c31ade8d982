import { Players, RunService, TweenService, UserInputService, Workspace } from "@rbxts/services";

export class PlayerMovement {
	private player = Players.LocalPlayer;
	private character?: Model;
	private humanoid?: Humanoid;
	private humanoidRootPart?: Part;
	private connections: RBXScriptConnection[] = [];

	constructor() {
		this.setup<PERSON><PERSON>cter();

		// Listen for character respawning
		this.player.CharacterAdded.Connect((character) => {
			this.setup<PERSON>haracter();
		});
	}

	private setupCharacter(): void {
		this.character = this.player.Character || this.player.CharacterAdded.Wait()[0];
		this.humanoid = this.character.WaitForChild("Humanoid") as Humanoid;
		this.humanoidRootPart = this.character.WaitForChild("HumanoidRootPart") as Part;

		print("✅ PlayerMovement setup complete");
	}

	/**
	 * Basic movement using Humanoid.WalkSpeed
	 */
	public setWalkSpeed(speed: number): void {
		if (this.humanoid) {
			this.humanoid.WalkSpeed = speed;
			print(`🚶 Walk speed set to: ${speed}`);
		}
	}

	/**
	 * Make the player jump
	 */
	public jump(): void {
		if (this.humanoid) {
			this.humanoid.Jump = true;
			print("🦘 Player jumped!");
		}
	}

	/**
	 * Move player to a specific position using Humanoid:MoveTo()
	 */
	public moveTo(position: Vector3): void {
		if (this.humanoid) {
			this.humanoid.MoveTo(position);
			print(`🎯 Moving to position: ${position}`);
		}
	}

	/**
	 * Dash in a direction using BodyVelocity (temporary speed boost)
	 */
	public dash(direction: Vector3, power: number = 50, duration: number = 0.3): void {
		if (!this.humanoidRootPart) return;

		// Create BodyVelocity for the dash
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(4000, 0, 4000); // Only horizontal movement
		bodyVelocity.Velocity = direction.Unit.mul(power);
		bodyVelocity.Parent = this.humanoidRootPart;

		// Remove the BodyVelocity after duration
		task.delay(duration, () => {
			if (bodyVelocity.Parent) {
				bodyVelocity.Destroy();
			}
		});

		print(`💨 Dashed in direction: ${direction} with power: ${power}`);
	}

	/**
	 * Launch player upward (like a rocket jump)
	 */
	public launch(upwardForce: number = 50, forwardForce: number = 0): void {
		if (!this.humanoidRootPart) return;

		const lookDirection = this.humanoidRootPart.CFrame.LookVector;
		const launchDirection = new Vector3(
			lookDirection.X * forwardForce,
			upwardForce,
			lookDirection.Z * forwardForce,
		);

		// Use AssemblyLinearVelocity for modern approach
		this.humanoidRootPart.AssemblyLinearVelocity = launchDirection;

		print(`🚀 Launched player with force: ${launchDirection}`);
	}

	/**
	 * Smooth movement to position using TweenService
	 */
	public smoothMoveTo(targetPosition: Vector3, duration: number = 2): void {
		if (!this.humanoidRootPart) return;

		const tween = TweenService.Create(
			this.humanoidRootPart,
			new TweenInfo(duration, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{ CFrame: new CFrame(targetPosition) },
		);

		tween.Play();
		print(`🌊 Smoothly moving to: ${targetPosition} over ${duration} seconds`);
	}

	/**
	 * Teleport player instantly to position
	 */
	public teleport(position: Vector3): void {
		if (!this.humanoidRootPart) return;

		this.humanoidRootPart.CFrame = new CFrame(position);
		print(`⚡ Teleported to: ${position}`);
	}

	/**
	 * Enable/disable player movement
	 */
	public setMovementEnabled(enabled: boolean): void {
		if (this.humanoid) {
			this.humanoid.PlatformStand = !enabled;
			print(`🔒 Movement ${enabled ? "enabled" : "disabled"}`);
		}
	}

	/**
	 * Get current player velocity
	 */
	public getVelocity(): Vector3 {
		if (this.humanoidRootPart) {
			return this.humanoidRootPart.AssemblyLinearVelocity;
		}
		return new Vector3(0, 0, 0);
	}

	/**
	 * Check if player is moving
	 */
	public isMoving(): boolean {
		const velocity = this.getVelocity();
		const horizontalSpeed = math.sqrt(velocity.X * velocity.X + velocity.Z * velocity.Z);
		return horizontalSpeed > 1; // Consider moving if horizontal speed > 1
	}

	/**
	 * Setup WASD movement override (custom movement system)
	 */
	public setupCustomMovement(speed: number = 16): void {
		// Disable default movement
		if (this.humanoid) {
			this.humanoid.PlatformStand = true;
		}

		// Custom movement with WASD
		const connection = RunService.Heartbeat.Connect(() => {
			if (!this.humanoidRootPart) return;

			const camera = Workspace.CurrentCamera;
			if (!camera) return;

			let moveVector = new Vector3(0, 0, 0);

			// Check input
			if (UserInputService.IsKeyDown(Enum.KeyCode.W)) {
				moveVector = moveVector.add(camera.CFrame.LookVector);
			}
			if (UserInputService.IsKeyDown(Enum.KeyCode.S)) {
				moveVector = moveVector.sub(camera.CFrame.LookVector);
			}
			if (UserInputService.IsKeyDown(Enum.KeyCode.A)) {
				moveVector = moveVector.sub(camera.CFrame.RightVector);
			}
			if (UserInputService.IsKeyDown(Enum.KeyCode.D)) {
				moveVector = moveVector.add(camera.CFrame.RightVector);
			}

			// Apply movement
			if (moveVector.Magnitude > 0) {
				const normalizedMove = moveVector.Unit;
				this.humanoidRootPart.AssemblyLinearVelocity = new Vector3(
					normalizedMove.X * speed,
					this.humanoidRootPart.AssemblyLinearVelocity.Y, // Preserve Y velocity
					normalizedMove.Z * speed,
				);
			} else {
				// Stop horizontal movement when no input
				this.humanoidRootPart.AssemblyLinearVelocity = new Vector3(
					0,
					this.humanoidRootPart.AssemblyLinearVelocity.Y,
					0,
				);
			}
		});

		this.connections.push(connection);
		print("🎮 Custom WASD movement enabled");
	}

	/**
	 * Restore default Roblox movement
	 */
	public restoreDefaultMovement(): void {
		if (this.humanoid) {
			this.humanoid.PlatformStand = false;
		}

		// Disconnect custom movement connections
		for (const connection of this.connections) {
			connection.Disconnect();
		}
		this.connections = [];

		print("🔄 Default movement restored");
	}

	/**
	 * Cleanup when done
	 */
	public destroy(): void {
		this.restoreDefaultMovement();
	}
}
