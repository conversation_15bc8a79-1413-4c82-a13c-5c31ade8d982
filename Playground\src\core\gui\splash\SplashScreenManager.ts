import { RunService } from "@rbxts/services";

export interface LoadingTask {
	name: string;
	weight: number; // Relative weight for progress calculation
	task: () => Promise<void> | void;
}

export interface SplashScreenState {
	isVisible: boolean;
	loadingProgress: number;
	loadingText: string;
}

export class SplashScreenManager {
	private static instance?: SplashScreenManager;
	private loadingTasks: LoadingTask[] = [];
	private currentProgress = 0;
	private currentText = "Initializing...";
	private isVisible = false;
	private onStateChange?: (state: SplashScreenState) => void;

	private constructor() {}

	public static getInstance(): SplashScreenManager {
		if (!this.instance) {
			this.instance = new SplashScreenManager();
		}
		return this.instance;
	}

	/**
	 * Add a loading task to be executed during splash screen
	 */
	public addLoadingTask(task: LoadingTask): void {
		this.loadingTasks.push(task);
	}

	/**
	 * Add multiple loading tasks
	 */
	public addLoadingTasks(tasks: LoadingTask[]): void {
		for (const task of tasks) {
			this.loadingTasks.push(task);
		}
	}

	/**
	 * Set the state change callback
	 */
	public onStateChanged(callback: (state: SplashScreenState) => void): void {
		this.onStateChange = callback;
	}

	/**
	 * Start the loading process
	 */
	public async startLoading(): Promise<void> {
		this.isVisible = true;
		this.currentProgress = 0;
		this.currentText = "Initializing Core Framework...";
		this.notifyStateChange();

		const totalWeight = this.loadingTasks.reduce((sum, task) => sum + task.weight, 0);
		let completedWeight = 0;

		for (const task of this.loadingTasks) {
			this.currentText = task.name;
			this.notifyStateChange();

			try {
				// Execute the task
				const result = task.task();
				if (result !== undefined) {
					await result;
				}

				// Update progress
				completedWeight += task.weight;
				this.currentProgress = completedWeight / totalWeight;
				this.notifyStateChange();

				// Small delay for visual feedback
				await this.delay(0.1);
			} catch (error) {
				print(`❌ Loading task failed: ${task.name} - ${error}`);
				this.currentText = `Error loading: ${task.name}`;
				this.notifyStateChange();
				await this.delay(1); // Show error for a moment
			}
		}

		// Ensure we reach 100%
		this.currentProgress = 1;
		this.currentText = "Loading Complete!";
		this.notifyStateChange();

		// Wait a moment before hiding
		await this.delay(0.5);
	}

	/**
	 * Hide the splash screen
	 */
	public hide(): void {
		this.isVisible = false;
		this.notifyStateChange();
	}

	/**
	 * Get current state
	 */
	public getState(): SplashScreenState {
		return {
			isVisible: this.isVisible,
			loadingProgress: this.currentProgress,
			loadingText: this.currentText,
		};
	}

	/**
	 * Setup default core framework loading tasks
	 */
	public setupDefaultCoreTasks(): void {
		this.addLoadingTasks([
			{
				name: "Initializing Physics System...",
				weight: 1,
				task: async () => {
					// Import and initialize physics
					const { PhysicsImpactHelper } = await import("../../world/physics/PhysicsImpactHelper");
					PhysicsImpactHelper.initialize();
				},
			},
			{
				name: "Loading Weather System...",
				weight: 1,
				task: async () => {
					// Import weather systems
					await import("../../world/state/WeatherController");
					await import("../../effects/WeatherParticleHelper");
					await import("../../effects/WeatherSoundHelper");
				},
			},
			{
				name: "Initializing UI Framework...",
				weight: 1,
				task: async () => {
					// Import UI components
					await import("../layout/ZIndexManager");
					const { ResponsiveManager } = await import("../layout/ResponsiveManager");
					ResponsiveManager.getInstance();
				},
			},
			{
				name: "Loading Sound System...",
				weight: 1,
				task: async () => {
					// Import sound helpers
					await import("../../effects/SoundHelper");
				},
			},
			{
				name: "Initializing Animation System...",
				weight: 1,
				task: async () => {
					// Import animation systems
					await import("../../animations/AnimationBuilder");
					await import("../../animations/LimbAnimator");
				},
			},
			{
				name: "Loading Entity Management...",
				weight: 1,
				task: async () => {
					// Import entity systems
					await import("../../entities/EntityManager");
				},
			},
			{
				name: "Finalizing Core Framework...",
				weight: 1,
				task: async () => {
					// Final initialization
					print("✅ Core Framework loaded successfully!");
				},
			},
		]);
	}

	private notifyStateChange(): void {
		if (this.onStateChange) {
			this.onStateChange(this.getState());
		}
	}

	private delay(seconds: number): Promise<void> {
		return new Promise((resolve) => {
			task.delay(seconds, resolve);
		});
	}
}
