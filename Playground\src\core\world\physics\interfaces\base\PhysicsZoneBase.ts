export type ObjectType = "players" | "parts" | "debris" | "projectiles" | "npcs";

export interface PhysicsZoneOptions {
	center: Vector3;
	radius: number;
	zoneType: "gravity" | "force" | "barrier" | "teleport" | "slow" | "accelerate";
	intensity: number; // 0.1 to 2.0 (multiplier)
	duration?: number; // seconds, undefined = permanent
	affectedObjects: ObjectType[];
	visualEffect?: boolean;
	soundEffect?: string;
}

export interface PhysicsZone {
	id: string;
	options: PhysicsZoneOptions;
	createdAt: number;
	expiresAt?: number;
	affectedObjects: Set<Instance>;
	zoneVisual?: Part;
}

export interface ForceApplication {
	target: Instance;
	forceType: "BodyVelocity" | "BodyPosition" | "BodyAngularVelocity";
	forceObject: Instance;
	originalValues?: Map<string, unknown>;
}
