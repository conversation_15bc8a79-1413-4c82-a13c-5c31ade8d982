import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error, createError } from "../foundation/types/RobloxError";
import { StateError } from "./errors/StateError";
import { StateSubscription } from "./interfaces/StateSubscription";
import { StateAction } from "./interfaces/StateAction";
import { StateMiddleware } from "./interfaces/StateMiddleware";

export class StateManager<TState> extends BaseService {
	private currentState: TState;
	private subscriptions = new Map<string, StateSubscription<TState>>();
	private middlewares: StateMiddleware<TState>[] = [];
	private actionHistory: StateAction<TState>[] = [];
	private maxHistorySize = 100;

	constructor(initialState: TState, name: string = "StateManager") {
		super(name);
		this.currentState = initialState;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		this.logInfo(`State manager initialized with initial state`);
		return Result.ok(undefined);
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.subscriptions.clear();
		this.middlewares = [];
		this.actionHistory = [];
		return Result.ok(undefined);
	}

	public getState(): TState {
		return this.deepClone(this.currentState);
	}

	public dispatch<TPayload>(action: StateAction<TState, TPayload>): Result<TState, StateError> {
		const initResult = this.ensureInitialized();
		if (initResult.isError()) {
			return Result.err(new StateError(initResult.getError().message));
		}

		try {
			// Apply middlewares
			let processedAction = action;
			for (const middleware of this.middlewares) {
				const middlewareResult = middleware.process(processedAction, this.currentState);
				if (middlewareResult.isError()) {
					return Result.err(new StateError(`Middleware error: ${middlewareResult.getError().message}`));
				}
				processedAction = middlewareResult.getValue();
			}

			// Apply the action
			const previousState = this.deepClone(this.currentState);
			const newState = processedAction.reducer(this.currentState, processedAction.payload);

			// Validate new state
			if (processedAction.validator) {
				const validationResult = processedAction.validator(newState);
				if (validationResult.isError()) {
					return Result.err(
						new StateError(`State validation failed: ${validationResult.getError().message}`),
					);
				}
			}

			// Update state
			this.currentState = newState;

			// Add to history
			this.addToHistory(processedAction as StateAction<TState, unknown>);

			// Notify subscribers
			this.notifySubscribers(previousState, newState);

			this.logDebug(`Action dispatched: ${processedAction.type}`);
			return Result.ok(this.deepClone(newState));
		} catch (error) {
			return Result.err(new StateError(`Failed to dispatch action: ${error}`));
		}
	}

	public subscribe(
		id: string,
		callback: (previousState: TState, newState: TState) => void,
		selector?: (state: TState) => unknown,
	): Result<() => void, StateError> {
		const initResult = this.ensureInitialized();
		if (initResult.isError()) {
			return Result.err(new StateError(initResult.getError().message));
		}

		if (this.subscriptions.has(id)) {
			return Result.err(new StateError(`Subscription with ID '${id}' already exists`));
		}

		const subscription: StateSubscription<TState> = {
			id,
			callback,
			selector,
			lastSelectedValue: selector ? selector(this.currentState) : undefined,
		};

		this.subscriptions.set(id, subscription);

		// Return unsubscribe function
		const unsubscribe = () => {
			this.subscriptions.delete(id);
			this.logDebug(`Unsubscribed: ${id}`);
		};

		this.logDebug(`Subscribed: ${id}`);
		return Result.ok(unsubscribe);
	}

	public addMiddleware(middleware: StateMiddleware<TState>): Result<void, StateError> {
		const initResult = this.ensureInitialized();
		if (initResult.isError()) {
			return Result.err(new StateError(initResult.getError().message));
		}

		this.middlewares.push(middleware);
		this.logDebug(`Middleware added: ${middleware.name}`);
		return Result.ok(undefined);
	}

	public getActionHistory(): StateAction<TState>[] {
		return [...this.actionHistory];
	}

	public canUndo(): boolean {
		return this.actionHistory.size() > 0;
	}

	public undo(): Result<TState, StateError> {
		if (!this.canUndo()) {
			return Result.err(new StateError("No actions to undo"));
		}

		const lastAction = this.actionHistory.pop()!;
		if (lastAction.undoReducer) {
			try {
				const newState = lastAction.undoReducer(this.currentState);
				const previousState = this.deepClone(this.currentState);
				this.currentState = newState;

				this.notifySubscribers(previousState, newState);
				this.logDebug(`Undid action: ${lastAction.type}`);
				return Result.ok(this.deepClone(newState));
			} catch (error) {
				return Result.err(new StateError(`Failed to undo action: ${error}`));
			}
		} else {
			return Result.err(new StateError(`Action '${lastAction.type}' is not undoable`));
		}
	}

	private notifySubscribers(previousState: TState, newState: TState): void {
		for (const [, subscription] of this.subscriptions) {
			try {
				if (subscription.selector) {
					const newSelectedValue = subscription.selector(newState);
					if (newSelectedValue !== subscription.lastSelectedValue) {
						subscription.lastSelectedValue = newSelectedValue;
						subscription.callback(previousState, newState);
					}
				} else {
					subscription.callback(previousState, newState);
				}
			} catch (error) {
				this.logError(`Subscription callback error for ${subscription.id}: ${error}`);
			}
		}
	}

	private addToHistory(action: StateAction<TState>): void {
		if (action.undoReducer) {
			this.actionHistory.push(action);

			// Limit history size
			if (this.actionHistory.size() > this.maxHistorySize) {
				this.actionHistory.shift();
			}
		}
	}

	private deepClone<T>(obj: T): T {
		if (obj === undefined || typeOf(obj) !== "table") {
			return obj;
		}

		const cloned = {} as T;
		for (const [key, value] of pairs(obj as Record<string, unknown>)) {
			(cloned as Record<string, unknown>)[key] = this.deepClone(value);
		}

		return cloned;
	}
}
