-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local ContainerFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame").ContainerFrame
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function Grid(props)
	-- Use larger cells for buttons, smaller for labels
	local isButtonGrid = props.cellType == "button"
	local responsiveManager = ResponsiveManager:getInstance()
	-- Calculate responsive cell sizes
	local cellWidth
	local cellHeight
	if props.responsive then
		local screenSize = responsiveManager:getScreenSize()
		local deviceType = responsiveManager:getDeviceType()
		-- Base sizes adjusted for device type
		local baseCellWidth = if isButtonGrid then 80 else SIZES.gridCell.width
		local baseCellHeight = if isButtonGrid then 50 else SIZES.gridCell.height
		-- Scale based on device type
		local scaleFactor = if deviceType == "mobile" then 0.8 elseif deviceType == "tablet" then 0.9 else 1.0
		cellWidth = baseCellWidth * scaleFactor
		cellHeight = baseCellHeight * scaleFactor
		-- Ensure grid doesn't exceed max width if specified
		local _value = props.maxWidth
		if _value ~= 0 and _value == _value and _value then
			local maxCellWidth = (props.maxWidth - 5 * (props.cols - 1)) / props.cols
			cellWidth = math.min(cellWidth, maxCellWidth)
		end
	else
		cellWidth = if isButtonGrid then 80 else SIZES.gridCell.width
		cellHeight = if isButtonGrid then 50 else SIZES.gridCell.height
	end
	local cellPadding = responsiveManager:getResponsiveMargin(5)
	local gridWidth = cellWidth * props.cols + cellPadding * (props.cols - 1)
	local gridHeight = cellHeight * props.rows + cellPadding * (props.rows - 1)
	local _exp = React.createElement("uigridlayout", {
		CellPadding = UDim2.new(0, cellPadding, 0, cellPadding),
		CellSize = UDim2.new(0, cellWidth, 0, cellHeight),
		SortOrder = Enum.SortOrder.LayoutOrder,
	})
	local _result = props.children
	if _result ~= nil then
		-- ▼ ReadonlyArray.map ▼
		local _newValue = table.create(#_result)
		local _callback = function(child, index)
			return React.createElement(ContainerFrame, {
				key = `cell-{index}`,
				backgroundColor = if isButtonGrid then "transparent" else COLORS.bg.base,
				backgroundTransparency = if isButtonGrid then 1 else 0,
				cornerRadius = if isButtonGrid then 0 else BORDER_RADIUS.sm,
				borderColor = COLORS.border.l2,
				borderThickness = if isButtonGrid then 0 else 1,
				borderTransparency = 0.2,
				padding = if isButtonGrid then 0 else 2,
				layoutOrder = index,
			}, child)
		end
		for _k, _v in _result do
			_newValue[_k] = _callback(_v, _k - 1, _result)
		end
		-- ▲ ReadonlyArray.map ▲
		_result = _newValue
	end
	return React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(0, gridWidth, 0, gridHeight),
		padding = 0,
		borderThickness = 0,
		layoutOrder = props.layoutOrder,
	}, _exp, _result)
end
return {
	Grid = Grid,
}
