// Add immediate debug output to verify script is running
print("🚀 [SERVER] main.server.ts is loading...");
print("📦 [SERVER] Importing modules...");

import { makeHello } from "../shared/module";
import { initializeCoreFramework, Core } from "../core";
import { WhitebeardAbilityServer } from "./abilities/WhitebeardAbilityServer";
import { WorldTestingServer } from "./world/WorldTestingServer";
import { ServerDataStoreService } from "./data/DataStoreService";
import { ReplicatedStorage } from "@rbxts/services";

print("✅ [SERVER] All modules imported successfully");

// Initialize the new enterprise-grade Core Framework
async function initializeServer() {
	print("🚀 [SERVER] Starting server initialization...");
	print("🚀 [SERVER] Initializing server with enterprise Core Framework...");

	try {
		const initResult = await initializeCoreFramework();
		if (initResult.isError()) {
			error(`[SERVER] Failed to initialize Core Framework: ${initResult.getError().message}`);
			return;
		}

		print("✅ [SERVER] Core Framework initialized successfully!");

		// Initialize game-specific systems
		print("🔧 [SERVER] Initializing WhitebeardAbilityServer...");
		const whitebeardServer = new WhitebeardAbilityServer();

		print("🔧 [SERVER] Initializing WorldTestingServer...");
		const worldTestingServer = new WorldTestingServer();

		print("🔧 [SERVER] Initializing DataStoreService...");
		const dataStoreService = ServerDataStoreService.getInstance();

		print("🎮 [SERVER] Game server ready!");
		print("📡 [SERVER] RemoteEvents should now be available to clients");

		// Create a server initialization indicator
		const serverIndicator = new Instance("BoolValue");
		serverIndicator.Name = "CoreServerInitialized";
		serverIndicator.Value = true;
		serverIndicator.Parent = ReplicatedStorage;
		print("🚩 [SERVER] Created server initialization indicator in ReplicatedStorage");
	} catch (err) {
		error(`[SERVER] Server initialization error: ${err}`);
	}
}

// Start server initialization
print("🚀 [SERVER] About to call initializeServer()...");
initializeServer().then(
	() => {
		print("✅ [SERVER] Server initialization completed successfully!");
	},
	(err) => {
		error(`[SERVER] Server initialization failed: ${err}`);
	},
);

print("🔥 [SERVER] Playground server script executed! Starting initialization...");
