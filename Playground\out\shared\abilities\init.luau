-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Core ability types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "TS", "abilities", "types", "AbilityType") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "TS", "abilities", "types", "Ability") or {} do
	exports[_k] = _v
end
-- Networking types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "TS", "abilities", "networking", "AbilityNetworkTypes") or {} do
	exports[_k] = _v
end
-- Ability definitions
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "TS", "abilities", "definitions", "AbilityDefinitions") or {} do
	exports[_k] = _v
end
return exports
