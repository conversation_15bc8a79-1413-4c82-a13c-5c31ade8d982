-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local ConfigurationScope = TS.import(script, game:GetService("ReplicatedStorage"), "core", "config", "types", "ConfigurationTypes").ConfigurationScope
local ConfigurationService
do
	local super = BaseService
	ConfigurationService = setmetatable({}, {
		__tostring = function()
			return "ConfigurationService"
		end,
		__index = super,
	})
	ConfigurationService.__index = ConfigurationService
	function ConfigurationService.new(...)
		local self = setmetatable({}, ConfigurationService)
		return self:constructor(...) or self
	end
	function ConfigurationService:constructor()
		super.constructor(self, "ConfigurationService")
		self.config = {}
		self.validationRules = {}
		self:setupDefaultConfiguration()
	end
	ConfigurationService.onInitialize = TS.async(function(self)
		print("🔧 ConfigurationService: Initializing...")
		return Result:ok(nil)
	end)
	ConfigurationService.onShutdown = TS.async(function(self)
		table.clear(self.config)
		table.clear(self.validationRules)
		return Result:ok(nil)
	end)
	function ConfigurationService:get(key, defaultValue)
		local _config = self.config
		local _key = key
		local entry = _config[_key]
		if not entry then
			if defaultValue ~= nil then
				return Result:ok(defaultValue)
			end
			return Result:err(createError(`Configuration key '{key}' not found`))
		end
		return Result:ok(entry.value)
	end
	function ConfigurationService:set(key, value)
		local _config = self.config
		local _key = key
		local existingEntry = _config[_key]
		local _result = existingEntry
		if _result ~= nil then
			_result = _result.isReadOnly
		end
		if _result then
			return Result:err(createError(`Configuration key '{key}' is read-only`))
		end
		-- Validate the value if rules exist
		local _validationRules = self.validationRules
		local _key_1 = key
		local rule = _validationRules[_key_1]
		if rule then
			local validationResult = self:validateValue(value, rule)
			if validationResult:isError() then
				return validationResult
			end
		end
		local entry = {
			key = key,
			value = value,
			scope = ConfigurationScope.RUNTIME,
			timestamp = tick(),
		}
		local _config_1 = self.config
		local _key_2 = key
		_config_1[_key_2] = entry
		return Result:ok(nil)
	end
	function ConfigurationService:has(key)
		local _config = self.config
		local _key = key
		return _config[_key] ~= nil
	end
	function ConfigurationService:remove(key)
		local _config = self.config
		local _key = key
		local entry = _config[_key]
		local _result = entry
		if _result ~= nil then
			_result = _result.isReadOnly
		end
		if _result then
			return Result:err(createError(`Configuration key '{key}' is read-only`))
		end
		local _config_1 = self.config
		local _key_1 = key
		_config_1[_key_1] = nil
		return Result:ok(nil)
	end
	function ConfigurationService:getAll()
		local result = {}
		for key, entry in self.config do
			result[key] = entry.value
		end
		return result
	end
	function ConfigurationService:clear()
		-- Only clear non-read-only entries
		for key, entry in self.config do
			if not entry.isReadOnly then
				self.config[key] = nil
			end
		end
	end
	function ConfigurationService:addValidationRule(key, rule)
		local _validationRules = self.validationRules
		local _key = key
		local _rule = rule
		_validationRules[_key] = _rule
	end
	function ConfigurationService:validateValue(value, rule)
		if rule.required and value == nil then
			local _condition = rule.errorMessage
			if not (_condition ~= "" and _condition) then
				_condition = `Value is required for configuration key`
			end
			return Result:err(createError(_condition))
		end
		if rule.validator and not rule.validator(value) then
			local _condition = rule.errorMessage
			if not (_condition ~= "" and _condition) then
				_condition = `Validation failed for configuration value`
			end
			return Result:err(createError(_condition))
		end
		-- Type-specific validation
		local _condition = rule.type == "number"
		if _condition then
			local _value = value
			_condition = typeof(_value) == "number"
		end
		if _condition then
			local numberValue = value
			if rule.min ~= nil and numberValue < rule.min then
				return Result:err(createError(`Value must be at least {rule.min}`))
			end
			if rule.max ~= nil and numberValue > rule.max then
				return Result:err(createError(`Value must be at most {rule.max}`))
			end
		end
		return Result:ok(nil)
	end
	function ConfigurationService:setupDefaultConfiguration()
		-- Core system defaults
		self:setReadOnlyConfig("system.version", "1.0.0", "System version")
		self:setReadOnlyConfig("system.environment", "development", "Runtime environment")
		-- Game defaults
		self:setConfig("game.debug.enabled", true, "Enable debug mode")
		self:setConfig("game.physics.gravity", 196.2, "World gravity")
		self:setConfig("network.timeout", 30, "Network timeout in seconds")
		self:setConfig("ui.animations.enabled", true, "Enable UI animations")
	end
	function ConfigurationService:setConfig(key, value, description)
		local entry = {
			key = key,
			value = value,
			scope = ConfigurationScope.GLOBAL,
			timestamp = tick(),
			description = description,
		}
		local _config = self.config
		local _key = key
		_config[_key] = entry
	end
	function ConfigurationService:setReadOnlyConfig(key, value, description)
		local entry = {
			key = key,
			value = value,
			scope = ConfigurationScope.GLOBAL,
			timestamp = tick(),
			description = description,
			isReadOnly = true,
		}
		local _config = self.config
		local _key = key
		_config[_key] = entry
	end
end
return {
	ConfigurationService = ConfigurationService,
}
