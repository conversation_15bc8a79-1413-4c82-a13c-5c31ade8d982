import { TweenService, RunService, Workspace, Players } from "@rbxts/services";
import { CrackEffectHelper } from "../../../../core";

export function createAirCracks(position: Vector3): void {
	print(`🔥 Creating ${8} air cracks at position: ${position}`);

	// Use Core framework CrackEffectHelper - exact same behavior
	CrackEffectHelper.createAirCracks(position, 8);
}

export function createEnhancedAirCracks(position: Vector3, direction: Vector3): void {
	print(`🔥 Creating ENHANCED Gura Gura air cracks at position: ${position}, direction: ${direction}`);

	// Create massive impact flash first (temporarily disabled to debug black square)
	// this.createMassiveImpactFlash(position);

	// Create enhanced glass crack system (main feature)
	createEnhancedGlassCracks(position, direction);

	// Create atmospheric distortion waves (temporarily disabled to debug black square)
	// this.createAtmosphericDistortion(position, direction);

	// Create reduced energy lightning around cracks
	createEnergyLightning(position);

	// Create floating energy particles (temporarily disabled to debug black square)
	// this.createEnergyParticleStorm(position);

	// Create reality shatter effect (disabled - was causing black box)
	// this.createRealityShatter(position, direction);

	// Create flying objects effect (was missing!)
	createFlyingObjects(position);

	// ✨ NEW: Create spectacular sparkling effects for the cracks
	createCrackSparklingEffects(position, direction);

	print("✅ ENHANCED Gura Gura air cracks created with MASSIVE effects including flying objects and sparkling");
}

export function createEnhancedGlassCracks(position: Vector3, direction: Vector3): void {
	// Create stunning realistic glass crack pattern like punching through air
	print(`💎 Creating enhanced glass crack system at ${position}`);

	// Use Core framework CrackEffectHelper for glass cracks - exact same behavior
	CrackEffectHelper.createGlassCracks(position, direction, 8, 12);

	// Create floating glass fragments (game-specific effect)
	createGlassFragments(position, direction);

	print("✅ Enhanced glass crack system created");
}

export function createGlassImpactCenter(position: Vector3): void {
	// Create bright impact center like hitting glass
	const impact = new Instance("Part");
	impact.Name = "GlassImpactCenter";
	impact.Shape = Enum.PartType.Ball;
	impact.Size = new Vector3(2, 2, 2);
	impact.Color = Color3.fromRGB(255, 255, 255);
	impact.Material = Enum.Material.Neon;
	impact.Transparency = 0;
	impact.CanCollide = false;
	impact.Anchored = true;
	impact.Position = position;
	impact.Parent = Workspace;

	// Bright white light
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(255, 255, 255);
	light.Range = 30;
	light.Brightness = 20;
	light.Parent = impact;

	// Expand and fade quickly
	const expandTween = TweenService.Create(
		impact,
		new TweenInfo(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{ Size: new Vector3(8, 8, 8), Transparency: 1 },
	);
	expandTween.Play();
	expandTween.Completed.Connect(() => impact.Destroy());
}

export function createMainGlassCracks(position: Vector3, direction: Vector3): void {
	// Create main radiating glass cracks
	const numMainCracks = 8;

	for (let i = 0; i < numMainCracks; i++) {
		const angle = (i / numMainCracks) * math.pi * 2;
		const delay = i * 0.02; // Fast propagation

		task.delay(delay, () => {
			createEnhancedGlassCrackLine(position, angle, 15, true);
		});
	}
}

export function createEnhancedGlassCrackLine(
	startPoint: Vector3,
	angle: number,
	maxLength: number,
	isMainCrack: boolean,
): void {
	const segments = isMainCrack ? 12 : 6;
	const segmentLength = maxLength / segments;

	for (let seg = 0; seg < segments; seg++) {
		const segmentDelay = seg * 0.01; // Very fast propagation

		task.delay(segmentDelay, () => {
			const crack = new Instance("Part");
			crack.Name = `EnhancedGlassCrack_${isMainCrack ? "Main" : "Secondary"}_${seg}`;
			crack.Shape = Enum.PartType.Block;

			// Realistic glass crack appearance
			const thickness = isMainCrack ? 0.05 : 0.03;
			const length = segmentLength + math.random(-0.2, 0.2);
			const width = isMainCrack ? 0.08 : 0.05;
			crack.Size = new Vector3(thickness, width, length);

			// Bright glass-like appearance
			crack.Color = Color3.fromRGB(240, 250, 255);
			crack.Material = Enum.Material.Neon; // Neon for better visibility
			crack.Transparency = 0.1; // Slightly transparent
			crack.CanCollide = false;
			crack.Anchored = true;
			crack.Parent = Workspace;

			// Position along crack line with natural variation
			const deviation = math.random(-0.2, 0.2);
			const heightVariation = math.random(-1, 1);
			const distance = seg * segmentLength + segmentLength * 0.5;

			const crackPos = startPoint.add(
				new Vector3(
					math.cos(angle + deviation) * distance,
					math.sin(angle + deviation) * distance + heightVariation,
					math.random(-0.3, 0.3),
				),
			);

			crack.Position = crackPos;
			crack.CFrame = CFrame.lookAt(crackPos, crackPos.add(new Vector3(math.cos(angle), math.sin(angle), 0)));

			// Bright glow for main cracks
			if (isMainCrack) {
				const light = new Instance("PointLight");
				light.Color = Color3.fromRGB(200, 230, 255);
				light.Range = 2;
				light.Brightness = 3;
				light.Parent = crack;
			}

			// Flash into existence
			crack.Transparency = 1;
			const flashTween = TweenService.Create(crack, new TweenInfo(0.02, Enum.EasingStyle.Quad), {
				Transparency: 0.1,
			});
			flashTween.Play();

			// Fade out after time
			task.delay(5 + math.random(2, 4), () => {
				const fadeOut = TweenService.Create(crack, new TweenInfo(2, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeOut.Play();
				fadeOut.Completed.Connect(() => crack.Destroy());
			});
		});
	}
}

export function createEnhancedGlassWebPattern(position: Vector3): void {
	// Create secondary connecting cracks for web pattern
	const numWebCracks = 12;

	for (let i = 0; i < numWebCracks; i++) {
		const delay = 0.2 + i * 0.03; // Start after main cracks

		task.delay(delay, () => {
			const angle = math.random(0, math.pi * 2);
			const startRadius = math.random(3, 8);
			const startPoint = position.add(
				new Vector3(math.cos(angle) * startRadius, math.sin(angle) * startRadius, math.random(-1, 1)),
			);

			createEnhancedGlassCrackLine(startPoint, angle + math.random(-0.5, 0.5), 8, false);
		});
	}
}

export function createGlassFragments(position: Vector3, direction: Vector3): void {
	// Create floating glass fragments
	const numFragments = 15;

	for (let i = 0; i < numFragments; i++) {
		const fragment = new Instance("Part");
		fragment.Name = `GlassFragment_${i}`;
		fragment.Shape = Enum.PartType.Block;
		fragment.Size = new Vector3(math.random(0.2, 0.6), math.random(0.2, 0.6), math.random(0.05, 0.1));
		fragment.Color = Color3.fromRGB(240, 248, 255);
		fragment.Material = Enum.Material.Glass;
		fragment.Transparency = 0.2;
		fragment.CanCollide = false;
		fragment.Anchored = false;
		fragment.Parent = Workspace;

		// Position around impact point
		const angle = (i / numFragments) * math.pi * 2;
		const radius = math.random(2, 8);
		fragment.Position = position.add(
			new Vector3(math.cos(angle) * radius, math.sin(angle) * radius + math.random(1, 4), math.random(-1, 1)),
		);

		// Apply gentle floating motion
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(2000, 2000, 2000);
		bodyVelocity.Velocity = new Vector3(math.random(-5, 5), math.random(-3, 8), math.random(-5, 5));
		bodyVelocity.Parent = fragment;

		// Gentle rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(500, 500, 500);
		bodyAngularVelocity.AngularVelocity = new Vector3(math.random(-2, 2), math.random(-2, 2), math.random(-2, 2));
		bodyAngularVelocity.Parent = fragment;

		// Remove forces and fade out
		task.delay(2, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();
		});

		task.delay(4 + math.random(2, 4), () => {
			const fadeOut = TweenService.Create(fragment, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => fragment.Destroy());
		});
	}
}

export function createRealisticGlassBreaking(position: Vector3): void {
	// Create a stunning ice breaking effect like punching through frozen air
	const impactPoint = position;

	// Create impact point with bright flash
	createIceImpactCrater(impactPoint);

	// Create expanding ice crack pattern that spreads from impact
	createExpandingIceCracks(impactPoint);

	// Create ice shards that fall from the cracks
	createIceShards(impactPoint);
}

export function createIceImpactCrater(position: Vector3): void {
	// Create a bright ice impact point with expanding frost rings
	const crater = new Instance("Part");
	crater.Name = "IceImpactCrater";
	crater.Shape = Enum.PartType.Ball;
	crater.Size = new Vector3(1.5, 1.5, 1.5);
	crater.Color = Color3.fromRGB(200, 230, 255); // Ice blue color
	crater.Material = Enum.Material.Neon;
	crater.Transparency = 0;
	crater.CanCollide = false;
	crater.Anchored = true;
	crater.Position = position;
	crater.Parent = Workspace;

	// Bright ice-blue light
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(200, 230, 255);
	light.Range = 30;
	light.Brightness = 25;
	light.Parent = crater;

	// Expand and fade with ice effect
	const expandTween = TweenService.Create(
		crater,
		new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size: new Vector3(8, 8, 8),
			Transparency: 1,
		},
	);

	const lightFade = TweenService.Create(light, new TweenInfo(0.5, Enum.EasingStyle.Quad), { Brightness: 0 });

	expandTween.Play();
	lightFade.Play();

	expandTween.Completed.Connect(() => {
		crater.Destroy();
	});
}

export function createExpandingIceCracks(impactPoint: Vector3): void {
	// Create realistic ice crack pattern that expands from impact point
	const numMainCracks = 8;

	// Create main radial ice cracks - faster timing
	for (let i = 0; i < numMainCracks; i++) {
		const angle = (i / numMainCracks) * math.pi * 2;
		const delay = i * 0.03; // Faster timing

		task.delay(delay, () => {
			createIceCrackLine(impactPoint, angle, 15, true);
		});
	}

	// Create secondary branching ice cracks - faster timing
	task.delay(0.15, () => {
		for (let i = 0; i < 16; i++) {
			const angle = math.random(0, math.pi * 2);
			const delay = i * 0.02; // Faster timing

			task.delay(delay, () => {
				// Start from points around the impact area
				const startPoint = impactPoint.add(
					new Vector3(
						math.cos(angle) * math.random(2, 6),
						math.sin(angle) * math.random(2, 6),
						math.random(-2, 2), // Vary depth for 3D effect
					),
				);

				createIceCrackLine(startPoint, angle + math.random(-0.5, 0.5), 8, false);
			});
		}
	});

	// Create ice crystal formations - faster timing
	task.delay(0.3, () => {
		createIceCrystals(impactPoint);
	});
}

export function createVerticalIceCrack(startPoint: Vector3, endPoint: Vector3, isMainCrack: boolean): void {
	// Create a vertical ice crack line from start to end point
	const direction = endPoint.sub(startPoint);
	const totalLength = direction.Magnitude;
	const segments = isMainCrack ? 10 : 6;
	const segmentLength = totalLength / segments;

	for (let seg = 0; seg < segments; seg++) {
		const segmentDelay = seg * 0.015; // Fast propagation

		task.delay(segmentDelay, () => {
			const crack = new Instance("Part");
			crack.Name = `VerticalIceCrack_${isMainCrack ? "Main" : "Branch"}_${seg}`;
			crack.Shape = Enum.PartType.Block;

			// Vertical ice crack appearance
			const thickness = isMainCrack ? 0.1 : 0.06;
			const width = isMainCrack ? 0.15 : 0.1;
			crack.Size = new Vector3(thickness, segmentLength, width);

			// Ice-like appearance with blue tint
			crack.Color = Color3.fromRGB(200, 230, 255);
			crack.Material = Enum.Material.Ice;
			crack.Transparency = 0.1;
			crack.CanCollide = false;
			crack.Anchored = true;
			crack.Parent = Workspace;

			// Position along the vertical crack line
			const progress = (seg + 0.5) / segments;
			const segmentPos = startPoint.add(direction.mul(progress));
			crack.Position = segmentPos;

			// Orient vertically
			crack.CFrame = new CFrame(segmentPos, segmentPos.add(direction.Unit));

			// Ice-blue glow effect
			const light = new Instance("PointLight");
			light.Color = Color3.fromRGB(150, 200, 255);
			light.Range = isMainCrack ? 4 : 2;
			light.Brightness = isMainCrack ? 5 : 3;
			light.Parent = crack;

			// Start invisible and flash into existence
			crack.Transparency = 1;
			const flashTween = TweenService.Create(crack, new TweenInfo(0.05, Enum.EasingStyle.Quad), {
				Transparency: 0.1,
			});
			flashTween.Play();

			// Gradually fade out
			task.delay(6 + math.random(2, 4), () => {
				const fadeOut = TweenService.Create(crack, new TweenInfo(3, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeOut.Play();

				fadeOut.Completed.Connect(() => {
					crack.Destroy();
				});
			});
		});
	}
}

export function createIceCrackLine(startPoint: Vector3, angle: number, maxLength: number, isMainCrack: boolean): void {
	const segments = isMainCrack ? 12 : 8;
	const segmentLength = maxLength / segments;

	for (let seg = 0; seg < segments; seg++) {
		const segmentDelay = seg * 0.015; // Much faster ice crack propagation

		task.delay(segmentDelay, () => {
			const crack = new Instance("Part");
			crack.Name = `IceCrack_${isMainCrack ? "Main" : "Branch"}_${seg}`;
			crack.Shape = Enum.PartType.Block;

			// Ice crack appearance - thicker than glass
			const thickness = isMainCrack ? 0.08 : 0.05;
			const length = segmentLength + math.random(-0.3, 0.3);
			const width = isMainCrack ? 0.12 : 0.08;
			crack.Size = new Vector3(thickness, width, length);

			// Ice-like appearance with blue tint
			crack.Color = Color3.fromRGB(200, 230, 255);
			crack.Material = Enum.Material.Ice;
			crack.Transparency = 0.1;
			crack.CanCollide = false;
			crack.Anchored = true;
			crack.Parent = Workspace;

			// Position along the crack line with natural variation
			const deviation = math.random(-0.4, 0.4);
			const heightVariation = math.random(-1, 1);
			const distance = seg * segmentLength + segmentLength * 0.5;

			const crackPos = startPoint.add(
				new Vector3(
					math.cos(angle + deviation) * distance,
					math.sin(angle + deviation) * distance + heightVariation,
					math.random(-0.5, 0.5), // Slight depth variation
				),
			);

			crack.Position = crackPos;
			crack.CFrame = CFrame.lookAt(crackPos, crackPos.add(new Vector3(math.cos(angle), math.sin(angle), 0)));

			// Ice-blue glow effect
			const light = new Instance("PointLight");
			light.Color = Color3.fromRGB(150, 200, 255);
			light.Range = isMainCrack ? 3 : 2;
			light.Brightness = isMainCrack ? 4 : 2;
			light.Parent = crack;

			// Start invisible and flash into existence
			crack.Transparency = 1;
			const flashTween = TweenService.Create(crack, new TweenInfo(0.05, Enum.EasingStyle.Quad), {
				Transparency: 0.1,
			});
			flashTween.Play();

			// Gradually fade out
			task.delay(6 + math.random(2, 4), () => {
				const fadeOut = TweenService.Create(crack, new TweenInfo(3, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeOut.Play();

				fadeOut.Completed.Connect(() => {
					crack.Destroy();
				});
			});
		});
	}
}

export function createIceCrystals(centerPosition: Vector3): void {
	// Create ice crystal formations around the impact point
	const numCrystals = 12;

	for (let i = 0; i < numCrystals; i++) {
		const crystal = new Instance("Part");
		crystal.Name = `IceCrystal_${i}`;
		crystal.Shape = Enum.PartType.Block;
		crystal.Size = new Vector3(math.random(0.3, 0.8), math.random(1, 2.5), math.random(0.3, 0.8));
		crystal.Color = Color3.fromRGB(220, 240, 255);
		crystal.Material = Enum.Material.Ice;
		crystal.Transparency = 0.2;
		crystal.CanCollide = false;
		crystal.Anchored = true;
		crystal.Parent = Workspace;

		// Position crystals around the impact point
		const angle = (i / numCrystals) * math.pi * 2;
		const radius = math.random(4, 10);
		crystal.Position = centerPosition.add(
			new Vector3(math.cos(angle) * radius, math.sin(angle) * radius, math.random(-2, 2)),
		);

		// Random rotation for natural look
		crystal.CFrame = crystal.CFrame.mul(
			CFrame.Angles(math.random(0, math.pi), math.random(0, math.pi), math.random(0, math.pi)),
		);

		// Ice-blue glow
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(150, 200, 255);
		light.Range = 2;
		light.Brightness = 3;
		light.Parent = crystal;

		// Delayed appearance
		crystal.Transparency = 1;
		const delay = i * 0.08;
		task.delay(delay, () => {
			const appearTween = TweenService.Create(
				crystal,
				new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{ Transparency: 0.2 },
			);
			appearTween.Play();
		});

		// Fade out after some time
		task.delay(5 + math.random(2, 4), () => {
			const fadeOut = TweenService.Create(crystal, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();

			fadeOut.Completed.Connect(() => {
				crystal.Destroy();
			});
		});
	}
}

export function createIceShards(centerPosition: Vector3): void {
	// Create ice shards that fall from the broken ice
	const numShards = 20;

	for (let i = 0; i < numShards; i++) {
		const shard = new Instance("Part");
		shard.Name = `IceShard_${i}`;
		shard.Shape = Enum.PartType.Block;
		shard.Size = new Vector3(math.random(0.2, 0.6), math.random(0.4, 1.2), math.random(0.1, 0.3));
		shard.Color = Color3.fromRGB(220, 240, 255);
		shard.Material = Enum.Material.Ice;
		shard.Transparency = 0.15;
		shard.CanCollide = false;
		shard.Anchored = false;
		shard.Parent = Workspace;

		// Position around the impact point
		const angle = (i / numShards) * math.pi * 2;
		const radius = math.random(3, 8);
		shard.Position = centerPosition.add(
			new Vector3(math.cos(angle) * radius, math.sin(angle) * radius + math.random(3, 6), math.random(-1, 1)),
		);

		// Ice-blue glow
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(150, 200, 255);
		light.Range = 1;
		light.Brightness = 1.5;
		light.Parent = shard;

		// Apply gentle falling motion
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(1500, 1500, 1500);
		bodyVelocity.Velocity = new Vector3(
			math.random(-8, 8),
			math.random(-15, -8), // Downward motion
			math.random(-8, 8),
		);
		bodyVelocity.Parent = shard;

		// Add gentle rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(300, 300, 300);
		bodyAngularVelocity.AngularVelocity = new Vector3(math.random(-3, 3), math.random(-3, 3), math.random(-3, 3));
		bodyAngularVelocity.Parent = shard;

		// Remove forces after some time
		task.delay(2, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();
		});

		// Fade out and destroy
		task.delay(4 + math.random(2, 4), () => {
			const fadeOut = TweenService.Create(shard, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();

			fadeOut.Completed.Connect(() => {
				shard.Destroy();
			});
		});
	}

	print(`❄️ Created ${numShards} falling ice shards`);
}

export function createGlassCrackLine(
	startPoint: Vector3,
	angle: number,
	maxLength: number,
	isMainCrack: boolean,
	crackLines: Part[],
): void {
	const segments = isMainCrack ? 15 : 8;
	const segmentLength = maxLength / segments;

	for (let seg = 0; seg < segments; seg++) {
		const segmentDelay = seg * 0.02; // Fast propagation like real glass

		task.delay(segmentDelay, () => {
			const crack = new Instance("Part");
			crack.Name = `GlassCrack_${isMainCrack ? "Main" : "Branch"}_${seg}`;
			crack.Shape = Enum.PartType.Block;

			// Very thin crack line
			const thickness = isMainCrack ? 0.03 : 0.02;
			const length = segmentLength + math.random(-0.2, 0.2);
			crack.Size = new Vector3(thickness, thickness, length);

			// Bright glass-like appearance
			crack.Color = Color3.fromRGB(255, 255, 255);
			crack.Material = Enum.Material.Neon;
			crack.Transparency = 0;
			crack.CanCollide = false;
			crack.Anchored = true;
			crack.Parent = Workspace;

			// Position along the crack line with slight randomness
			const deviation = math.random(-0.3, 0.3);
			const distance = seg * segmentLength + segmentLength * 0.5;
			const crackPos = startPoint.add(
				new Vector3(
					math.cos(angle + deviation) * distance,
					math.sin(angle + deviation) * distance,
					math.random(-0.1, 0.1), // Slight depth variation
				),
			);

			crack.Position = crackPos;
			crack.CFrame = CFrame.lookAt(crackPos, crackPos.add(new Vector3(math.cos(angle), math.sin(angle), 0)));

			// Add to crack lines array for web connections
			crackLines.push(crack);

			// Bright glow effect
			const light = new Instance("PointLight");
			light.Color = Color3.fromRGB(200, 230, 255);
			light.Range = isMainCrack ? 2 : 1;
			light.Brightness = isMainCrack ? 3 : 2;
			light.Parent = crack;

			// Start invisible and flash into existence
			crack.Transparency = 1;
			const flashTween = TweenService.Create(crack, new TweenInfo(0.02, Enum.EasingStyle.Quad), {
				Transparency: 0,
			});
			flashTween.Play();

			// Gradually fade out
			task.delay(4 + math.random(2, 4), () => {
				const fadeOut = TweenService.Create(crack, new TweenInfo(2, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeOut.Play();

				fadeOut.Completed.Connect(() => {
					crack.Destroy();
				});
			});
		});
	}
}

export function createGlassWebConnections(impactPoint: Vector3, crackLines: Part[]): void {
	// Create web-like connections between crack segments
	const numConnections = math.min(15, crackLines.size());

	for (let i = 0; i < numConnections; i++) {
		if (crackLines.size() < 2) break;

		const delay = i * 0.04;
		task.delay(delay, () => {
			// Connect random crack segments
			const crack1 = crackLines[math.floor(math.random() * crackLines.size())];
			const crack2 = crackLines[math.floor(math.random() * crackLines.size())];

			if (crack1 !== crack2 && crack1.Parent && crack2.Parent) {
				const distance = crack1.Position.sub(crack2.Position).Magnitude;
				if (distance > 1 && distance < 8) {
					// Only connect nearby cracks
					createGlassWebSegment(crack1.Position, crack2.Position);
				}
			}
		});
	}
}

export function createGlassWebSegment(pos1: Vector3, pos2: Vector3): void {
	const webSegment = new Instance("Part");
	webSegment.Name = "GlassWebConnection";
	webSegment.Shape = Enum.PartType.Block;

	const direction = pos2.sub(pos1);
	const length = direction.Magnitude;
	const midPoint = pos1.add(direction.mul(0.5));

	webSegment.Size = new Vector3(0.015, 0.015, length); // Very thin web line
	webSegment.Color = Color3.fromRGB(255, 255, 255);
	webSegment.Material = Enum.Material.Neon;
	webSegment.Transparency = 0.2;
	webSegment.CanCollide = false;
	webSegment.Anchored = true;
	webSegment.Parent = Workspace;

	webSegment.CFrame = CFrame.lookAt(midPoint, pos2);

	// Subtle glow
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(200, 230, 255);
	light.Range = 0.5;
	light.Brightness = 1;
	light.Parent = webSegment;

	// Flash into existence
	webSegment.Transparency = 1;
	const appearTween = TweenService.Create(webSegment, new TweenInfo(0.03, Enum.EasingStyle.Quad), {
		Transparency: 0.2,
	});
	appearTween.Play();

	// Fade out
	task.delay(3 + math.random(1, 3), () => {
		const fadeOut = TweenService.Create(webSegment, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
		fadeOut.Play();

		fadeOut.Completed.Connect(() => {
			webSegment.Destroy();
		});
	});
}

export function createGlassImpactCrater(position: Vector3): void {
	// Create a bright impact point with expanding rings
	const crater = new Instance("Part");
	crater.Name = "GlassImpactCrater";
	crater.Shape = Enum.PartType.Ball;
	crater.Size = new Vector3(1, 1, 1);
	crater.Color = Color3.fromRGB(255, 255, 255);
	crater.Material = Enum.Material.Neon;
	crater.Transparency = 0;
	crater.CanCollide = false;
	crater.Anchored = true;
	crater.Position = position;
	crater.Parent = Workspace;

	// Bright light
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(255, 255, 255);
	light.Range = 25;
	light.Brightness = 20;
	light.Parent = crater;

	// Expand and fade
	const expandTween = TweenService.Create(
		crater,
		new TweenInfo(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size: new Vector3(6, 6, 6),
			Transparency: 1,
		},
	);

	const lightFade = TweenService.Create(light, new TweenInfo(0.4, Enum.EasingStyle.Quad), { Brightness: 0 });

	expandTween.Play();
	lightFade.Play();

	expandTween.Completed.Connect(() => {
		crater.Destroy();
	});
}

export function animateCrackLine(nodes: Vector3[], delay: number, isMainCrack: boolean): void {
	for (let i = 0; i < nodes.size() - 1; i++) {
		const startNode = nodes[i];
		const endNode = nodes[i + 1];
		const segmentDelay = delay + i * 0.03;

		task.delay(segmentDelay, () => {
			createCrackSegment(startNode, endNode, isMainCrack);
		});
	}
}

export function createCrackSegment(startPos: Vector3, endPos: Vector3, isMainCrack: boolean): void {
	const segment = new Instance("Part");
	segment.Name = `GlassCrack_${isMainCrack ? "Main" : "Secondary"}`;
	segment.Shape = Enum.PartType.Block;

	// Calculate segment properties
	const direction = endPos.sub(startPos);
	const length = direction.Magnitude;
	const midPoint = startPos.add(direction.mul(0.5));

	// Thin crack appearance
	const thickness = isMainCrack ? 0.05 : 0.03;
	segment.Size = new Vector3(thickness, thickness, length);

	// Realistic glass crack appearance
	segment.Color = Color3.fromRGB(245, 250, 255);
	segment.Material = Enum.Material.Glass;
	segment.Transparency = isMainCrack ? 0.1 : 0.2;
	segment.CanCollide = false;
	segment.Anchored = true;
	segment.Parent = Workspace;

	// Orient segment along the crack direction
	segment.CFrame = CFrame.lookAt(midPoint, endPos);

	// Add subtle glow for main cracks
	if (isMainCrack) {
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(200, 220, 255);
		light.Range = 1.5;
		light.Brightness = 0.8;
		light.Parent = segment;
	}

	// Start invisible and appear quickly
	segment.Transparency = 1;
	const appearTween = TweenService.Create(segment, new TweenInfo(0.05, Enum.EasingStyle.Quad), {
		Transparency: isMainCrack ? 0.1 : 0.2,
	});
	appearTween.Play();

	// Fade out after some time
	task.delay(4 + math.random(1, 3), () => {
		const fadeOut = TweenService.Create(segment, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
		fadeOut.Play();

		fadeOut.Completed.Connect(() => {
			segment.Destroy();
		});
	});
}

export function createGlassWebPattern(centerPoint: Vector3, existingNodes: Vector3[]): void {
	// Create interconnecting web cracks between existing crack nodes
	const numWebCracks = 8;

	for (let i = 0; i < numWebCracks; i++) {
		if (existingNodes.size() < 2) break;

		// Connect random nodes to create web pattern
		const node1 = existingNodes[math.floor(math.random() * existingNodes.size())];
		const node2 = existingNodes[math.floor(math.random() * existingNodes.size())];

		if (node1 !== node2) {
			const delay = i * 0.1;
			task.delay(delay, () => {
				createCrackSegment(node1, node2, false);
			});
		}
	}
}

export function createImpactFlash(position: Vector3): void {
	// Create bright flash at impact point
	const flash = new Instance("Part");
	flash.Name = "ImpactFlash";
	flash.Shape = Enum.PartType.Ball;
	flash.Size = new Vector3(0.5, 0.5, 0.5);
	flash.Color = Color3.fromRGB(255, 255, 255);
	flash.Material = Enum.Material.Neon;
	flash.Transparency = 0;
	flash.CanCollide = false;
	flash.Anchored = true;
	flash.Position = position;
	flash.Parent = Workspace;

	// Bright light
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(255, 255, 255);
	light.Range = 20;
	light.Brightness = 15;
	light.Parent = flash;

	// Quick expand and fade
	const expandTween = TweenService.Create(
		flash,
		new TweenInfo(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size: new Vector3(4, 4, 4),
			Transparency: 1,
		},
	);

	const lightFade = TweenService.Create(light, new TweenInfo(0.3, Enum.EasingStyle.Quad), { Brightness: 0 });

	expandTween.Play();
	lightFade.Play();

	expandTween.Completed.Connect(() => {
		flash.Destroy();
	});
}

export function createFlyingObjects(centerPosition: Vector3): void {
	// Always create debris objects for guaranteed flying effect
	createDebrisObjects(centerPosition);

	// Also try to find and affect existing objects
	const objectsInRange: Part[] = [];

	// Search for parts in workspace recursively
	const searchForParts = (parent: Instance): void => {
		for (const obj of parent.GetChildren()) {
			if (obj.IsA("Part") && obj.Name !== "Baseplate" && obj.Parent !== Players.LocalPlayer.Character) {
				const distance = obj.Position.sub(centerPosition).Magnitude;
				if (distance < 40) {
					// Increased range
					objectsInRange.push(obj);
				}
			}
			// Search in models and folders
			if (obj.IsA("Model") || obj.IsA("Folder")) {
				searchForParts(obj);
			}
		}
	};

	searchForParts(Workspace);

	// Apply gravity effects to found objects
	for (const part of objectsInRange) {
		// Skip if part is too large or important
		if (
			part.Size.Magnitude > 20 ||
			part.Name.lower().find("spawn")[0] !== undefined ||
			part.Name.lower().find("base")[0] !== undefined
		) {
			continue;
		}

		// Temporarily unanchor if anchored
		const wasAnchored = part.Anchored;
		part.Anchored = false;

		// Create BodyVelocity for flying effect
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(12000, 12000, 12000);

		// Calculate direction away from center with strong upward force
		const direction = part.Position.sub(centerPosition).Unit;
		const upwardForce = new Vector3(0, math.random(35, 70), 0);
		const outwardForce = direction.mul(math.random(30, 60));

		bodyVelocity.Velocity = outwardForce.add(upwardForce);
		bodyVelocity.Parent = part;

		// Add dramatic rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(4000, 4000, 4000);
		bodyAngularVelocity.AngularVelocity = new Vector3(math.random(-8, 8), math.random(-8, 8), math.random(-8, 8));
		bodyAngularVelocity.Parent = part;

		// Remove the forces after some time
		task.delay(2.5, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();

			// Re-anchor if it was originally anchored
			if (wasAnchored) {
				task.delay(4, () => {
					if (part.Parent) {
						part.Anchored = true;
					}
				});
			}
		});
	}

	print(`🌪️ Applied gravity effects to ${objectsInRange.size()} existing objects + created debris`);
}

export function createDebrisObjects(centerPosition: Vector3): void {
	// Create dramatic debris objects for guaranteed flying effect
	const numDebris = 15; // More debris for better effect

	for (let i = 0; i < numDebris; i++) {
		const debris = new Instance("Part");
		debris.Name = "QuakeDebris";

		// Varied shapes for more interesting debris
		const shapeRandom = math.random();
		if (shapeRandom < 0.4) {
			debris.Shape = Enum.PartType.Block;
		} else if (shapeRandom < 0.7) {
			debris.Shape = Enum.PartType.Ball;
		} else {
			debris.Shape = Enum.PartType.Cylinder;
		}

		debris.Size = new Vector3(math.random(0.8, 3), math.random(0.8, 3), math.random(0.8, 3));

		// Realistic debris colors (concrete, rock, metal)
		const colorType = math.random();
		if (colorType < 0.5) {
			// Concrete/stone
			debris.Color = Color3.fromRGB(math.random(80, 120), math.random(75, 115), math.random(70, 110));
			debris.Material = Enum.Material.Concrete;
		} else if (colorType < 0.8) {
			// Metal
			debris.Color = Color3.fromRGB(math.random(60, 100), math.random(60, 100), math.random(60, 100));
			debris.Material = Enum.Material.Metal;
		} else {
			// Rock
			debris.Color = Color3.fromRGB(math.random(90, 130), math.random(85, 125), math.random(80, 120));
			debris.Material = Enum.Material.Rock;
		}

		debris.CanCollide = true;
		debris.Anchored = false;
		debris.Parent = Workspace;

		// Position around the impact point with more spread
		const angle = (i / numDebris) * math.pi * 2 + math.random(-0.3, 0.3);
		const radius = math.random(2, 12);
		debris.Position = centerPosition.add(
			new Vector3(math.cos(angle) * radius, math.random(1, 4), math.sin(angle) * radius),
		);

		// Apply powerful initial velocity
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(10000, 10000, 10000);

		const direction = debris.Position.sub(centerPosition).Unit;
		const upwardForce = new Vector3(0, math.random(40, 80), 0);
		const outwardForce = direction.mul(math.random(35, 70));

		bodyVelocity.Velocity = outwardForce.add(upwardForce);
		bodyVelocity.Parent = debris;

		// Add dramatic rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(5000, 5000, 5000);
		bodyAngularVelocity.AngularVelocity = new Vector3(
			math.random(-12, 12),
			math.random(-12, 12),
			math.random(-12, 12),
		);
		bodyAngularVelocity.Parent = debris;

		// No sound effects for debris objects

		// Remove forces after some time
		task.delay(3, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();
		});

		// Clean up debris after longer time
		task.delay(12 + math.random(3, 8), () => {
			if (debris.Parent) {
				const fadeOut = TweenService.Create(debris, new TweenInfo(3, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeOut.Play();

				fadeOut.Completed.Connect(() => {
					debris.Destroy();
				});
			}
		});
	}

	print(`🌪️ Created ${numDebris} dramatic debris objects for flying effect`);
}

export function createFlyingParticles(centerPosition: Vector3): void {
	// Create glass/ice particles that fly around
	const numParticles = 25;

	for (let i = 0; i < numParticles; i++) {
		const particle = new Instance("Part");
		particle.Name = `GlassParticle_${i}`;
		particle.Shape = Enum.PartType.Block;
		particle.Size = new Vector3(math.random(0.1, 0.3), math.random(0.1, 0.3), math.random(0.1, 0.3));
		particle.Color = Color3.fromRGB(240, 250, 255);
		particle.Material = Enum.Material.Glass;
		particle.Transparency = 0.2;
		particle.CanCollide = false;
		particle.Anchored = false; // Let physics handle movement
		particle.Parent = Workspace;

		// Position around the impact point
		const angle = (i / numParticles) * math.pi * 2;
		const radius = math.random(2, 5);
		const startPos = centerPosition.add(
			new Vector3(math.cos(angle) * radius, math.random(-1, 3), math.sin(angle) * radius),
		);
		particle.Position = startPos;

		// Add subtle glow
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(200, 220, 255);
		light.Range = 1;
		light.Brightness = 0.5;
		light.Parent = particle;

		// Apply initial velocity
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(2000, 2000, 2000);

		// Random direction with upward bias
		const direction = new Vector3(
			math.random(-1, 1),
			math.random(0.5, 1.5), // Upward bias
			math.random(-1, 1),
		).Unit;

		bodyVelocity.Velocity = direction.mul(math.random(10, 25));
		bodyVelocity.Parent = particle;

		// Add rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(500, 500, 500);
		bodyAngularVelocity.AngularVelocity = new Vector3(
			math.random(-10, 10),
			math.random(-10, 10),
			math.random(-10, 10),
		);
		bodyAngularVelocity.Parent = particle;

		// Delayed appearance
		particle.Transparency = 1;
		task.delay(math.random(0.1, 0.3), () => {
			const appearTween = TweenService.Create(particle, new TweenInfo(0.2, Enum.EasingStyle.Quad), {
				Transparency: 0.2,
			});
			appearTween.Play();
		});

		// Remove forces after some time and let gravity take over
		task.delay(1, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();
		});

		// Fade out and destroy
		task.delay(3 + math.random(1, 3), () => {
			const fadeOut = TweenService.Create(particle, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();

			fadeOut.Completed.Connect(() => {
				particle.Destroy();
			});
		});
	}

	print(`✨ Created ${numParticles} flying glass particles`);
}

export function createCrackSparklingEffects(position: Vector3, direction: Vector3): void {
	print(`✨ Creating spectacular sparkling effects for cracks at ${position}`);

	// Create multiple types of sparkling effects
	createCrackSparkles(position);
	createEnergyMotes(position, direction);
	createShimmeringDust(position);
	createCrackGlitter(position, direction);
	createFloatingSparkles(position);

	print("✅ Crack sparkling effects created");
}

export function createCrackSparkles(position: Vector3): void {
	// Create tiny sparkling particles that emanate from crack lines
	const numSparkles = 50;

	for (let i = 0; i < numSparkles; i++) {
		const sparkle = new Instance("Part");
		sparkle.Name = `CrackSparkle_${i}`;
		sparkle.Shape = Enum.PartType.Ball;
		sparkle.Size = new Vector3(0.05, 0.05, 0.05); // Very tiny sparkles
		sparkle.Color = Color3.fromRGB(255, 255, 255); // Pure white sparkles
		sparkle.Material = Enum.Material.Neon;
		sparkle.Transparency = 0;
		sparkle.CanCollide = false;
		sparkle.Anchored = true;
		sparkle.Parent = Workspace;

		// Position sparkles along crack lines with random distribution
		const angle = math.random(0, math.pi * 2);
		const radius = math.random(1, 12); // Spread along crack length
		const height = math.random(-2, 4); // Vary height

		sparkle.Position = position.add(new Vector3(math.cos(angle) * radius, height, math.sin(angle) * radius));

		// Bright sparkle light
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(255, 255, 255);
		light.Range = 0.5;
		light.Brightness = 5;
		light.Parent = sparkle;

		// Twinkling animation - sparkles appear and disappear randomly
		const twinkleDelay = math.random(0, 2);
		const twinkleDuration = math.random(0.1, 0.3);

		task.delay(twinkleDelay, () => {
			// Flash bright
			const flashTween = TweenService.Create(
				sparkle,
				new TweenInfo(twinkleDuration * 0.3, Enum.EasingStyle.Quad),
				{
					Transparency: 0,
					Size: new Vector3(0.1, 0.1, 0.1),
				},
			);
			flashTween.Play();

			// Then fade out
			flashTween.Completed.Connect(() => {
				const fadeOut = TweenService.Create(
					sparkle,
					new TweenInfo(twinkleDuration * 0.7, Enum.EasingStyle.Quad),
					{
						Transparency: 1,
						Size: new Vector3(0.02, 0.02, 0.02),
					},
				);
				fadeOut.Play();
				fadeOut.Completed.Connect(() => sparkle.Destroy());
			});
		});
	}
}

export function createEnergyMotes(position: Vector3, direction: Vector3): void {
	// Create floating energy motes that drift around the crack area
	const numMotes = 25;

	for (let i = 0; i < numMotes; i++) {
		const mote = new Instance("Part");
		mote.Name = `EnergyMote_${i}`;
		mote.Shape = Enum.PartType.Ball;
		mote.Size = new Vector3(0.15, 0.15, 0.15);
		mote.Color = Color3.fromRGB(200, 230, 255); // Soft blue-white
		mote.Material = Enum.Material.Neon;
		mote.Transparency = 0.3;
		mote.CanCollide = false;
		mote.Anchored = true;
		mote.Parent = Workspace;

		// Position around the crack area
		const angle = math.random(0, math.pi * 2);
		const radius = math.random(3, 10);
		const startPos = position.add(
			new Vector3(math.cos(angle) * radius, math.random(-1, 3), math.sin(angle) * radius),
		);
		mote.Position = startPos;

		// Soft glow
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(150, 200, 255);
		light.Range = 2;
		light.Brightness = 1;
		light.Parent = mote;

		// Gentle floating motion
		const floatDirection = new Vector3(math.random(-1, 1), math.random(0.5, 1.5), math.random(-1, 1)).Unit;

		const endPos = startPos.add(floatDirection.mul(math.random(5, 15)));

		const floatTween = TweenService.Create(
			mote,
			new TweenInfo(math.random(3, 6), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{ Position: endPos },
		);
		floatTween.Play();

		// Gentle pulsing
		const pulseTween = TweenService.Create(
			mote,
			new TweenInfo(math.random(1, 2), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
			{
				Transparency: 0.6,
				Size: new Vector3(0.2, 0.2, 0.2),
			},
		);
		pulseTween.Play();

		// Fade out after time
		task.delay(4 + math.random(2, 4), () => {
			pulseTween.Cancel();
			const fadeOut = TweenService.Create(mote, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => mote.Destroy());
		});
	}
}

export function createShimmeringDust(position: Vector3): void {
	// Create shimmering dust particles that float around the cracks
	const numDustParticles = 30;

	for (let i = 0; i < numDustParticles; i++) {
		const dust = new Instance("Part");
		dust.Name = `ShimmeringDust_${i}`;
		dust.Shape = Enum.PartType.Ball;
		dust.Size = new Vector3(0.08, 0.08, 0.08);
		dust.Color = Color3.fromRGB(255, 245, 200); // Golden shimmer
		dust.Material = Enum.Material.Neon;
		dust.Transparency = 0.4;
		dust.CanCollide = false;
		dust.Anchored = true;
		dust.Parent = Workspace;

		// Position dust particles around crack area
		const angle = math.random(0, math.pi * 2);
		const radius = math.random(2, 8);
		const height = math.random(-1, 5);

		dust.Position = position.add(new Vector3(math.cos(angle) * radius, height, math.sin(angle) * radius));

		// Gentle shimmer light
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(255, 220, 150);
		light.Range = 1;
		light.Brightness = 0.8;
		light.Parent = dust;

		// Slow floating motion with shimmer
		const floatTween = TweenService.Create(
			dust,
			new TweenInfo(math.random(4, 8), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{
				Position: dust.Position.add(new Vector3(math.random(-3, 3), math.random(2, 6), math.random(-3, 3))),
			},
		);
		floatTween.Play();

		// Shimmer effect - transparency changes
		const shimmerTween = TweenService.Create(
			dust,
			new TweenInfo(math.random(0.5, 1.5), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
			{ Transparency: 0.8 },
		);
		shimmerTween.Play();

		// Fade out
		task.delay(5 + math.random(2, 4), () => {
			shimmerTween.Cancel();
			const fadeOut = TweenService.Create(dust, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => dust.Destroy());
		});
	}
}

export function createCrackGlitter(position: Vector3, direction: Vector3): void {
	// Create glittering particles that follow the crack lines
	const numGlitter = 40;

	for (let i = 0; i < numGlitter; i++) {
		const glitter = new Instance("Part");
		glitter.Name = `CrackGlitter_${i}`;
		glitter.Shape = Enum.PartType.Block;
		glitter.Size = new Vector3(0.03, 0.03, 0.03); // Very small glitter
		glitter.Color = Color3.fromRGB(255, 255, 255);
		glitter.Material = Enum.Material.Neon;
		glitter.Transparency = 0;
		glitter.CanCollide = false;
		glitter.Anchored = true;
		glitter.Parent = Workspace;

		// Position along crack lines
		const angle = (i / numGlitter) * math.pi * 2;
		const radius = math.random(1, 15);
		const deviation = math.random(-0.5, 0.5);

		glitter.Position = position.add(
			new Vector3(math.cos(angle + deviation) * radius, math.random(-2, 4), math.sin(angle + deviation) * radius),
		);

		// Random rotation for glitter effect
		glitter.CFrame = glitter.CFrame.mul(
			CFrame.Angles(math.random(0, math.pi * 2), math.random(0, math.pi * 2), math.random(0, math.pi * 2)),
		);

		// Intense sparkle light
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(255, 255, 255);
		light.Range = 0.3;
		light.Brightness = 8;
		light.Parent = glitter;

		// Quick sparkle animation
		const sparkleDelay = math.random(0, 1.5);

		task.delay(sparkleDelay, () => {
			// Quick flash
			const flashTween = TweenService.Create(glitter, new TweenInfo(0.05, Enum.EasingStyle.Quad), {
				Size: new Vector3(0.08, 0.08, 0.08),
				Transparency: 0,
			});
			flashTween.Play();

			// Quick fade
			flashTween.Completed.Connect(() => {
				const fadeOut = TweenService.Create(glitter, new TweenInfo(0.2, Enum.EasingStyle.Quad), {
					Transparency: 1,
					Size: new Vector3(0.01, 0.01, 0.01),
				});
				fadeOut.Play();
				fadeOut.Completed.Connect(() => glitter.Destroy());
			});
		});
	}
}

export function createFloatingSparkles(position: Vector3): void {
	// Create floating sparkles that rise up from the cracks
	const numSparkles = 20;

	for (let i = 0; i < numSparkles; i++) {
		const sparkle = new Instance("Part");
		sparkle.Name = `FloatingSparkle_${i}`;
		sparkle.Shape = Enum.PartType.Ball;
		sparkle.Size = new Vector3(0.06, 0.06, 0.06);
		sparkle.Color = Color3.fromRGB(255, 255, 255);
		sparkle.Material = Enum.Material.Neon;
		sparkle.Transparency = 0.2;
		sparkle.CanCollide = false;
		sparkle.Anchored = true;
		sparkle.Parent = Workspace;

		// Start position near cracks
		const angle = math.random(0, math.pi * 2);
		const radius = math.random(1, 8);
		const startPos = position.add(
			new Vector3(math.cos(angle) * radius, math.random(-1, 1), math.sin(angle) * radius),
		);
		sparkle.Position = startPos;

		// Bright sparkle light
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(255, 255, 255);
		light.Range = 1;
		light.Brightness = 3;
		light.Parent = sparkle;

		// Float upward with gentle swaying
		const endPos = startPos.add(new Vector3(math.random(-2, 2), math.random(8, 15), math.random(-2, 2)));

		const floatTween = TweenService.Create(
			sparkle,
			new TweenInfo(math.random(3, 5), Enum.EasingStyle.Sine, Enum.EasingDirection.Out),
			{ Position: endPos },
		);
		floatTween.Play();

		// Gentle twinkling
		const twinkleTween = TweenService.Create(
			sparkle,
			new TweenInfo(math.random(0.3, 0.8), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
			{
				Transparency: 0.6,
				Size: new Vector3(0.1, 0.1, 0.1),
			},
		);
		twinkleTween.Play();

		// Fade out as it rises
		task.delay(2, () => {
			const fadeOut = TweenService.Create(sparkle, new TweenInfo(3, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => sparkle.Destroy());
		});
	}
}

export function createGlassShards(centerPosition: Vector3): void {
	// Create beautiful glass shards that fall from the cracks
	const numShards = 15;

	for (let i = 0; i < numShards; i++) {
		const shard = new Instance("Part");
		shard.Name = `GlassShard_${i}`;
		shard.Shape = Enum.PartType.Block;
		shard.Size = new Vector3(math.random(0.2, 0.6), math.random(0.2, 0.6), math.random(0.05, 0.15));
		shard.Color = Color3.fromRGB(245, 250, 255);
		shard.Material = Enum.Material.Glass;
		shard.Transparency = 0.1;
		shard.CanCollide = false;
		shard.Anchored = false;
		shard.Parent = Workspace;

		// Position around the impact point
		const angle = (i / numShards) * math.pi * 2;
		const radius = math.random(3, 8);
		shard.Position = centerPosition.add(
			new Vector3(math.cos(angle) * radius, math.random(2, 6), math.sin(angle) * radius),
		);

		// Add subtle glow
		const light = new Instance("PointLight");
		light.Color = Color3.fromRGB(200, 220, 255);
		light.Range = 1.5;
		light.Brightness = 0.8;
		light.Parent = shard;

		// Apply gentle falling motion
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(1000, 1000, 1000);
		bodyVelocity.Velocity = new Vector3(
			math.random(-5, 5),
			math.random(-10, -5), // Downward motion
			math.random(-5, 5),
		);
		bodyVelocity.Parent = shard;

		// Add gentle rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(200, 200, 200);
		bodyAngularVelocity.AngularVelocity = new Vector3(math.random(-2, 2), math.random(-2, 2), math.random(-2, 2));
		bodyAngularVelocity.Parent = shard;

		// Remove forces after some time
		task.delay(1.5, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();
		});

		// Fade out and destroy
		task.delay(4 + math.random(2, 4), () => {
			const fadeOut = TweenService.Create(shard, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();

			fadeOut.Completed.Connect(() => {
				shard.Destroy();
			});
		});
	}

	print(`💎 Created ${numShards} falling glass shards`);
}

export function createEnhancedShockwave(position: Vector3): void {
	// Create multiple shockwave rings for layered effect
	for (let ring = 0; ring < 3; ring++) {
		const shockwave = new Instance("Part");
		shockwave.Name = `EnhancedShockwave_${ring}`;
		shockwave.Shape = Enum.PartType.Cylinder;
		shockwave.Size = new Vector3(0.2, 1, 1);
		shockwave.Color = ring === 0 ? Color3.fromRGB(255, 255, 255) : Color3.fromRGB(240, 248, 255);
		shockwave.Material = Enum.Material.ForceField;
		shockwave.Transparency = 0.1 + ring * 0.2;
		shockwave.CanCollide = false;
		shockwave.Anchored = true;
		shockwave.CFrame = new CFrame(position).mul(CFrame.Angles(0, 0, math.rad(90)));
		shockwave.Parent = Workspace;

		// Add intense light only to first ring
		if (ring === 0) {
			const light = new Instance("PointLight");
			light.Color = Color3.fromRGB(255, 255, 255);
			light.Range = 40;
			light.Brightness = 10;
			light.Parent = shockwave;

			// Flash effect
			const flashTween = TweenService.Create(light, new TweenInfo(0.3, Enum.EasingStyle.Quad), { Brightness: 3 });
			flashTween.Play();
		}

		// Staggered expansion for wave effect
		const delay = ring * 0.1;
		const maxSize = 80 - ring * 10; // Each ring slightly smaller

		task.delay(delay, () => {
			const expandTween = TweenService.Create(
				shockwave,
				new TweenInfo(2.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					Size: new Vector3(0.2, maxSize, maxSize),
					Transparency: 1,
				},
			);
			expandTween.Play();
			expandTween.Completed.Connect(() => shockwave.Destroy());
		});
	}

	// No ground impact - only air cracks
}

export function createRealisticAirFractures(centerPos: Vector3, direction: Vector3): void {
	// Create impact point in front of punch
	const impactPoint = centerPos.add(direction.mul(8));

	// Create central impact shape (like broken glass center)
	createImpactCenter(impactPoint);

	// Create radiating cracks from impact point (like glass fracture pattern)
	createRadiatingCracks(impactPoint, direction);

	// Create secondary web pattern
	createCrackWeb(impactPoint, direction);
}

export function createImpactCenter(impactPoint: Vector3): void {
	// Create the main "hole" in the air - like glass was punched through
	const airHole = new Instance("Part");
	airHole.Name = "AirHole";
	airHole.Shape = Enum.PartType.Ball;
	airHole.Size = new Vector3(2, 2, 2);
	airHole.Color = Color3.fromRGB(0, 0, 0); // Black hole effect
	airHole.Material = Enum.Material.ForceField;
	airHole.Transparency = 0.8; // Semi-transparent dark center
	airHole.CanCollide = false;
	airHole.Anchored = true;
	airHole.Position = impactPoint;
	airHole.Parent = Workspace;

	// Create bright white rim around the hole
	const rim = new Instance("Part");
	rim.Name = "ImpactRim";
	rim.Shape = Enum.PartType.Ball;
	rim.Size = new Vector3(2.2, 2.2, 2.2);
	rim.Color = Color3.fromRGB(255, 255, 255);
	rim.Material = Enum.Material.Neon;
	rim.Transparency = 0.1;
	rim.CanCollide = false;
	rim.Anchored = true;
	rim.Position = impactPoint;
	rim.Parent = Workspace;

	// Add intense light at impact center
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(255, 255, 255);
	light.Range = 25;
	light.Brightness = 10;
	light.Parent = rim;

	// Create glass fragments floating around the break
	for (let i = 0; i < 8; i++) {
		const fragment = new Instance("Part");
		fragment.Name = `GlassFragment_${i}`;
		fragment.Shape = Enum.PartType.Block;
		fragment.Size = new Vector3(math.random(0.3, 0.8), math.random(0.3, 0.8), math.random(0.05, 0.1)); // Thin like glass shards
		fragment.Color = Color3.fromRGB(240, 248, 255);
		fragment.Material = Enum.Material.Glass;
		fragment.Transparency = 0.2;
		fragment.CanCollide = false;
		fragment.Anchored = true;
		fragment.Parent = Workspace;

		// Position fragments around the hole
		const angle = (i / 8) * math.pi * 2 + math.random(-0.3, 0.3);
		const distance = math.random(1.5, 3);
		const fragmentPos = impactPoint.add(
			new Vector3(math.cos(angle) * distance, math.random(-1, 1), math.sin(angle) * distance),
		);

		// Orient fragments randomly like broken glass pieces
		fragment.CFrame = new CFrame(fragmentPos).mul(
			CFrame.Angles(
				math.random(-math.pi, math.pi),
				math.random(-math.pi, math.pi),
				math.random(-math.pi, math.pi),
			),
		);
	}

	// Flash appearance effect
	airHole.Transparency = 1;
	rim.Transparency = 1;

	const holeFlash = TweenService.Create(airHole, new TweenInfo(0.15, Enum.EasingStyle.Quad), { Transparency: 0.8 });

	const rimFlash = TweenService.Create(rim, new TweenInfo(0.1, Enum.EasingStyle.Quad), { Transparency: 0.1 });

	holeFlash.Play();
	rimFlash.Play();

	// Disappear after time
	task.delay(6, () => {
		const holeFade = TweenService.Create(airHole, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
		const rimFade = TweenService.Create(rim, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
		holeFade.Play();
		rimFade.Play();

		holeFade.Completed.Connect(() => {
			airHole.Destroy();
			rim.Destroy();
		});
	});
}

export function createRadiatingCracks(impactPoint: Vector3, direction: Vector3): void {
	// Create main glass fracture lines radiating from the break point
	for (let i = 0; i < 16; i++) {
		// More cracks for better effect
		const angle = (i / 16) * math.pi * 2;
		const rightVector = direction.Cross(new Vector3(0, 1, 0)).Unit;
		const upVector = new Vector3(0, 1, 0);

		// Calculate crack direction with more variation
		const crackDirection = rightVector
			.mul(math.cos(angle))
			.add(upVector.mul(math.sin(angle)))
			.add(direction.mul(math.random(0.1, 0.5))); // More forward bias variation
	}
}

export function createCrackWeb(impactPoint: Vector3, direction: Vector3): void {
	// Create secondary glass fractures and connecting patterns
	for (let i = 0; i < 16; i++) {
		const webCrack = new Instance("Part");
		webCrack.Name = `GlassWeb_${i}`;
		webCrack.Shape = Enum.PartType.Block;

		const length = math.random(6, 15);
		webCrack.Size = new Vector3(0.02, length, 0.02); // Even thinner for secondary cracks

		webCrack.Color = Color3.fromRGB(245, 250, 255);
		webCrack.Material = Enum.Material.Glass;
		webCrack.Transparency = 0.4; // More transparent for secondary cracks
		webCrack.CanCollide = false;
		webCrack.Anchored = true;
		webCrack.Parent = Workspace;

		// Position web cracks in realistic glass pattern
		const angle = math.random(0, math.pi * 2);
		const distance = math.random(4, 18);
		const rightVector = direction.Cross(new Vector3(0, 1, 0)).Unit;
		const upVector = new Vector3(0, 1, 0);

		const webPos = impactPoint
			.add(rightVector.mul(math.cos(angle) * distance))
			.add(upVector.mul(math.sin(angle) * distance * 0.8))
			.add(
				direction.mul(math.random(-2, 4)), // Some forward/backward variation
			);

		// Orient towards impact point for realistic glass web
		const towardCenter = impactPoint.sub(webPos).Unit;
		const randomVariation = new Vector3(math.random(-0.6, 0.6), math.random(-0.4, 0.4), math.random(-0.6, 0.6));
		const webDirection = towardCenter.add(randomVariation).Unit;

		webCrack.CFrame = CFrame.lookAt(webPos, webPos.add(webDirection));

		// Delayed appearance after main cracks
		webCrack.Transparency = 1;
		task.delay(math.random(0.3, 0.8), () => {
			const appearTween = TweenService.Create(webCrack, new TweenInfo(0.12, Enum.EasingStyle.Quad), {
				Transparency: 0.4,
			});
			appearTween.Play();
		});

		// Create small glass dust particles around some web cracks
		if (i % 3 === 0) {
			createGlassDust(webPos);
		}

		// Disappear
		task.delay(6, () => {
			const fadeOut = TweenService.Create(webCrack, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => webCrack.Destroy());
		});
	}
}

export function createGlassDust(position: Vector3): void {
	// Create tiny glass dust particles for extra realism
	for (let i = 0; i < 4; i++) {
		const dust = new Instance("Part");
		dust.Name = `GlassDust_${i}`;
		dust.Shape = Enum.PartType.Ball;
		dust.Size = new Vector3(0.1, 0.1, 0.1);
		dust.Color = Color3.fromRGB(255, 255, 255);
		dust.Material = Enum.Material.Glass;
		dust.Transparency = 0.6;
		dust.CanCollide = false;
		dust.Anchored = true;
		dust.Parent = Workspace;

		dust.Position = position.add(
			new Vector3(math.random(-0.8, 0.8), math.random(-0.8, 0.8), math.random(-0.8, 0.8)),
		);

		// Delayed appearance
		dust.Transparency = 1;
		task.delay(math.random(0.4, 0.9), () => {
			const appearTween = TweenService.Create(dust, new TweenInfo(0.2, Enum.EasingStyle.Quad), {
				Transparency: 0.6,
			});
			appearTween.Play();
		});

		// Quick disappear
		task.delay(3, () => {
			const fadeOut = TweenService.Create(dust, new TweenInfo(1, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => dust.Destroy());
		});
	}
}

export function createMassiveImpactFlash(position: Vector3): void {
	// Create an absolutely massive impact flash that lights up everything
	const flash = new Instance("Part");
	flash.Name = "MassiveImpactFlash";
	flash.Shape = Enum.PartType.Ball;
	flash.Size = new Vector3(3, 3, 3);
	flash.Color = Color3.fromRGB(255, 255, 255);
	flash.Material = Enum.Material.Neon;
	flash.Transparency = 0;
	flash.CanCollide = false;
	flash.Anchored = true;
	flash.Position = position;
	flash.Parent = Workspace;

	// MASSIVE light that illuminates everything
	const light = new Instance("PointLight");
	light.Color = Color3.fromRGB(255, 255, 255);
	light.Range = 100; // Huge range
	light.Brightness = 50; // Extremely bright
	light.Parent = flash;

	// Multiple expanding rings for layered effect
	for (let ring = 0; ring < 5; ring++) {
		const ringFlash = new Instance("Part");
		ringFlash.Name = `FlashRing_${ring}`;
		ringFlash.Shape = Enum.PartType.Ball;
		ringFlash.Size = new Vector3(2 + ring, 2 + ring, 2 + ring);
		ringFlash.Color = Color3.fromRGB(255, 255, 255);
		ringFlash.Material = Enum.Material.ForceField;
		ringFlash.Transparency = 0.3 + ring * 0.1;
		ringFlash.CanCollide = false;
		ringFlash.Anchored = true;
		ringFlash.Position = position;
		ringFlash.Parent = Workspace;

		// Staggered expansion
		const delay = ring * 0.05;
		task.delay(delay, () => {
			const expandTween = TweenService.Create(
				ringFlash,
				new TweenInfo(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					Size: new Vector3(25 + ring * 5, 25 + ring * 5, 25 + ring * 5),
					Transparency: 1,
				},
			);
			expandTween.Play();
			expandTween.Completed.Connect(() => ringFlash.Destroy());
		});
	}

	// Main flash expansion and fade
	const expandTween = TweenService.Create(
		flash,
		new TweenInfo(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size: new Vector3(20, 20, 20),
			Transparency: 1,
		},
	);

	const lightFade = TweenService.Create(light, new TweenInfo(0.6, Enum.EasingStyle.Quad), { Brightness: 0 });

	expandTween.Play();
	lightFade.Play();

	expandTween.Completed.Connect(() => {
		flash.Destroy();
	});

	print("💥 MASSIVE impact flash created!");
}

export function createFloatingDebris(position: Vector3, direction: Vector3): void {
	for (let i = 0; i < 12; i++) {
		const debris = new Instance("Part");
		debris.Name = "Debris";
		debris.Shape = Enum.PartType.Block;
		debris.Size = new Vector3(math.random(0.5, 2), math.random(0.5, 2), math.random(0.5, 2));
		debris.Color = Color3.fromRGB(150, 150, 150);
		debris.Material = Enum.Material.Concrete;
		debris.CanCollide = false;
		debris.Parent = Workspace;

		// Position debris around impact
		const debrisPos = position.add(new Vector3(math.random(-8, 8), math.random(0, 5), math.random(-8, 8)));
		debris.Position = debrisPos;

		// Add physics for realistic movement
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(4000, 4000, 4000);
		bodyVelocity.Velocity = direction
			.mul(math.random(15, 40))
			.add(new Vector3(math.random(-15, 15), math.random(10, 30), math.random(-15, 15)));
		bodyVelocity.Parent = debris;

		// Add rotation
		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(4000, 4000, 4000);
		bodyAngularVelocity.AngularVelocity = new Vector3(
			math.random(-10, 10),
			math.random(-10, 10),
			math.random(-10, 10),
		);
		bodyAngularVelocity.Parent = debris;

		// Remove debris after some time
		task.delay(4, () => {
			if (bodyVelocity.Parent) bodyVelocity.Destroy();
			if (bodyAngularVelocity.Parent) bodyAngularVelocity.Destroy();
			const fadeTween = TweenService.Create(debris, new TweenInfo(2, Enum.EasingStyle.Quad), { Transparency: 1 });
			fadeTween.Play();
			fadeTween.Completed.Connect(() => debris.Destroy());
		});
	}
}

export function createDimensionalCracks(position: Vector3): void {
	// Create large dimensional crack effects
	for (let i = 0; i < 6; i++) {
		const crack = new Instance("Part");
		crack.Name = "DimensionalCrack";
		crack.Shape = Enum.PartType.Block;
		crack.Size = new Vector3(0.1, math.random(15, 25), 0.1);
		crack.Color = Color3.fromRGB(255, 0, 255);
		crack.Material = Enum.Material.Neon;
		crack.Transparency = 0.2;
		crack.CanCollide = false;
		crack.Anchored = true;
		crack.Parent = Workspace;

		const angle = (i / 6) * math.pi * 2;
		const distance = math.random(3, 8);
		const crackPos = position.add(
			new Vector3(math.cos(angle) * distance, math.random(-5, 5), math.sin(angle) * distance),
		);

		crack.CFrame = new CFrame(crackPos).mul(CFrame.Angles(math.random(-0.5, 0.5), angle, math.random(-0.3, 0.3)));

		// Fade out after delay
		task.delay(5, () => {
			const fadeOut = TweenService.Create(crack, new TweenInfo(2), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => crack.Destroy());
		});
	}
}

export function createAtmosphericDistortion(position: Vector3, direction: Vector3): void {
	// Create atmospheric distortion waves
	for (let i = 0; i < 4; i++) {
		const wave = new Instance("Part");
		wave.Name = "AtmosphericWave";
		wave.Shape = Enum.PartType.Ball;
		wave.Size = new Vector3(1, 1, 1);
		wave.Color = Color3.fromRGB(200, 200, 255);
		wave.Material = Enum.Material.ForceField;
		wave.Transparency = 0.8;
		wave.CanCollide = false;
		wave.Anchored = true;
		wave.Parent = Workspace;
		wave.Position = position;

		const expandTween = TweenService.Create(
			wave,
			new TweenInfo(1.5 + i * 0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{ Size: new Vector3(20, 20, 20), Transparency: 1 },
		);
		expandTween.Play();
		expandTween.Completed.Connect(() => wave.Destroy());
	}
}

export function createEnergyLightning(position: Vector3): void {
	// Create reduced energy lightning effects (less overwhelming)
	for (let i = 0; i < 3; i++) {
		// Reduced from 8 to 3
		const lightning = new Instance("Part");
		lightning.Name = "EnergyLightning";
		lightning.Shape = Enum.PartType.Block;
		lightning.Size = new Vector3(0.15, math.random(6, 10), 0.15); // Smaller and shorter
		lightning.Color = Color3.fromRGB(255, 255, 150); // Slightly less intense yellow
		lightning.Material = Enum.Material.Neon;
		lightning.Transparency = 0.3; // More transparent (was 0.1)
		lightning.CanCollide = false;
		lightning.Anchored = true;
		lightning.Parent = Workspace;

		const lightningPos = position.add(
			new Vector3(
				math.random(-6, 6), // Smaller spread
				math.random(3, 8), // Lower height
				math.random(-6, 6),
			),
		);
		lightning.Position = lightningPos;

		// Reduced flicker effect
		for (let flicker = 0; flicker < 3; flicker++) {
			// Reduced from 5 to 3
			task.delay(flicker * 0.15, () => {
				lightning.Transparency = math.random(0.3, 0.7); // More subtle flicker
			});
		}

		task.delay(1.5, () => lightning.Destroy()); // Shorter duration
	}
}

export function createEnergyParticleStorm(position: Vector3): void {
	// Create energy particle storm
	for (let i = 0; i < 20; i++) {
		const particle = new Instance("Part");
		particle.Name = "EnergyParticle";
		particle.Shape = Enum.PartType.Ball;
		particle.Size = new Vector3(0.5, 0.5, 0.5);
		particle.Color = Color3.fromRGB(255, 150, 0);
		particle.Material = Enum.Material.Neon;
		particle.Transparency = 0.3;
		particle.CanCollide = false;
		particle.Anchored = true;
		particle.Parent = Workspace;

		const particlePos = position.add(new Vector3(math.random(-15, 15), math.random(-5, 10), math.random(-15, 15)));
		particle.Position = particlePos;

		// Floating animation
		const floatTween = TweenService.Create(
			particle,
			new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{ Position: particlePos.add(new Vector3(0, math.random(5, 10), 0)) },
		);
		floatTween.Play();

		task.delay(4, () => {
			const fadeOut = TweenService.Create(particle, new TweenInfo(1), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => particle.Destroy());
		});
	}
}

export function createRealityShatter(position: Vector3, direction: Vector3): void {
	// Create reality shatter effect
	const shatter = new Instance("Part");
	shatter.Name = "RealityShatter";
	shatter.Shape = Enum.PartType.Block;
	shatter.Size = new Vector3(15, 15, 0.1);
	shatter.Color = Color3.fromRGB(0, 0, 0);
	shatter.Material = Enum.Material.Neon;
	shatter.Transparency = 0.5;
	shatter.CanCollide = false;
	shatter.Anchored = true;
	shatter.Parent = Workspace;
	shatter.Position = position.add(direction.mul(5));

	// Create shatter pattern
	for (let i = 0; i < 12; i++) {
		const fragment = new Instance("Part");
		fragment.Name = "ShatterFragment";
		fragment.Shape = Enum.PartType.Block;
		fragment.Size = new Vector3(math.random(1, 3), math.random(1, 3), 0.1);
		fragment.Color = Color3.fromRGB(100, 0, 100);
		fragment.Material = Enum.Material.Neon;
		fragment.Transparency = 0.3;
		fragment.CanCollide = false;
		fragment.Anchored = true;
		fragment.Parent = Workspace;

		const fragmentPos = position.add(new Vector3(math.random(-8, 8), math.random(-8, 8), math.random(-2, 2)));
		fragment.Position = fragmentPos;

		task.delay(3, () => {
			const fadeOut = TweenService.Create(fragment, new TweenInfo(2), { Transparency: 1 });
			fadeOut.Play();
			fadeOut.Completed.Connect(() => fragment.Destroy());
		});
	}

	task.delay(4, () => {
		const fadeOut = TweenService.Create(shatter, new TweenInfo(2), { Transparency: 1 });
		fadeOut.Play();
		fadeOut.Completed.Connect(() => shatter.Destroy());
	});
}
