import { Players, ReplicatedStorage } from "@rbxts/services";
import { PlayerDataManager, DataStoreHelper } from "../../core";
import { DataStoreRequest, DataStoreResponse } from "../../core/data/interfaces/RemoteEventTypes";

export class ServerDataStoreService {
	private static instance: ServerDataStoreService;
	private playerDataManager: PlayerDataManager;
	private dataStoreHelper: DataStoreHelper;
	private remoteEvents!: Folder;

	private constructor() {
		this.playerDataManager = PlayerDataManager.getInstance();
		this.dataStoreHelper = DataStoreHelper.getInstance();
		this.setupRemoteEvents();
		this.setupPlayerEvents();
	}

	public static getInstance(): ServerDataStoreService {
		if (!ServerDataStoreService.instance) {
			ServerDataStoreService.instance = new ServerDataStoreService();
		}
		return ServerDataStoreService.instance;
	}

	private setupRemoteEvents(): void {
		// Create RemoteEvents folder if it doesn't exist
		let remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
		if (!remoteEventsFolder) {
			remoteEventsFolder = new Instance("Folder");
			remoteEventsFolder.Name = "RemoteEvents";
			remoteEventsFolder.Parent = ReplicatedStorage;
		}
		this.remoteEvents = remoteEventsFolder;

		// Create DataStore testing RemoteEvent
		const dataStoreEvent = new Instance("RemoteEvent");
		dataStoreEvent.Name = "DATA_STORE_TESTING";
		dataStoreEvent.Parent = this.remoteEvents;

		// Handle client requests
		dataStoreEvent.OnServerEvent.Connect((player, request: unknown) => {
			this.handleDataStoreRequest(player, request as DataStoreRequest);
		});
	}

	private setupPlayerEvents(): void {
		Players.PlayerAdded.Connect((player) => {
			// Player data is automatically loaded by PlayerDataManager
			print(`🔄 Player ${player.Name} joined - data loading handled automatically`);
		});

		Players.PlayerRemoving.Connect((player) => {
			// Player data is automatically saved by PlayerDataManager
			print(`💾 Player ${player.Name} leaving - data saving handled automatically`);
		});
	}

	private async handleDataStoreRequest(player: Player, request: DataStoreRequest): Promise<void> {
		try {
			// Basic input validation
			if (typeOf(request) !== "table") {
				const errorResponse: DataStoreResponse = {
					action: "error",
					result: { success: false, error: "Request must be a table" },
				};
				this.sendErrorResponse(player, errorResponse);
				return;
			}

			const { action, data } = request;

			// Validate action
			if (typeOf(action) !== "string") {
				const errorResponse: DataStoreResponse = {
					action: "error",
					result: { success: false, error: "Action must be a string" },
				};
				this.sendErrorResponse(player, errorResponse);
				return;
			}

			let result;

			switch (action) {
				case "loadPlayerData":
					result = await this.playerDataManager.onPlayerJoin(player);
					break;

				case "updateGameData": {
					const gameData = data as { gameDataUpdates?: Record<string, unknown> };
					result = await this.playerDataManager.updatePlayerGameData(
						player.UserId,
						gameData.gameDataUpdates || { testValue: tick() },
					);
					break;
				}

				case "updateSettings": {
					const settingsData = data as { musicVolume?: number; graphics?: string };
					result = await this.playerDataManager.updatePlayerSettings(player.UserId, {
						musicVolume: settingsData.musicVolume !== undefined ? settingsData.musicVolume : 0.6,
						graphics: (settingsData.graphics as "Low" | "Medium" | "High") || "High",
					});
					break;
				}

				case "getStats": {
					const cacheSize = this.dataStoreHelper.getCacheSize();
					const loadedPlayers = this.playerDataManager.getLoadedPlayerCount();
					const isPlayerLoaded = this.playerDataManager.isPlayerLoaded(player.UserId);

					result = {
						success: true,
						data: {
							cacheSize,
							loadedPlayers,
							isPlayerLoaded,
						},
					};
					break;
				}

				default: {
					result = { success: false, error: `Unknown action: ${action}` };
					break;
				}
			}

			// Send result back to client
			const responseEvent = this.remoteEvents.FindFirstChild("DATA_STORE_RESPONSE") as RemoteEvent;
			const response: DataStoreResponse = { action, result };

			if (!responseEvent) {
				const newResponseEvent = new Instance("RemoteEvent");
				newResponseEvent.Name = "DATA_STORE_RESPONSE";
				newResponseEvent.Parent = this.remoteEvents;
				newResponseEvent.FireClient(player, response);
			} else {
				responseEvent.FireClient(player, response);
			}
		} catch (error) {
			print(`❌ DataStore request error: ${error}`);

			// Send error response
			const responseEvent = this.remoteEvents.FindFirstChild("DATA_STORE_RESPONSE") as RemoteEvent;
			if (responseEvent) {
				const errorResponse: DataStoreResponse = {
					action: request.action,
					result: { success: false, error: tostring(error) },
				};
				responseEvent.FireClient(player, errorResponse);
			}
		}
	}

	private sendErrorResponse(player: Player, errorResponse: DataStoreResponse): void {
		const responseEvent = this.remoteEvents.FindFirstChild("DATA_STORE_RESPONSE") as RemoteEvent;
		if (!responseEvent) {
			const newResponseEvent = new Instance("RemoteEvent");
			newResponseEvent.Name = "DATA_STORE_RESPONSE";
			newResponseEvent.Parent = this.remoteEvents;
			newResponseEvent.FireClient(player, errorResponse);
		} else {
			responseEvent.FireClient(player, errorResponse);
		}
	}

	public cleanup(): void {
		this.playerDataManager.cleanup();
		this.dataStoreHelper.cleanup();
	}
}
