-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local exports = {}
-- Base data store types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "base", "DataStoreBase") or {} do
	exports[_k] = _v
end
-- Player-specific types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "player", "PlayerDataResults") or {} do
	exports[_k] = _v
end
-- Currency-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "currency", "CurrencyTypes") or {} do
	exports[_k] = _v
end
-- Progression-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "progression", "ProgressionTypes") or {} do
	exports[_k] = _v
end
-- Inventory-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "inventory", "InventoryTypes") or {} do
	exports[_k] = _v
end
-- Settings-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "settings", "SettingsTypes") or {} do
	exports[_k] = _v
end
-- Stats-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "stats", "StatsTypes") or {} do
	exports[_k] = _v
end
return exports
