import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";
import { ResponsiveManager } from "../layout/ResponsiveManager";
import { LoadingSpinner } from "../loading/LoadingIndicator";

interface ButtonProps {
	text: string;
	onClick: () => void;
	disabled?: boolean;
	loading?: boolean; // New loading state
	variant?: "primary" | "secondary" | "danger"; // New variants
	LayoutOrder?: number;
	size?: UDim2;
	autoSize?: boolean; // When true, button sizes to fit text content
	minWidth?: number; // Minimum width when using autoSize
	padding?: number; // Custom padding override
	responsive?: boolean; // Enable responsive sizing
	ariaLabel?: string; // New accessibility support
	tooltip?: string; // New tooltip support
}

export function Button(props: ButtonProps) {
	const [hovered, setHovered] = React.useState(false);
	const [pressed, setPressed] = React.useState(false);
	const [focused, setFocused] = React.useState(false);

	// Enhanced color system with variants
	const getBackgroundColor = () => {
		if (props.disabled) return COLORS.bg.secondary;
		if (props.loading) return COLORS.bg.surface;

		const variant = props.variant ?? "primary";
		
		if (pressed) {
			switch (variant) {
				case "primary": return COLORS["primary-dark"];
				case "secondary": return COLORS.bg["surface-pressed"];
				case "danger": return COLORS["error-dark"];
				default: return COLORS.bg["surface-pressed"];
			}
		}
		
		if (hovered || focused) {
			switch (variant) {
				case "primary": return COLORS["primary-light"];
				case "secondary": return COLORS.bg["surface-hover"];
				case "danger": return COLORS.error;
				default: return COLORS.bg["surface-hover"];
			}
		}

		switch (variant) {
			case "primary": return COLORS.primary;
			case "secondary": return COLORS.bg.surface;
			case "danger": return COLORS.error;
			default: return COLORS.bg.surface;
		}
	};

	const getTextColor = () => {
		if (props.disabled) return COLORS.text.secondary;
		if (props.loading) return COLORS.text.secondary;
		
		const variant = props.variant ?? "primary";
		switch (variant) {
			case "primary": return COLORS.bg.base; // Dark text on primary
			case "secondary": return COLORS.text.main;
			case "danger": return COLORS.text.main;
			default: return COLORS.text.main;
		}
	};

	const getBorderColor = () => {
		if (focused && !props.disabled) return COLORS.border.focus;
		return COLORS.border.l2;
	};

	// Responsive manager for dynamic sizing
	const responsiveManager = ResponsiveManager.getInstance();

	// Smart sizing: use autoSize if specified or no size provided
	const useAutoSize = props.autoSize ?? props.size === undefined;

	// Calculate responsive dimensions
	let minWidth = props.minWidth ?? SIZES.button.width;
	let buttonHeight = SIZES.button.height;
	let padding = props.padding ?? SIZES.padding;

	if (props.responsive) {
		const deviceType = responsiveManager.getDeviceType();
		const scaleFactor = deviceType === "mobile" ? 0.9 : deviceType === "tablet" ? 0.95 : 1.0;

		minWidth = minWidth * scaleFactor;
		buttonHeight = buttonHeight * scaleFactor;
		padding = responsiveManager.getResponsiveMargin(padding);
	}

	// Calculate size based on autoSize setting
	const size =
		props.size ?? (useAutoSize ? new UDim2(0, minWidth, 0, buttonHeight) : new UDim2(0, minWidth, 0, buttonHeight));

	// Display text with loading state
	const displayText = props.loading ? "Loading..." : props.text;

	return (
		<textbutton
			Text={displayText}
			TextColor3={Color3.fromHex(getTextColor())}
			BackgroundColor3={Color3.fromHex(getBackgroundColor())}
			Size={size}
			LayoutOrder={props.LayoutOrder}
			Font={TYPOGRAPHY.font}
			TextSize={SIZES.fontSize}
			AutoButtonColor={false}
			AutomaticSize={useAutoSize ? Enum.AutomaticSize.X : Enum.AutomaticSize.None}
			Event={{
				Activated: () => {
					if (!props.disabled && !props.loading) {
						props.onClick();
					}
				},
				MouseEnter: () => {
					if (!props.disabled && !props.loading) {
						setHovered(true);
					}
				},
				MouseLeave: () => {
					setHovered(false);
					setPressed(false);
				},
				MouseButton1Down: () => {
					if (!props.disabled && !props.loading) {
						setPressed(true);
					}
				},
				MouseButton1Up: () => {
					setPressed(false);
				},
				SelectionGained: () => {
					setFocused(true);
				},
				SelectionLost: () => {
					setFocused(false);
				},
			}}
			BorderSizePixel={0}
			Selectable={!props.disabled && !props.loading}
		>
			{/* Rounded corners */}
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />

			{/* Enhanced border with focus state */}
			<uistroke 
				Color={Color3.fromHex(getBorderColor())} 
				Thickness={focused ? 2 : 1} 
				Transparency={props.disabled ? 0.5 : 0} 
			/>

			{/* Padding for auto-sizing */}
			{useAutoSize && <uipadding PaddingLeft={new UDim(0, padding)} PaddingRight={new UDim(0, padding)} />}

			{/* Enhanced loading spinner */}
			{props.loading && (
				<frame
					Size={new UDim2(0, 20, 0, 20)}
					Position={new UDim2(0, padding / 2, 0.5, 0)}
					AnchorPoint={new Vector2(0, 0.5)}
					BackgroundTransparency={1}
				>
					<LoadingSpinner 
						size={16} 
						color={getTextColor()} 
						speed={1}
					/>
				</frame>
			)}
		</textbutton>
	);
}
