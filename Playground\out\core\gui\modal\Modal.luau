-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local IconButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").IconButton
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local _frame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame")
local ContainerFrame = _frame.ContainerFrame
local HorizontalFrame = _frame.HorizontalFrame
local Overlay = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay").Overlay
local function Modal(props)
	local isAnimating, setIsAnimating = React.useState(false)
	local _condition = props.width
	if _condition == nil then
		_condition = 300
	end
	local width = _condition
	local _condition_1 = props.height
	if _condition_1 == nil then
		_condition_1 = 200
	end
	local height = _condition_1
	-- Handle open/close animations
	React.useEffect(function()
		if props.isOpen then
			setIsAnimating(true)
		end
	end, { props.isOpen })
	local handleClose = function()
		setIsAnimating(false)
		-- Delay actual close to allow animation
		task.delay(0.2, function()
			props.onClose()
		end)
	end
	if not props.isOpen and not isAnimating then
		return React.createElement(React.Fragment)
	end
	local modalScale = if isAnimating and props.isOpen then 1 else 0.8
	local modalTransparency = if isAnimating and props.isOpen then 0 else 0.3
	return React.createElement(Overlay, {
		onBackdropClick = handleClose,
		backgroundColor = Color3.fromHex(COLORS.bg.modal),
	}, React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(COLORS.bg.base),
		Size = UDim2.new(modalScale, width * (1 - modalScale), modalScale, height * (1 - modalScale)),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		ZIndex = 12,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		BackgroundTransparency = modalTransparency,
		Event = {
			Activated = function()
				-- Stop propagation by doing nothing - this prevents backdrop click
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l2),
		Thickness = 1,
	}), React.createElement("frame", {
		Size = UDim2.new(1, 4, 1, 4),
		Position = UDim2.new(0, 2, 0, 2),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromHex(COLORS.shadow.lg),
		BackgroundTransparency = 0.6,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	})), React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, 0),
		padding = 0,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, React.createElement(HorizontalFrame, {
		backgroundColor = COLORS.bg.secondary,
		size = UDim2.new(1, 0, 0, 40),
		backgroundTransparency = 0,
		padding = SIZES.padding,
		spacing = 0,
		horizontalAlignment = Enum.HorizontalAlignment.Left,
		verticalAlignment = Enum.VerticalAlignment.Center,
	}, React.createElement(Label, {
		text = props.title,
		fontSize = SIZES.fontSize + 2,
		size = UDim2.new(1, -50, 1, 0),
		bold = true,
		layoutOrder = 1,
	}), React.createElement(IconButton, {
		icon = "X",
		onClick = handleClose,
		layoutOrder = 2,
		size = UDim2.new(0, 30, 0, 30),
	})), React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, -40),
		position = UDim2.new(0, 0, 0, 40),
		padding = SIZES.padding,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, props.children))))
end
return {
	Modal = Modal,
}
