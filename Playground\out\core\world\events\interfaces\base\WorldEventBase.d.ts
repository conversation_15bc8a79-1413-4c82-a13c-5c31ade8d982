export type WorldEventType = "earthquake" | "tsunami" | "meteor" | "lightning_storm" | "blizzard" | "volcanic_eruption" | "gravity_shift" | "time_dilation" | "dimensional_rift" | "energy_surge";
export interface WorldEventOptions {
    eventType: WorldEventType;
    center?: Vector3;
    radius?: number;
    duration?: number;
    intensity: number;
    affectedPlayers?: "all" | "nearby" | "specific";
    specificPlayers?: Player[];
    visualEffects?: boolean;
    soundEffects?: boolean;
    environmentalChanges?: boolean;
}
export interface WorldEvent {
    id: string;
    options: WorldEventOptions;
    startTime: number;
    endTime?: number;
    status: "pending" | "active" | "completed" | "cancelled";
    affectedRegions: Region3[];
    participants: Set<Player>;
}
export interface EventPhase {
    name: string;
    startTime: number;
    duration: number;
    effects: EventEffect[];
}
export interface EventEffect {
    type: "visual" | "audio" | "physics" | "environmental";
    data: unknown;
    delay?: number;
    duration?: number;
}
