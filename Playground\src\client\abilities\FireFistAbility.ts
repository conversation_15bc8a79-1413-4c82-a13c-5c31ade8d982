import { AbilityBase } from "./AbilityBase";
import { TweenService, RunService, Workspace, Players, Lighting } from "@rbxts/services";

export class FireFistAbility extends AbilityBase {
	private fireEffects: Part[] = [];
	private fireConnection?: RBXScriptConnection;
	private cooldownEndTime = 0;
	private isActive = false;

	constructor() {
		super("FIRE_FIST", 8);
	}

	public isOnCooldown(): boolean {
		return tick() < this.cooldownEndTime;
	}

	private startCooldown(): void {
		this.cooldownEndTime = tick() + this.getCooldownTime();
	}

	public activate(): void {
		if (this.isOnCooldown() || this.isActive) return;

		const player = Players.LocalPlayer;
		const character = player.Character;
		if (!character) return;

		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
		const rightHand = character.FindFirstChild("RightHand") as Part;
		if (!humanoidRootPart || !rightHand) return;

		print("🔥 Activating Ace's Fire Fist!");
		this.isActive = true;

		// Phase 1: Character fire preparation animation
		this.createFireFistAnimation(character);

		// Phase 2: Character fire aura
		this.createCharacterFireAura(character);

		// Phase 2: Create fire fist charging effect
		this.createFireFistCharge(rightHand);

		// Phase 3: Launch the massive fire projectile
		task.delay(1.5, () => {
			this.launchFireFist(humanoidRootPart);
		});

		// Phase 4: Create environmental fire effects
		this.createEnvironmentalFire();

		// Duration: 5 seconds
		task.delay(5, () => {
			this.cleanupEffects();
			this.isActive = false;
		});

		this.startCooldown();
	}

	private createFireFistAnimation(character: Model): void {
		print("🔥 Creating Ace's Fire Fist animation");

		const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
		if (!humanoid) return;

		// Create proper Motor6D animation like QuakeAbility
		this.createCharacterAnimation(character);
	}

	private createCharacterAnimation(character: Model): void {
		const torso = character.FindFirstChild("Torso") as Part;
		const upperTorso = character.FindFirstChild("UpperTorso") as Part;

		if (torso) {
			// R6 character
			this.animateR6Character(torso);
		} else if (upperTorso) {
			// R15 character
			this.animateR15Character(upperTorso);
		}
	}

	private animateR6Character(torso: Part): void {
		const rightShoulder = torso.FindFirstChild("Right Shoulder") as Motor6D;
		const leftShoulder = torso.FindFirstChild("Left Shoulder") as Motor6D;
		const rightHip = torso.FindFirstChild("Right Hip") as Motor6D;
		const leftHip = torso.FindFirstChild("Left Hip") as Motor6D;

		if (!rightShoulder || !leftShoulder) return;

		// Store original C0 values
		const originalRightC0 = rightShoulder.C0;
		const originalLeftC0 = leftShoulder.C0;
		const originalRightHipC0 = rightHip ? rightHip.C0 : undefined;
		const originalLeftHipC0 = leftHip ? leftHip.C0 : undefined;

		// Phase 1: Fire stance (0-0.5s) - Defensive position
		print("🔥 Fire stance preparation");

		const rightArmStance = TweenService.Create(
			rightShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalRightC0.mul(CFrame.Angles(-math.pi / 6, 0, -math.pi / 8)),
			},
		);
		const leftArmStance = TweenService.Create(
			leftShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 6, 0, math.pi / 8)),
			},
		);

		rightArmStance.Play();
		leftArmStance.Play();

		// Phase 2: Charging pose (0.5-1.5s) - Right fist back, left forward
		task.delay(0.5, () => {
			print("👊 Fire Fist charging");

			const rightArmCharge = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 3, 0, -math.pi / 2)),
				},
			);
			const leftArmBalance = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 4, 0, math.pi / 4)),
				},
			);

			rightArmCharge.Play();
			leftArmBalance.Play();

			// Fighting stance with legs
			if (rightHip && originalRightHipC0) {
				const rightLegStance = TweenService.Create(
					rightHip,
					new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalRightHipC0.mul(CFrame.Angles(math.pi / 12, 0, 0)),
					},
				);
				rightLegStance.Play();
			}
		});

		// Phase 3: Fire Fist punch (1.5-2s) - Dramatic forward punch
		task.delay(1.5, () => {
			print("🚀 Fire Fist punch!");

			const rightArmPunch = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 2, 0, 0)),
				},
			);

			rightArmPunch.Play();
		});

		// Phase 4: Follow through (2-3s) - Extended punch pose
		task.delay(2, () => {
			print("💥 Fire Fist follow through");

			const rightArmExtend = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 2, 0, -math.pi / 6)),
				},
			);
			const leftArmBack = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{
					C0: originalLeftC0.mul(CFrame.Angles(math.pi / 6, 0, math.pi / 3)),
				},
			);

			rightArmExtend.Play();
			leftArmBack.Play();
		});

		// Phase 5: Return to normal (4s) - Restore all joints
		task.delay(4, () => {
			print("🔥 Returning to normal stance");

			const rightArmReturn = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalRightC0,
				},
			);
			const leftArmReturn = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0,
				},
			);

			rightArmReturn.Play();
			leftArmReturn.Play();

			// Restore legs
			if (rightHip && originalRightHipC0) {
				const rightLegReturn = TweenService.Create(
					rightHip,
					new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						C0: originalRightHipC0,
					},
				);
				rightLegReturn.Play();
			}
		});
	}

	private animateR15Character(upperTorso: Part): void {
		const rightShoulder = upperTorso.FindFirstChild("RightShoulder") as Motor6D;
		const leftShoulder = upperTorso.FindFirstChild("LeftShoulder") as Motor6D;

		if (!rightShoulder || !leftShoulder) return;

		// Store original C0 values
		const originalRightC0 = rightShoulder.C0;
		const originalLeftC0 = leftShoulder.C0;

		// Similar animation sequence for R15
		print("🔥 R15 Fire Fist animation");

		// Phase 1: Stance
		const rightArmStance = TweenService.Create(
			rightShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalRightC0.mul(CFrame.Angles(-math.pi / 6, 0, -math.pi / 8)),
			},
		);
		const leftArmStance = TweenService.Create(
			leftShoulder,
			new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 6, 0, math.pi / 8)),
			},
		);

		rightArmStance.Play();
		leftArmStance.Play();

		// Phase 2: Charging (0.5s delay)
		task.delay(0.5, () => {
			const rightArmCharge = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 3, 0, -math.pi / 2)),
				},
			);
			const leftArmBalance = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0.mul(CFrame.Angles(-math.pi / 4, 0, math.pi / 4)),
				},
			);

			rightArmCharge.Play();
			leftArmBalance.Play();
		});

		// Phase 3: Punch (1.5s delay)
		task.delay(1.5, () => {
			const rightArmPunch = TweenService.Create(
				rightShoulder,
				new TweenInfo(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 2, 0, 0)),
				},
			);
			rightArmPunch.Play();
		});

		// Phase 4: Follow through (2s delay)
		task.delay(2, () => {
			const rightArmExtend = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{
					C0: originalRightC0.mul(CFrame.Angles(-math.pi / 2, 0, -math.pi / 6)),
				},
			);
			const leftArmBack = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{
					C0: originalLeftC0.mul(CFrame.Angles(math.pi / 6, 0, math.pi / 3)),
				},
			);

			rightArmExtend.Play();
			leftArmBack.Play();
		});

		// Phase 5: Return (4s delay)
		task.delay(4, () => {
			const rightArmReturn = TweenService.Create(
				rightShoulder,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalRightC0,
				},
			);
			const leftArmReturn = TweenService.Create(
				leftShoulder,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					C0: originalLeftC0,
				},
			);

			rightArmReturn.Play();
			leftArmReturn.Play();
		});
	}

	private createCharacterFireAura(character: Model): void {
		print("🔥 Creating Ace's fire aura");

		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
		if (!humanoidRootPart) return;

		// Create fire aura around character
		const fireAura = new Instance("Part");
		fireAura.Name = "FireAura";
		fireAura.Shape = Enum.PartType.Ball;
		fireAura.Size = new Vector3(10, 10, 10);
		fireAura.Color = Color3.fromRGB(255, 100, 0); // Bright orange fire
		fireAura.Material = Enum.Material.Neon;
		fireAura.Transparency = 0.5;
		fireAura.CanCollide = false;
		fireAura.Anchored = true;
		fireAura.Position = humanoidRootPart.Position;
		fireAura.Parent = Workspace;
		this.fireEffects.push(fireAura);

		// Add intense fire light
		const auraLight = new Instance("PointLight");
		auraLight.Color = Color3.fromRGB(255, 150, 0);
		auraLight.Brightness = 6;
		auraLight.Range = 30;
		auraLight.Parent = fireAura;

		// Flickering fire effect
		const flickerTween = TweenService.Create(
			fireAura,
			new TweenInfo(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
			{
				Size: new Vector3(12, 12, 12),
				Transparency: 0.3,
			},
		);
		flickerTween.Play();

		// Follow character
		const followConnection = RunService.Heartbeat.Connect(() => {
			if (humanoidRootPart.Parent && fireAura.Parent) {
				fireAura.Position = humanoidRootPart.Position;
			}
		});

		// Create fire particles around character
		this.createFireParticles(fireAura);

		// Cleanup after duration
		task.delay(5, () => {
			followConnection.Disconnect();
			flickerTween.Cancel();

			const fadeTween = TweenService.Create(fireAura, new TweenInfo(1, Enum.EasingStyle.Quad), {
				Transparency: 1,
			});
			fadeTween.Play();
			fadeTween.Completed.Connect(() => {
				fireAura.Destroy();
				const index = this.fireEffects.indexOf(fireAura);
				if (index > -1) {
					this.fireEffects.remove(index);
				}
			});
		});
	}

	private createFireParticles(parent: Part): void {
		// Create attachment for particles
		const attachment = new Instance("Attachment");
		attachment.Name = "FireAttachment";
		attachment.Parent = parent;

		// Create fire particle emitter
		const fireParticles = new Instance("ParticleEmitter");
		fireParticles.Name = "FireParticles";
		fireParticles.Texture = "rbxasset://textures/particles/fire_main.dds";
		fireParticles.Color = new ColorSequence([
			new ColorSequenceKeypoint(0, Color3.fromRGB(255, 255, 0)), // Yellow
			new ColorSequenceKeypoint(0.5, Color3.fromRGB(255, 100, 0)), // Orange
			new ColorSequenceKeypoint(1, Color3.fromRGB(255, 0, 0)), // Red
		]);
		fireParticles.Size = new NumberSequence(1, 3);
		fireParticles.Lifetime = new NumberRange(0.5, 1.5);
		fireParticles.Rate = 100;
		fireParticles.SpreadAngle = new Vector2(45, 45);
		fireParticles.Speed = new NumberRange(5, 15);
		fireParticles.Parent = attachment;

		// Stop particles after duration
		task.delay(5, () => {
			fireParticles.Enabled = false;
			task.delay(2, () => {
				if (attachment.Parent) {
					attachment.Destroy();
				}
			});
		});
	}

	private createFireFistCharge(rightHand: Part): void {
		print("👊 Creating Fire Fist charging effect");

		// Create charging fire sphere in hand
		const chargeSphere = new Instance("Part");
		chargeSphere.Name = "FireFistCharge";
		chargeSphere.Shape = Enum.PartType.Ball;
		chargeSphere.Size = new Vector3(2, 2, 2);
		chargeSphere.Color = Color3.fromRGB(255, 200, 0); // Bright yellow-orange
		chargeSphere.Material = Enum.Material.Neon;
		chargeSphere.Transparency = 0.2;
		chargeSphere.CanCollide = false;
		chargeSphere.Anchored = true;
		chargeSphere.Position = rightHand.Position;
		chargeSphere.Parent = Workspace;
		this.fireEffects.push(chargeSphere);

		// Add intense light
		const chargeLight = new Instance("PointLight");
		chargeLight.Color = Color3.fromRGB(255, 150, 0);
		chargeLight.Brightness = 8;
		chargeLight.Range = 20;
		chargeLight.Parent = chargeSphere;

		// Growing charge animation
		const chargeTween = TweenService.Create(
			chargeSphere,
			new TweenInfo(1.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{
				Size: new Vector3(6, 6, 6),
				Transparency: 0.1,
			},
		);
		chargeTween.Play();

		// Follow hand
		const followConnection = RunService.Heartbeat.Connect(() => {
			if (rightHand.Parent && chargeSphere.Parent) {
				chargeSphere.Position = rightHand.Position;
			}
		});

		// Create charge particles
		this.createFireParticles(chargeSphere);

		// Remove after charge time
		task.delay(1.5, () => {
			followConnection.Disconnect();
			chargeSphere.Destroy();
			const index = this.fireEffects.indexOf(chargeSphere);
			if (index > -1) {
				this.fireEffects.remove(index);
			}
		});
	}

	private launchFireFist(humanoidRootPart: Part): void {
		print("🚀 Launching Fire Fist projectile!");

		const startPosition = humanoidRootPart.Position.add(humanoidRootPart.CFrame.LookVector.mul(3));
		const direction = humanoidRootPart.CFrame.LookVector;

		// Create massive fire projectile
		const fireFist = new Instance("Part");
		fireFist.Name = "FireFistProjectile";
		fireFist.Shape = Enum.PartType.Ball;
		fireFist.Size = new Vector3(8, 8, 8);
		fireFist.Color = Color3.fromRGB(255, 100, 0);
		fireFist.Material = Enum.Material.Neon;
		fireFist.Transparency = 0.1;
		fireFist.CanCollide = false;
		fireFist.Anchored = true;
		fireFist.Position = startPosition;
		fireFist.Parent = Workspace;
		this.fireEffects.push(fireFist);

		// Add massive fire light
		const projectileLight = new Instance("PointLight");
		projectileLight.Color = Color3.fromRGB(255, 150, 0);
		projectileLight.Brightness = 10;
		projectileLight.Range = 40;
		projectileLight.Parent = fireFist;

		// Create fire trail particles
		this.createFireParticles(fireFist);

		// Projectile movement
		const speed = 80;
		const maxDistance = 150;
		let travelDistance = 0;

		const moveConnection = RunService.Heartbeat.Connect((deltaTime) => {
			if (!fireFist.Parent) {
				moveConnection.Disconnect();
				return;
			}

			const moveDistance = speed * deltaTime;
			travelDistance += moveDistance;

			if (travelDistance >= maxDistance) {
				// Create explosion at max range
				this.createFireExplosion(fireFist.Position);
				moveConnection.Disconnect();
				fireFist.Destroy();
				return;
			}

			// Move projectile
			fireFist.Position = fireFist.Position.add(direction.mul(moveDistance));

			// Check for collision with objects
			this.checkFireProjectileCollision(fireFist);
		});

		// Safety cleanup after 3 seconds
		task.delay(3, () => {
			if (moveConnection.Connected) {
				moveConnection.Disconnect();
			}
			if (fireFist.Parent) {
				this.createFireExplosion(fireFist.Position);
				fireFist.Destroy();
			}
		});
	}

	private checkFireProjectileCollision(projectile: Part): void {
		// Simple collision detection with raycasting
		const raycast = Workspace.Raycast(
			projectile.Position,
			projectile.CFrame.LookVector.mul(5),
			new RaycastParams(),
		);

		if (raycast && raycast.Instance) {
			const hitPart = raycast.Instance;

			// Skip if it's our own fire effects or player characters
			if (
				hitPart.Name.find("Fire")[0] !== undefined ||
				Players.GetPlayerFromCharacter(hitPart.Parent as Model) !== undefined
			) {
				return;
			}

			// Create explosion at hit point
			this.createFireExplosion(raycast.Position);

			// Remove projectile
			projectile.Destroy();
			const index = this.fireEffects.indexOf(projectile);
			if (index > -1) {
				this.fireEffects.remove(index);
			}
		}
	}

	private createFireExplosion(position: Vector3): void {
		print("💥 Creating Fire Fist explosion!");

		// Create explosion sphere
		const explosion = new Instance("Part");
		explosion.Name = "FireExplosion";
		explosion.Shape = Enum.PartType.Ball;
		explosion.Size = new Vector3(5, 5, 5);
		explosion.Color = Color3.fromRGB(255, 200, 0);
		explosion.Material = Enum.Material.Neon;
		explosion.Transparency = 0.2;
		explosion.CanCollide = false;
		explosion.Anchored = true;
		explosion.Position = position;
		explosion.Parent = Workspace;
		this.fireEffects.push(explosion);

		// Add explosion light
		const explosionLight = new Instance("PointLight");
		explosionLight.Color = Color3.fromRGB(255, 150, 0);
		explosionLight.Brightness = 15;
		explosionLight.Range = 50;
		explosionLight.Parent = explosion;

		// Explosion expansion
		const explosionTween = TweenService.Create(
			explosion,
			new TweenInfo(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				Size: new Vector3(25, 25, 25),
				Transparency: 0.8,
			},
		);
		explosionTween.Play();

		// Create explosion particles
		this.createFireParticles(explosion);

		// Create fire rings
		this.createFireRings(position);

		// Remove explosion after animation
		explosionTween.Completed.Connect(() => {
			explosion.Destroy();
			const index = this.fireEffects.indexOf(explosion);
			if (index > -1) {
				this.fireEffects.remove(index);
			}
		});
	}

	private createFireRings(position: Vector3): void {
		// Create expanding fire rings
		for (let i = 0; i < 3; i++) {
			task.delay(i * 0.2, () => {
				const fireRing = new Instance("Part");
				fireRing.Name = "FireRing";
				fireRing.Shape = Enum.PartType.Cylinder;
				fireRing.Size = new Vector3(0.5, 8, 8);
				fireRing.Color = Color3.fromRGB(255, 100, 0);
				fireRing.Material = Enum.Material.Neon;
				fireRing.Transparency = 0.4;
				fireRing.CanCollide = false;
				fireRing.Anchored = true;
				fireRing.Position = position.add(new Vector3(0, 1, 0));
				fireRing.CFrame = fireRing.CFrame.mul(CFrame.Angles(0, 0, math.pi / 2));
				fireRing.Parent = Workspace;
				this.fireEffects.push(fireRing);

				// Expand ring
				const ringTween = TweenService.Create(
					fireRing,
					new TweenInfo(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{
						Size: new Vector3(0.5, 40, 40),
						Transparency: 1,
					},
				);
				ringTween.Play();
				ringTween.Completed.Connect(() => {
					fireRing.Destroy();
					const index = this.fireEffects.indexOf(fireRing);
					if (index > -1) {
						this.fireEffects.remove(index);
					}
				});
			});
		}
	}

	private createEnvironmentalFire(): void {
		print("🔥 Creating environmental fire effects");

		// Create fire atmosphere
		this.createFireAtmosphere();

		// Create floating fire orbs
		this.createFloatingFireOrbs();

		// Create ground fire patches
		this.createGroundFirePatches();
	}

	private createFireAtmosphere(): void {
		// Change lighting to warm fire atmosphere
		const originalAmbient = Lighting.Ambient;
		const originalOutdoorAmbient = Lighting.OutdoorAmbient;
		const originalBrightness = Lighting.Brightness;

		const lightingTween = TweenService.Create(
			Lighting,
			new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				Ambient: Color3.fromRGB(200, 100, 50), // Warm fire ambient
				OutdoorAmbient: Color3.fromRGB(220, 120, 60), // Warm fire outdoor
				Brightness: 2, // Brighter for fire glow
			},
		);
		lightingTween.Play();

		// Restore lighting after duration
		task.delay(4, () => {
			const restoreLightingTween = TweenService.Create(
				Lighting,
				new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{
					Ambient: originalAmbient,
					OutdoorAmbient: originalOutdoorAmbient,
					Brightness: originalBrightness,
				},
			);
			restoreLightingTween.Play();
		});
	}

	private createFloatingFireOrbs(): void {
		// Create floating fire orbs around the area
		for (let i = 0; i < 10; i++) {
			task.delay(i * 0.3, () => {
				const randomPosition = new Vector3(math.random(-50, 50), math.random(5, 25), math.random(-50, 50));

				const fireOrb = new Instance("Part");
				fireOrb.Name = "FloatingFireOrb";
				fireOrb.Shape = Enum.PartType.Ball;
				fireOrb.Size = new Vector3(math.random(2, 4), math.random(2, 4), math.random(2, 4));
				fireOrb.Color = Color3.fromRGB(255, math.random(50, 150), 0);
				fireOrb.Material = Enum.Material.Neon;
				fireOrb.Transparency = 0.3;
				fireOrb.CanCollide = false;
				fireOrb.Anchored = true;
				fireOrb.Position = randomPosition;
				fireOrb.Parent = Workspace;
				this.fireEffects.push(fireOrb);

				// Add fire light
				const orbLight = new Instance("PointLight");
				orbLight.Color = Color3.fromRGB(255, 150, 0);
				orbLight.Brightness = 3;
				orbLight.Range = 15;
				orbLight.Parent = fireOrb;

				// Floating animation
				const floatTween = TweenService.Create(
					fireOrb,
					new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
					{
						Position: randomPosition.add(new Vector3(0, 5, 0)),
					},
				);
				floatTween.Play();

				// Flickering effect
				const flickerTween = TweenService.Create(
					fireOrb,
					new TweenInfo(0.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
					{ Transparency: 0.6 },
				);
				flickerTween.Play();

				// Create fire particles
				this.createFireParticles(fireOrb);
			});
		}
	}

	private createGroundFirePatches(): void {
		// Create fire patches on the ground
		for (let i = 0; i < 8; i++) {
			task.delay(i * 0.4, () => {
				const randomPosition = new Vector3(math.random(-40, 40), 0.5, math.random(-40, 40));

				const firePatch = new Instance("Part");
				firePatch.Name = "GroundFirePatch";
				firePatch.Shape = Enum.PartType.Cylinder;
				firePatch.Size = new Vector3(1, math.random(6, 12), math.random(6, 12));
				firePatch.Color = Color3.fromRGB(255, math.random(80, 120), 0);
				firePatch.Material = Enum.Material.Neon;
				firePatch.Transparency = 0.4;
				firePatch.CanCollide = false;
				firePatch.Anchored = true;
				firePatch.Position = randomPosition;
				firePatch.CFrame = firePatch.CFrame.mul(CFrame.Angles(0, 0, math.pi / 2));
				firePatch.Parent = Workspace;
				this.fireEffects.push(firePatch);

				// Add ground fire light
				const patchLight = new Instance("PointLight");
				patchLight.Color = Color3.fromRGB(255, 150, 0);
				patchLight.Brightness = 4;
				patchLight.Range = 20;
				patchLight.Parent = firePatch;

				// Flickering fire animation
				const patchFlicker = TweenService.Create(
					firePatch,
					new TweenInfo(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
					{
						Size: firePatch.Size.add(new Vector3(0.2, 1, 1)),
						Transparency: 0.6,
					},
				);
				patchFlicker.Play();

				// Create fire particles
				this.createFireParticles(firePatch);
			});
		}
	}

	private cleanupEffects(): void {
		print("🧹 Cleaning up Fire Fist effects");

		// Clean up all fire effects
		for (const effect of this.fireEffects) {
			if (effect.Parent) {
				// Fade out effect
				const fadeTween = TweenService.Create(effect, new TweenInfo(1, Enum.EasingStyle.Quad), {
					Transparency: 1,
				});
				fadeTween.Play();
				fadeTween.Completed.Connect(() => effect.Destroy());
			}
		}
		this.fireEffects = [];

		// Disconnect any remaining connections
		if (this.fireConnection) {
			this.fireConnection.Disconnect();
			this.fireConnection = undefined;
		}
	}
}
