import * as React from "@rbxts/react";
import { Button, VerticalFrame, ClientState, useUIState } from "../../core";
import { WorldTestingPanel } from "./WorldTestingPanel";
import { DebugPanel } from "./DebugPanel";
import { ResponsiveManager } from "../../core/gui/layout/ResponsiveManager";

interface BottomLeftGridProps {
	onTestClick: () => void;
	onHelloClick: () => void;
}

export function BottomLeftGrid(_props: BottomLeftGridProps) {
	// Use Core state management instead of local state
	const worldTestingState = useUIState("worldTestingPanel");
	const debugState = useUIState("debugPanel");
	const [debugPanelOpen, setDebugPanelOpen] = React.useState(false);

	// Get responsive manager for dynamic positioning (memoized)
	const responsiveManager = React.useMemo(() => ResponsiveManager.getInstance(), []);
	
	// Memoize expensive calculations
	const responsiveSettings = React.useMemo(() => {
		const safeAreaInsets = responsiveManager.getSafeAreaInsets();
		const containerWidth = responsiveManager.isMobile() ? 100 : 120;
		const containerHeight = responsiveManager.isMobile() ? 160 : 200;
		const marginLeft = responsiveManager.getResponsiveMargin(16);
		const marginBottom = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.bottom;
		
		return {
			containerWidth,
			containerHeight,
			marginLeft,
			marginBottom,
		};
	}, [responsiveManager]);

	// Memoize button handlers
	const handleWorldClick = React.useCallback(() => {
		ClientState.updateUI("worldTestingPanel", { isOpen: true });
	}, []);

	const handleDebugClick = React.useCallback(() => {
		setDebugPanelOpen(true);
	}, []);

	// Memoize frame props
	const frameProps = React.useMemo(() => ({
		backgroundTransparency: 1,
		size: new UDim2(0, responsiveSettings.containerWidth, 0, responsiveSettings.containerHeight),
		position: new UDim2(0, responsiveSettings.marginLeft, 1, -responsiveSettings.marginBottom),
		anchorPoint: new Vector2(0, 1),
		spacing: 8,
		padding: 0,
		responsive: true,
		responsiveMargin: true,
	}), [responsiveSettings]);

	return (
		<>
			<VerticalFrame {...frameProps}>
				<Button
					text="🌍 World"
					variant="primary"
					onClick={handleWorldClick}
					LayoutOrder={5}
					responsive={true}
				/>

				<Button 
					text="🔧 Debug" 
					variant="secondary"
					onClick={handleDebugClick}
					LayoutOrder={6}
					responsive={true}
				/>
			</VerticalFrame>

			<WorldTestingPanel />

			<DebugPanel isOpen={debugPanelOpen} onClose={() => setDebugPanelOpen(false)} />
		</>
	);
}
