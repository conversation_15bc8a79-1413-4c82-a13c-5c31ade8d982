-- Compiled with roblox-ts v3.0.0
local PatrolBehavior
do
	PatrolBehavior = setmetatable({}, {
		__tostring = function()
			return "PatrolBehavior"
		end,
	})
	PatrolBehavior.__index = PatrolBehavior
	function PatrolBehavior.new(...)
		local self = setmetatable({}, PatrolBehavior)
		return self:constructor(...) or self
	end
	function PatrolBehavior:constructor()
		self.name = "Patrol"
		self.priority = 3
	end
	function PatrolBehavior:canExecute(context)
		return not context.target
	end
	function PatrolBehavior:execute(context)
		local patrolPoints = (context.blackboard.patrolPoints) or {}
		local _condition = (context.blackboard.currentPatrolIndex)
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local currentPatrolIndex = _condition
		if #patrolPoints == 0 then
			self:generatePatrolPoints(context)
			return {
				success = true,
				completed = false,
			}
		end
		local targetPoint = patrolPoints[currentPatrolIndex + 1]
		local distance = (context.position - targetPoint).Magnitude
		if distance <= 5 then
			context.blackboard.currentPatrolIndex = (currentPatrolIndex + 1) % #patrolPoints
		else
			self:moveTowards(context, targetPoint)
		end
		return {
			success = true,
			completed = false,
		}
	end
	function PatrolBehavior:onEnter(context)
		print(`🚶 {context.entityId} is patrolling`)
	end
	function PatrolBehavior:generatePatrolPoints(context)
		local patrolPoints = {}
		local basePosition = context.position
		local radius = 20
		for i = 0, 3 do
			local angle = (i / 4) * math.pi * 2
			local x = basePosition.X + math.cos(angle) * radius
			local z = basePosition.Z + math.sin(angle) * radius
			local _vector3 = Vector3.new(x, basePosition.Y, z)
			table.insert(patrolPoints, _vector3)
		end
		context.blackboard.patrolPoints = patrolPoints
		context.blackboard.currentPatrolIndex = 0
	end
	function PatrolBehavior:moveTowards(context, targetPosition)
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(targetPosition)
			end
		end
	end
end
return {
	PatrolBehavior = PatrolBehavior,
}
