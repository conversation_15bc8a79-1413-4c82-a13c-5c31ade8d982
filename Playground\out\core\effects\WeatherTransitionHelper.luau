-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Lighting = _services.Lighting
local TweenService = _services.TweenService
local WeatherTransitionHelper
do
	WeatherTransitionHelper = setmetatable({}, {
		__tostring = function()
			return "WeatherTransitionHelper"
		end,
	})
	WeatherTransitionHelper.__index = WeatherTransitionHelper
	function WeatherTransitionHelper.new(...)
		local self = setmetatable({}, WeatherTransitionHelper)
		return self:constructor(...) or self
	end
	function WeatherTransitionHelper:constructor()
	end
	function WeatherTransitionHelper:transitionWeather(config)
		if self.isTransitioning then
			print("⚠️ Weather transition already in progress, stopping previous transition")
			self:stopCurrentTransition()
		end
		self.isTransitioning = true
		local _lIGHTING_STATES = self.LIGHTING_STATES
		local _fromWeather = config.fromWeather
		local fromState = _lIGHTING_STATES[_fromWeather]
		local _lIGHTING_STATES_1 = self.LIGHTING_STATES
		local _toWeather = config.toWeather
		local toState = _lIGHTING_STATES_1[_toWeather]
		if not fromState or not toState then
			print(`⚠️ Invalid weather states for transition: {config.fromWeather} -> {config.toWeather}`)
			self.isTransitioning = false
			return nil
		end
		print(`🌦️ Starting weather transition: {config.fromWeather} -> {config.toWeather} ({config.duration}s)`)
		-- Create tween info
		local tweenInfo = TweenInfo.new(config.duration, config.easingStyle or Enum.EasingStyle.Sine, config.easingDirection or Enum.EasingDirection.InOut)
		-- Tween lighting properties
		local lightingTween = TweenService:Create(Lighting, tweenInfo, {
			FogEnd = toState.fogEnd,
			FogStart = toState.fogStart,
			FogColor = toState.fogColor,
			Brightness = toState.brightness,
			Ambient = toState.ambient,
		})
		-- Store the tween
		local _exp = self.activeTweens
		table.insert(_exp, lightingTween)
		-- Handle completion
		lightingTween.Completed:Connect(function()
			self.isTransitioning = false
			local _exp_1 = self.activeTweens
			-- ▼ ReadonlyArray.filter ▼
			local _newValue = {}
			local _callback = function(tween)
				return tween ~= lightingTween
			end
			local _length = 0
			for _k, _v in _exp_1 do
				if _callback(_v, _k - 1, _exp_1) == true then
					_length += 1
					_newValue[_length] = _v
				end
			end
			-- ▲ ReadonlyArray.filter ▲
			self.activeTweens = _newValue
			if config.onComplete then
				config.onComplete()
			end
			print(`✅ Weather transition completed: {config.toWeather}`)
		end)
		-- Handle progress updates
		if config.onUpdate then
			local startTime = tick()
			local updateLoop
			updateLoop = function()
				if not self.isTransitioning then
					return nil
				end
				local elapsed = tick() - startTime
				local progress = math.min(elapsed / config.duration, 1)
				config.onUpdate(progress)
				if progress < 1 then
					task.wait(0.1)
					updateLoop()
				end
			end
			task.spawn(function()
				return updateLoop()
			end)
		end
		-- Start the transition
		lightingTween:Play()
	end
	function WeatherTransitionHelper:getLightingStateForWeather(weatherType, intensity)
		local _lIGHTING_STATES = self.LIGHTING_STATES
		local _weatherType = weatherType
		local baseState = _lIGHTING_STATES[_weatherType]
		if not baseState then
			return self.LIGHTING_STATES.clear
		end
		-- Adjust lighting based on intensity
		return {
			fogEnd = math.max(50, baseState.fogEnd - intensity * baseState.fogEnd * 0.3),
			fogStart = baseState.fogStart,
			fogColor = baseState.fogColor,
			brightness = math.max(0.05, baseState.brightness - intensity * 0.2),
			ambient = Color3.new(math.max(0.05, baseState.ambient.R - intensity * 0.1), math.max(0.05, baseState.ambient.G - intensity * 0.1), math.max(0.05, baseState.ambient.B - intensity * 0.1)),
		}
	end
	function WeatherTransitionHelper:applyLightingState(state)
		Lighting.FogEnd = state.fogEnd
		Lighting.FogStart = state.fogStart
		Lighting.FogColor = state.fogColor
		Lighting.Brightness = state.brightness
		Lighting.Ambient = state.ambient
	end
	function WeatherTransitionHelper:getCurrentLightingState()
		return {
			fogEnd = Lighting.FogEnd,
			fogStart = Lighting.FogStart,
			fogColor = Lighting.FogColor,
			brightness = Lighting.Brightness,
			ambient = Lighting.Ambient,
		}
	end
	function WeatherTransitionHelper:stopCurrentTransition()
		local _exp = self.activeTweens
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(tween)
			tween:Cancel()
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		self.activeTweens = {}
		self.isTransitioning = false
		print("🛑 Weather transition stopped")
	end
	function WeatherTransitionHelper:isTransitionInProgress()
		return self.isTransitioning
	end
	function WeatherTransitionHelper:transitionWeatherIntensity(weatherType, fromIntensity, toIntensity, duration, onUpdate, onComplete)
		local startTime = tick()
		local intensityDiff = toIntensity - fromIntensity
		local intensityLoop
		intensityLoop = function()
			local elapsed = tick() - startTime
			local progress = math.min(elapsed / duration, 1)
			local currentIntensity = fromIntensity + intensityDiff * progress
			if onUpdate then
				onUpdate(currentIntensity)
			end
			if progress >= 1 then
				if onComplete then
					onComplete()
				end
			else
				task.wait(0.1)
				intensityLoop()
			end
		end
		task.spawn(function()
			return intensityLoop()
		end)
	end
	function WeatherTransitionHelper:fadeWeatherEffect(effectType, duration, onUpdate, onComplete)
		local startTime = tick()
		local startAlpha = if effectType == "in" then 0 else 1
		local endAlpha = if effectType == "in" then 1 else 0
		local alphaDiff = endAlpha - startAlpha
		local fadeLoop
		fadeLoop = function()
			local elapsed = tick() - startTime
			local progress = math.min(elapsed / duration, 1)
			local currentAlpha = startAlpha + alphaDiff * progress
			if onUpdate then
				onUpdate(currentAlpha)
			end
			if progress >= 1 then
				if onComplete then
					onComplete()
				end
			else
				task.wait(0.05)
				fadeLoop()
			end
		end
		task.spawn(function()
			return fadeLoop()
		end)
	end
	WeatherTransitionHelper.activeTweens = {}
	WeatherTransitionHelper.isTransitioning = false
	WeatherTransitionHelper.LIGHTING_STATES = {
		clear = {
			fogEnd = 100000,
			fogStart = 0,
			fogColor = Color3.new(0.76, 0.76, 0.76),
			brightness = 1,
			ambient = Color3.new(0.5, 0.5, 0.5),
		},
		rain = {
			fogEnd = 1500,
			fogStart = 0,
			fogColor = Color3.new(0.6, 0.6, 0.6),
			brightness = 0.6,
			ambient = Color3.new(0.3, 0.3, 0.4),
		},
		heavy_rain = {
			fogEnd = 800,
			fogStart = 0,
			fogColor = Color3.new(0.5, 0.5, 0.5),
			brightness = 0.4,
			ambient = Color3.new(0.25, 0.25, 0.35),
		},
		snow = {
			fogEnd = 1200,
			fogStart = 0,
			fogColor = Color3.new(0.8, 0.8, 0.9),
			brightness = 0.7,
			ambient = Color3.new(0.4, 0.4, 0.5),
		},
		blizzard = {
			fogEnd = 300,
			fogStart = 0,
			fogColor = Color3.new(0.8, 0.8, 0.9),
			brightness = 0.4,
			ambient = Color3.new(0.4, 0.4, 0.5),
		},
		storm = {
			fogEnd = 600,
			fogStart = 0,
			fogColor = Color3.new(0.3, 0.3, 0.3),
			brightness = 0.3,
			ambient = Color3.new(0.2, 0.2, 0.3),
		},
		thunderstorm = {
			fogEnd = 400,
			fogStart = 0,
			fogColor = Color3.new(0.2, 0.2, 0.2),
			brightness = 0.15,
			ambient = Color3.new(0.1, 0.1, 0.2),
		},
		fog = {
			fogEnd = 200,
			fogStart = 0,
			fogColor = Color3.new(0.7, 0.7, 0.7),
			brightness = 0.4,
			ambient = Color3.new(0.3, 0.3, 0.3),
		},
		sandstorm = {
			fogEnd = 300,
			fogStart = 0,
			fogColor = Color3.new(0.8, 0.7, 0.5),
			brightness = 0.5,
			ambient = Color3.new(0.4, 0.3, 0.2),
		},
	}
end
return {
	WeatherTransitionHelper = WeatherTransitionHelper,
}
