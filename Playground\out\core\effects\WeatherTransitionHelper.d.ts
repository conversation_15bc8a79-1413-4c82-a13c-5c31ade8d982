import { WeatherType } from "../world/state/interfaces/WeatherOptions";
export interface LightingState {
    fogEnd: number;
    fogStart: number;
    fogColor: Color3;
    brightness: number;
    ambient: Color3;
}
export interface WeatherTransitionConfig {
    fromWeather: WeatherType;
    toWeather: WeatherType;
    duration: number;
    easingStyle?: Enum.EasingStyle;
    easingDirection?: Enum.EasingDirection;
    onComplete?: () => void;
    onUpdate?: (progress: number) => void;
}
export declare class WeatherTransitionHelper {
    private static activeTweens;
    private static isTransitioning;
    private static readonly LIGHTING_STATES;
    /**
     * Transition smoothly between weather states
     */
    static transitionWeather(config: WeatherTransitionConfig): void;
    /**
     * Get lighting state for a specific weather type with intensity adjustment
     */
    static getLightingStateForWeather(weatherType: WeatherType, intensity: number): LightingState;
    /**
     * Apply lighting state immediately (no transition)
     */
    static applyLightingState(state: LightingState): void;
    /**
     * Get current lighting state
     */
    static getCurrentLightingState(): LightingState;
    /**
     * Stop current weather transition
     */
    static stopCurrentTransition(): void;
    /**
     * Check if weather transition is in progress
     */
    static isTransitionInProgress(): boolean;
    /**
     * Create a gradual weather intensity transition
     */
    static transitionWeatherIntensity(weatherType: WeatherType, fromIntensity: number, toIntensity: number, duration: number, onUpdate?: (intensity: number) => void, onComplete?: () => void): void;
    /**
     * Create a smooth fade transition for weather effects
     */
    static fadeWeatherEffect(effectType: "in" | "out", duration: number, onUpdate?: (alpha: number) => void, onComplete?: () => void): void;
}
