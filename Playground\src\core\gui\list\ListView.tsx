import * as React from "@rbxts/react";
import { SIZES } from "../../design";
import { Label } from "../label/Label";
import { ListItemButton } from "../button";
import { Image } from "../image/Image";
import { ScrollingFrame } from "../frame";

interface ListItemProps {
	text: string;
	icon?: string;
	onClick?: () => void;
	selected?: boolean;
}

interface ListViewProps {
	items: ListItemProps[];
	width?: number;
	height?: number;
	onItemSelect?: (index: number) => void;
}

export function ListView(props: ListViewProps): React.ReactElement {
	const width = props.width ?? 200;
	const height = props.height ?? 300;
	const itemHeight = 40;

	return (
		<ScrollingFrame size={new UDim2(0, width, 0, height)} canvasSize={new UDim2(0, 0, 0, 0)} scrollBarThickness={6}>
			<uilistlayout Padding={new UDim(0, 2)} SortOrder={Enum.SortOrder.LayoutOrder} />

			{props.items.map((item, index) => (
				<ListItemButton
					key={`list-item-${index}`}
					selected={item.selected}
					size={new UDim2(1, -10, 0, itemHeight)}
					layoutOrder={index}
					onClick={() => {
						if (item.onClick) item.onClick();
						if (props.onItemSelect) props.onItemSelect(index);
					}}
				>
					{item.icon ? (
						<Image
							image={item.icon}
							size={new UDim2(0, 24, 0, 24)}
							position={new UDim2(0, 8, 0.5, 0)}
							anchorPoint={new Vector2(0, 0.5)}
						/>
					) : undefined}

					<Label
						text={item.text}
						size={new UDim2(1, item.icon ? -40 : -16, 1, 0)}
						position={new UDim2(0, item.icon ? 40 : 8, 0, 0)}
					/>
				</ListItemButton>
			))}
		</ScrollingFrame>
	);
}
