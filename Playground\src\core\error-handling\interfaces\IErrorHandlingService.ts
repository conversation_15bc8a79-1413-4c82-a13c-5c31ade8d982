import { ErrorEntry, ErrorSeverity, Error<PERSON>ate<PERSON>y, ErrorContext } from "../types/ErrorTypes";
import { Result } from "../../foundation/types/Result";
import { Error } from "../../foundation/types/RobloxError";

export interface IErrorHandlingService {
	logApplicationError(
		message: string,
		severity: ErrorSeverity,
		category: ErrorCategory,
		context?: ErrorContext,
	): string;
	getErrors(severity?: ErrorSeverity, category?: ErrorCategory): ErrorEntry[];
	resolveError(errorId: string, resolution: string): Result<void, Error>;
	clearResolvedErrors(): void;
	getErrorCount(severity?: ErrorSeverity): number;
}
