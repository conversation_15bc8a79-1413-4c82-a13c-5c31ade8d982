import { AbilityBase } from "./AbilityBase";
import { Players } from "@rbxts/services";

export class RoomAbility extends AbilityBase {
	private cooldownEndTime = 0;

	constructor() {
		super("ROOM", 8);
	}

	public isOnCooldown(): boolean {
		return tick() < this.cooldownEndTime;
	}

	private startCooldown(): void {
		this.cooldownEndTime = tick() + this.getCooldownTime();
	}

	public activate(): void {
		if (this.isOnCooldown()) return;

		const player = Players.LocalPlayer;
		const character = player.Character;
		if (!character) return;

		print(`🔵 ${player.Name} activated Room ability!`);

		// Add your Room ability logic here
		// For now, just a simple effect
		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
		if (humanoidRootPart) {
			// Create a blue sphere effect
			const roomSphere = new Instance("Part");
			roomSphere.Name = "RoomSphere";
			roomSphere.Shape = Enum.PartType.Ball;
			roomSphere.Size = new Vector3(20, 20, 20);
			roomSphere.Color = Color3.fromRGB(0, 100, 255);
			roomSphere.Material = Enum.Material.ForceField;
			roomSphere.Transparency = 0.8;
			roomSphere.CanCollide = false;
			roomSphere.Anchored = true;
			roomSphere.Position = humanoidRootPart.Position;
			roomSphere.Parent = game.Workspace;

			// Remove after 3 seconds
			task.delay(3, () => {
				if (roomSphere.Parent) {
					roomSphere.Destroy();
				}
			});
		}

		this.startCooldown();
	}
}
