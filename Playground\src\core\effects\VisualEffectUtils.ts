import { RunService, Workspace } from "@rbxts/services";
import { EffectPartBuilder } from "./EffectPartBuilder";
import { EffectTweenBuilder } from "./EffectTweenBuilder";

export function cameraShake(intensity = 5, duration = 0.5): void {
	const camera = Workspace.CurrentCamera;
	if (!camera) return;

	const originalCFrame = camera.CFrame;
	const startTime = tick();

	const connection = RunService.RenderStepped.Connect(() => {
		const elapsed = tick() - startTime;
		if (elapsed >= duration) {
			camera.CFrame = originalCFrame;
			connection.Disconnect();
			return;
		}

		const progress = elapsed / duration;
		const currentIntensity = intensity * (1 - progress);

		const offset = new Vector3(
			(math.random() - 0.5) * currentIntensity,
			(math.random() - 0.5) * currentIntensity,
			(math.random() - 0.5) * currentIntensity,
		);

		camera.CFrame = originalCFrame.add(offset);
	});
}

export class CameraShakeHelper {
	private static activeShakes = new Map<string, RBXScriptConnection>();

	static shake(intensity = 5, duration = 0.5, id = "default"): void {
		// Stop any existing shake with the same ID
		this.stopShake(id);

		const camera = Workspace.CurrentCamera;
		if (!camera) return;

		const originalCFrame = camera.CFrame;
		const startTime = tick();

		const connection = RunService.RenderStepped.Connect(() => {
			const elapsed = tick() - startTime;

			if (elapsed >= duration) {
				// Restore original camera position and disconnect
				camera.CFrame = originalCFrame;
				connection.Disconnect();
				this.activeShakes.delete(id);
				return;
			}

			// Calculate shake intensity that decreases over time
			const progress = elapsed / duration;
			const currentIntensity = intensity * (1 - progress);

			// Generate random shake offset
			const shakeX = (math.random() - 0.5) * currentIntensity;
			const shakeY = (math.random() - 0.5) * currentIntensity;
			const shakeZ = (math.random() - 0.5) * currentIntensity;

			// Apply shake to camera
			const shakeOffset = new Vector3(shakeX, shakeY, shakeZ);
			camera.CFrame = originalCFrame.add(shakeOffset);
		});

		this.activeShakes.set(id, connection);
	}

	static stopShake(id: string): void {
		const connection = this.activeShakes.get(id);
		if (connection) {
			connection.Disconnect();
			this.activeShakes.delete(id);
		}
	}

	static stopAllShakes(): void {
		for (const [, connection] of this.activeShakes) {
			connection.Disconnect();
		}
		this.activeShakes.clear();
	}
}

export class CrackEffectHelper {
	static createGroundCracks(centerPosition: Vector3, maxRadius = 140, numCracks = 12): void {
		for (let i = 0; i < numCracks; i++) {
			const angle = (i / numCracks) * math.pi * 2;
			const crackLength = math.random(maxRadius * 0.6, maxRadius);

			// Create crack line segments
			const segments = 8;
			const segmentLength = crackLength / segments;

			for (let seg = 0; seg < segments; seg++) {
				const segmentDelay = i * 0.03 + seg * 0.02;

				task.delay(segmentDelay, () => {
					const crack = EffectPartBuilder.create()
						.shape(Enum.PartType.Block)
						.size(new Vector3(0.2, 0.1, segmentLength))
						.color(Color3.fromRGB(80, 70, 60))
						.material(Enum.Material.Concrete)
						.transparency(1)
						.position(
							new Vector3(
								centerPosition.X +
									math.cos(angle + math.random(-0.1, 0.1)) *
										(seg * segmentLength + segmentLength * 0.5),
								centerPosition.Y - 4.5,
								centerPosition.Z +
									math.sin(angle + math.random(-0.1, 0.1)) *
										(seg * segmentLength + segmentLength * 0.5),
							),
						)
						.cframe(
							new CFrame(
								new Vector3(
									centerPosition.X + math.cos(angle) * (seg * segmentLength + segmentLength * 0.5),
									centerPosition.Y - 4.5,
									centerPosition.Z + math.sin(angle) * (seg * segmentLength + segmentLength * 0.5),
								),
								new Vector3(
									centerPosition.X +
										math.cos(angle) * (seg * segmentLength + segmentLength * 0.5) +
										math.cos(angle),
									centerPosition.Y - 4.5,
									centerPosition.Z +
										math.sin(angle) * (seg * segmentLength + segmentLength * 0.5) +
										math.sin(angle),
								),
							),
						)
						.spawn();

					// Start invisible and appear
					EffectTweenBuilder.for(crack)
						.fade(0.2)
						.duration(0.1)
						.easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
						.play();

					// Fade out after some time
					task.delay(8 + math.random(2, 5), () => {
						EffectTweenBuilder.for(crack)
							.fade(1)
							.duration(3)
							.onComplete(() => crack.Destroy())
							.play();
					});
				});
			}
		}
	}

	static createAirCracks(position: Vector3, numCracks = 8): void {
		for (let i = 0; i < numCracks; i++) {
			const crack = EffectPartBuilder.create()
				.shape(Enum.PartType.Block)
				.size(new Vector3(0.2, math.random(3, 8), 0.2))
				.color(Color3.fromRGB(255, 255, 255))
				.material(Enum.Material.Neon)
				.transparency(0.3)
				.spawn();

			// Random crack positioning
			const angle = (i / numCracks) * math.pi * 2 + math.random(-0.5, 0.5);
			const distance = math.random(2, 6);
			const crackPos = position.add(
				new Vector3(math.cos(angle) * distance, math.random(-3, 3), math.sin(angle) * distance),
			);

			crack.CFrame = new CFrame(crackPos).mul(
				CFrame.Angles(math.random(-0.5, 0.5), angle, math.random(-0.3, 0.3)),
			);

			// Crack appearance animation
			EffectTweenBuilder.for(crack)
				.fade(0.1)
				.duration(0.1)
				.easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
				.play();

			// Crack disappear animation
			task.delay(2, () => {
				EffectTweenBuilder.for(crack)
					.fade(1)
					.duration(1)
					.onComplete(() => crack.Destroy())
					.play();
			});
		}

		// Create shockwave effect
		const shockwave = EffectPartBuilder.create()
			.shape(Enum.PartType.Cylinder)
			.size(new Vector3(0.1, 1, 1))
			.color(Color3.fromRGB(255, 255, 255))
			.material(Enum.Material.Neon)
			.transparency(0.5)
			.position(position)
			.cframe(new CFrame(position).mul(CFrame.Angles(0, 0, math.rad(90))))
			.spawn();

		// Expand shockwave
		EffectTweenBuilder.for(shockwave)
			.expand(new Vector3(0.1, 30, 30))
			.fade(1)
			.duration(0.8)
			.easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			.onComplete(() => shockwave.Destroy())
			.play();
	}

	static createGlassCracks(position: Vector3, direction: Vector3, numMainCracks = 8, numWebCracks = 12): void {
		// Create central impact point with bright flash
		const impact = EffectPartBuilder.create()
			.shape(Enum.PartType.Ball)
			.size(new Vector3(2, 2, 2))
			.color(Color3.fromRGB(255, 255, 255))
			.material(Enum.Material.Neon)
			.transparency(0)
			.position(position)
			.withLight(30, 20)
			.spawn();

		EffectTweenBuilder.for(impact)
			.expand(new Vector3(8, 8, 8))
			.fade(1)
			.duration(0.3)
			.onComplete(() => impact.Destroy())
			.play();

		// Create main radiating cracks
		for (let i = 0; i < numMainCracks; i++) {
			const angle = (i / numMainCracks) * math.pi * 2;
			const delay = i * 0.02;

			task.delay(delay, () => {
				this.createGlassCrackLine(position, angle, 15, true);
			});
		}

		// Create secondary web pattern
		task.delay(0.2, () => {
			for (let i = 0; i < numWebCracks; i++) {
				const delay = i * 0.03;

				task.delay(delay, () => {
					const angle = math.random(0, math.pi * 2);
					const startRadius = math.random(3, 8);
					const startPoint = position.add(
						new Vector3(math.cos(angle) * startRadius, math.sin(angle) * startRadius, math.random(-1, 1)),
					);

					this.createGlassCrackLine(startPoint, angle + math.random(-0.5, 0.5), 8, false);
				});
			}
		});
	}

	private static createGlassCrackLine(
		startPoint: Vector3,
		angle: number,
		maxLength: number,
		isMainCrack: boolean,
	): void {
		const segments = isMainCrack ? 12 : 6;
		const segmentLength = maxLength / segments;

		for (let seg = 0; seg < segments; seg++) {
			const segmentDelay = seg * 0.01;

			task.delay(segmentDelay, () => {
				const crack = EffectPartBuilder.create()
					.shape(Enum.PartType.Block)
					.size(
						new Vector3(
							isMainCrack ? 0.05 : 0.03,
							isMainCrack ? 0.08 : 0.05,
							segmentLength + math.random(-0.2, 0.2),
						),
					)
					.color(Color3.fromRGB(240, 250, 255))
					.material(Enum.Material.Neon)
					.transparency(1)
					.spawn();

				// Position along crack line with natural variation
				const deviation = math.random(-0.2, 0.2);
				const heightVariation = math.random(-1, 1);
				const distance = seg * segmentLength + segmentLength * 0.5;

				const crackPos = startPoint.add(
					new Vector3(
						math.cos(angle + deviation) * distance,
						math.sin(angle + deviation) * distance + heightVariation,
						math.random(-0.3, 0.3),
					),
				);

				crack.Position = crackPos;
				crack.CFrame = CFrame.lookAt(crackPos, crackPos.add(new Vector3(math.cos(angle), math.sin(angle), 0)));

				// Bright glow for main cracks
				if (isMainCrack) {
					const light = new Instance("PointLight");
					light.Color = Color3.fromRGB(200, 230, 255);
					light.Range = 2;
					light.Brightness = 3;
					light.Parent = crack;
				}

				// Flash into existence
				EffectTweenBuilder.for(crack).fade(0.1).duration(0.02).play();

				// Fade out after time
				task.delay(5 + math.random(2, 4), () => {
					EffectTweenBuilder.for(crack)
						.fade(1)
						.duration(2)
						.onComplete(() => crack.Destroy())
						.play();
				});
			});
		}
	}
}

export function createImpactFlash(pos: Vector3, size = 4, duration = 0.3): void {
	const flash = EffectPartBuilder.create()
		.shape(Enum.PartType.Ball)
		.size(new Vector3(0.5, 0.5, 0.5))
		.color(Color3.fromRGB(255, 255, 255))
		.material(Enum.Material.Neon)
		.transparency(0)
		.position(pos)
		.withLight(20, 15)
		.spawn();

	EffectTweenBuilder.for(flash)
		.expand(new Vector3(size, size, size))
		.fade(1)
		.duration(duration)
		.onComplete(() => flash.Destroy())
		.play();
}

export function createTrail(startPos: Vector3, endPos: Vector3, color: Color3, numSegments = 8): void {
	for (let i = 0; i < numSegments; i++) {
		const t = i / numSegments;
		const basePos = startPos.Lerp(endPos, t);
		const offset = new Vector3(math.random(-2, 2), math.random(-1, 1), math.random(-2, 2));

		const trail = EffectPartBuilder.create()
			.shape(Enum.PartType.Block)
			.size(new Vector3(0.3, 0.3, 2))
			.color(color)
			.material(Enum.Material.Neon)
			.transparency(0.3)
			.position(basePos.add(offset))
			.cframe(new CFrame(basePos.add(offset), basePos.add(offset).add(endPos.sub(startPos).Unit)))
			.spawn();

		task.delay(i * 0.05, () => {
			EffectTweenBuilder.for(trail)
				.fade(1)
				.duration(1.5)
				.onComplete(() => trail.Destroy())
				.play();
		});
	}
}
