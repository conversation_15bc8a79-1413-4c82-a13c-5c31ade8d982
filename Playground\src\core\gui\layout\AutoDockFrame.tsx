import * as React from "@rbxts/react";
import { COLORS, BORDER_RADIUS } from "../../design";
import { useZIndex } from "./useZIndex";

interface AutoDockFrameProps {
	children?: React.ReactNode;
	dockPosition?: "TopLeft" | "TopRight" | "BottomLeft" | "BottomRight" | "Center";
	size?: UDim2;
	padding?: number;
	backgroundColor?: string;
	backgroundTransparency?: number;
	margin?: number;
	zIndex?: number;
	elementId?: string; // Unique ID for Z-Index management
	bringToFrontOnMount?: boolean; // Whether to automatically bring to front when mounted
}

export function AutoDockFrame(props: AutoDockFrameProps): React.ReactElement {
	const dockPosition = props.dockPosition ?? "TopRight";
	const margin = props.margin ?? 20;
	const padding = props.padding ?? 12;
	const backgroundColor = props.backgroundColor ?? COLORS.bg.surface;
	const backgroundTransparency = props.backgroundTransparency ?? 0.1;
	const size = props.size ?? new UDim2(0, 300, 0, 400);

	// Use automatic Z-Index management
	const { zIndex } = useZIndex(props.elementId, props.bringToFrontOnMount ?? true);

	const finalZIndex = props.zIndex ?? zIndex;

	// Calculate position and anchor based on dock position
	const getPositionAndAnchor = () => {
		switch (dockPosition) {
			case "TopLeft":
				return {
					position: new UDim2(0, margin, 0, margin),
					anchorPoint: new Vector2(0, 0),
				};
			case "TopRight":
				return {
					position: new UDim2(1, -margin, 0, margin),
					anchorPoint: new Vector2(1, 0),
				};
			case "BottomLeft":
				return {
					position: new UDim2(0, margin, 1, -margin),
					anchorPoint: new Vector2(0, 1),
				};
			case "BottomRight":
				return {
					position: new UDim2(1, -margin, 1, -margin),
					anchorPoint: new Vector2(1, 1),
				};
			case "Center":
				return {
					position: new UDim2(0.5, 0, 0.5, 0),
					anchorPoint: new Vector2(0.5, 0.5),
				};
			default:
				return {
					position: new UDim2(1, -margin, 0, margin),
					anchorPoint: new Vector2(1, 0),
				};
		}
	};

	const { position, anchorPoint } = getPositionAndAnchor();

	return (
		<frame
			BackgroundColor3={Color3.fromHex(backgroundColor)}
			BackgroundTransparency={backgroundTransparency}
			Size={size}
			Position={position}
			AnchorPoint={anchorPoint}
			BorderSizePixel={0}
			ZIndex={finalZIndex}
		>
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />

			<uistroke Color={Color3.fromHex(COLORS.border.l2)} Thickness={1} Transparency={0.3} />

			<uipadding
				PaddingTop={new UDim(0, padding)}
				PaddingBottom={new UDim(0, padding)}
				PaddingLeft={new UDim(0, padding)}
				PaddingRight={new UDim(0, padding)}
			/>

			{props.children}
		</frame>
	);
}
