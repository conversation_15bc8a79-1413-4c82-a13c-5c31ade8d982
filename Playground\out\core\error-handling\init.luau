-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local exports = {}
-- Error handling interfaces
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "error-handling", "interfaces", "IErrorHandlingService") or {} do
	exports[_k] = _v
end
-- Error handling types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "error-handling", "types", "ErrorTypes") or {} do
	exports[_k] = _v
end
-- Error handling services
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "error-handling", "services", "ErrorHandlingService") or {} do
	exports[_k] = _v
end
return exports
