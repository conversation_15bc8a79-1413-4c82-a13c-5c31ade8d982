import { RunService, Workspace } from "@rbxts/services";
import { Entity, EntitySpawnOptions } from "./interfaces/Entity";
import { EntityType } from "./enums/EntityType";
import { EffectPartBuilder } from "../effects/EffectPartBuilder";
import { EffectTweenBuilder } from "../effects/EffectTweenBuilder";
import { createParticleExplosion } from "../effects/ParticleHelper";
import { playSound } from "../effects/SoundHelper";
import { cameraShake, createImpactFlash, createTrail } from "../effects/VisualEffectUtils";
import { TrailHelper } from "../effects/TrailHelper";
import { PositionHelper } from "../helper/PositionHelper";
import { CharacterBuilder } from "../character/CharacterBuilder";

export class EntityManager {
	private static instance: EntityManager;
	private entities = new Map<string, Entity>();
	private entityCounter = 0;
	private heartbeatConnection?: RBXScriptConnection;

	private constructor() {
		this.startHeartbeat();
	}

	public static getInstance(): EntityManager {
		if (!EntityManager.instance) {
			EntityManager.instance = new EntityManager();
		}
		return EntityManager.instance;
	}

	public spawnEntity(options: EntitySpawnOptions): Entity {
		const id = this.generateEntityId();
		const entity: Entity = {
			id,
			type: options.type,
			instance: this.createEntityInstance(options),
			position: options.position,
			isActive: true,
			createdAt: tick(),
			data: options.data || {},
		};

		this.entities.set(id, entity);

		// Auto-destroy after lifetime if specified
		if (options.lifetime) {
			task.delay(options.lifetime, () => {
				this.destroyEntity(id);
			});
		}

		return entity;
	}

	public getEntity(id: string): Entity | undefined {
		return this.entities.get(id);
	}

	public getEntitiesByType(entityType: EntityType): Entity[] {
		const result: Entity[] = [];
		this.entities.forEach((entity) => {
			if (entity.type === entityType && entity.isActive) {
				result.push(entity);
			}
		});
		return result;
	}

	public getEntitiesInRadius(position: Vector3, radius: number): Entity[] {
		const result: Entity[] = [];
		this.entities.forEach((entity) => {
			if (entity.isActive && entity.position.sub(position).Magnitude <= radius) {
				result.push(entity);
			}
		});
		return result;
	}

	public updateEntityPosition(id: string, position: Vector3): void {
		const entity = this.entities.get(id);
		if (entity && entity.isActive) {
			entity.position = position;
			PositionHelper.setPosition(entity.instance, position);
		}
	}

	public updateEntityData(id: string, data: Record<string, unknown>): void {
		const entity = this.entities.get(id);
		if (entity && entity.isActive) {
			entity.data = { ...entity.data, ...data };
		}
	}

	public destroyEntity(id: string): boolean {
		const entity = this.entities.get(id);
		if (!entity) return false;

		entity.isActive = false;
		if (entity.instance.Parent) {
			entity.instance.Destroy();
		}
		this.entities.delete(id);
		return true;
	}

	public getActiveEntityCount(): number {
		let count = 0;
		this.entities.forEach((entity) => {
			if (entity.isActive) count++;
		});
		return count;
	}

	public cleanup(): void {
		this.entities.forEach((entity) => {
			if (entity.instance.Parent) {
				entity.instance.Destroy();
			}
		});
		this.entities.clear();

		if (this.heartbeatConnection) {
			this.heartbeatConnection.Disconnect();
		}
	}

	private generateEntityId(): string {
		this.entityCounter++;
		return `entity_${this.entityCounter}_${tick()}`;
	}

	private createEntityInstance(options: EntitySpawnOptions): Instance {
		let instance: Instance;

		switch (options.type) {
			case EntityType.Player: {
				// Use CharacterBuilder for clean player creation
				instance = CharacterBuilder.createBasicPlayer("PlayerEntity", options.position);
				break;
			}

			case EntityType.NPC: {
				// Use CharacterBuilder for clean NPC creation
				const npc = CharacterBuilder.createBasicNPC("NPC", options.position);

				// Play spawn sound effect
				playSound("rbxassetid://131961136", 0.5, 1, npc);

				instance = npc;
				break;
			}

			case EntityType.Projectile: {
				// Create projectile with core helpers
				const projectile = EffectPartBuilder.create()
					.shape(Enum.PartType.Ball)
					.size(new Vector3(1, 1, 1))
					.color(Color3.fromRGB(255, 100, 100))
					.material(Enum.Material.Neon)
					.transparency(0.2)
					.position(options.position)
					.spawn();

				// Add trail effect using TrailHelper
				TrailHelper.createProjectileTrail(projectile, Color3.fromRGB(255, 100, 100));

				// Add pulsing animation using EffectTweenBuilder
				EffectTweenBuilder.for(projectile)
					.duration(0.5)
					.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
					.expand(new Vector3(1.2, 1.2, 1.2))
					.fade(0.1)
					.play();

				// Add visual trail effect using core helper
				task.delay(0.1, () => {
					if (projectile.Parent) {
						const endPos = projectile.Position.add(
							new Vector3(math.random(-5, 5), math.random(-2, 2), math.random(-5, 5)),
						);
						createTrail(projectile.Position, endPos, Color3.fromRGB(255, 100, 100), 6);
					}
				});

				// Play projectile sound
				playSound("rbxassetid://5127701483", 0.4, 1.2, projectile);

				instance = projectile;
				break;
			}

			case EntityType.Effect: {
				// Create effect with core helpers
				const effect = EffectPartBuilder.create()
					.shape(Enum.PartType.Ball)
					.size(new Vector3(2, 2, 2))
					.color(Color3.fromRGB(100, 255, 100))
					.material(Enum.Material.ForceField)
					.transparency(0.5)
					.position(options.position)
					.spawn();

				// Create particle explosion effect
				createParticleExplosion(options.position, 10, Color3.fromRGB(100, 255, 100), [5, 15], [0.2, 0.8]);

				// Create impact flash
				createImpactFlash(options.position, 5, 0.5);

				// Add subtle camera shake
				cameraShake(2, 0.3);

				// Add expanding animation using EffectTweenBuilder
				EffectTweenBuilder.for(effect)
					.duration(options.lifetime || 3)
					.easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
					.expand(new Vector3(10, 10, 10))
					.fade(1)
					.onComplete(() => {
						if (effect.Parent) {
							effect.Destroy();
						}
					})
					.play();

				// Play effect sound
				playSound("rbxassetid://131961136", 0.3, 1.5, effect);

				instance = effect;
				break;
			}

			case EntityType.Pickup: {
				// Create pickup with core helpers
				const pickup = EffectPartBuilder.create()
					.shape(Enum.PartType.Cylinder)
					.size(new Vector3(2, 2, 2))
					.color(Color3.fromRGB(255, 255, 100))
					.material(Enum.Material.Neon)
					.transparency(0.1)
					.position(options.position)
					.spawn();

				// Add floating animation using EffectTweenBuilder
				const floatHeight = 2;
				const floatSpeed = 2;

				// Create continuous floating motion
				const floatUp = () => {
					EffectTweenBuilder.for(pickup)
						.duration(floatSpeed)
						.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
						.move(options.position.add(new Vector3(0, floatHeight, 0)))
						.onComplete(() => {
							// Float back down
							EffectTweenBuilder.for(pickup)
								.duration(floatSpeed)
								.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
								.move(options.position.sub(new Vector3(0, floatHeight, 0)))
								.onComplete(floatUp)
								.play();
						})
						.play();
				};
				floatUp();

				// Add spinning animation
				const bodyAngularVelocity = new Instance("BodyAngularVelocity");
				bodyAngularVelocity.AngularVelocity = new Vector3(0, 2, 0);
				bodyAngularVelocity.MaxTorque = new Vector3(0, math.huge, 0);
				bodyAngularVelocity.Parent = pickup;

				// Add pulsing glow effect
				EffectTweenBuilder.for(pickup)
					.duration(1)
					.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
					.brightness(2)
					.fade(0.3)
					.onComplete(() => {
						// Pulse back
						EffectTweenBuilder.for(pickup)
							.duration(1)
							.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
							.brightness(1)
							.fade(0.1)
							.play();
					})
					.play();

				// Play pickup spawn sound
				playSound("rbxassetid://5127701483", 0.3, 0.8, pickup);

				instance = pickup;
				break;
			}

			case EntityType.Structure: {
				// Create structure using EffectPartBuilder for consistent building
				const base = EffectPartBuilder.create()
					.shape(Enum.PartType.Block)
					.size(new Vector3(4, 1, 4))
					.color(Color3.fromRGB(99, 95, 98))
					.material(Enum.Material.Concrete)
					.position(options.position)
					.spawn();
				base.Name = "Base";
				base.Anchored = true;

				// Create pillars using core helpers
				const pillarPositions = [
					options.position.add(new Vector3(-1.5, 2, -1.5)),
					options.position.add(new Vector3(1.5, 2, -1.5)),
					options.position.add(new Vector3(-1.5, 2, 1.5)),
					options.position.add(new Vector3(1.5, 2, 1.5)),
				];

				const model = new Instance("Model");
				model.Name = "Structure";
				base.Parent = model;
				model.PrimaryPart = base;

				// Add pillars with staggered spawn animation
				pillarPositions.forEach((pillarPos, index) => {
					task.delay(index * 0.2, () => {
						const pillar = EffectPartBuilder.create()
							.shape(Enum.PartType.Block)
							.size(new Vector3(0.5, 3, 0.5))
							.color(Color3.fromRGB(120, 115, 118))
							.material(Enum.Material.Concrete)
							.position(pillarPos)
							.spawn();
						pillar.Name = `Pillar${index + 1}`;
						pillar.Anchored = true;
						pillar.Parent = model;

						// Animate pillar rising from ground
						pillar.Size = new Vector3(0.5, 0, 0.5);
						EffectTweenBuilder.for(pillar)
							.duration(0.5)
							.easing(Enum.EasingStyle.Back, Enum.EasingDirection.Out)
							.expand(new Vector3(0.5, 3, 0.5))
							.play();

						// Play construction sound
						playSound("rbxassetid://131961136", 0.2, 0.6, pillar);
					});
				});

				// Create impact flash at base
				createImpactFlash(options.position, 3, 0.4);

				instance = model;
				break;
			}

			default: {
				const part = EffectPartBuilder.create()
					.shape(Enum.PartType.Block)
					.size(new Vector3(2, 2, 2))
					.color(Color3.fromRGB(128, 128, 128))
					.position(options.position)
					.spawn();
				instance = part;
			}
		}

		// Set position and rotation using PositionHelper
		PositionHelper.setPosition(instance, options.position, options.rotation);

		// Set parent
		instance.Parent = options.parent || game.Workspace;

		return instance;
	}

	private startHeartbeat(): void {
		this.heartbeatConnection = RunService.Heartbeat.Connect(() => {
			// Update entity positions from their instances using PositionHelper
			this.entities.forEach((entity) => {
				if (entity.isActive && entity.instance.Parent) {
					entity.position = PositionHelper.getPosition(entity.instance);
				}
			});
		});
	}
}
