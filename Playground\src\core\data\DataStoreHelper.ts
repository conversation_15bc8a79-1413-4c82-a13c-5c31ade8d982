import { DataStoreService, Players, RunService } from "@rbxts/services";
import { DataStoreOptions, DataStoreResult } from "./interfaces/DataStoreConfig";
import { BasePlayerData, CorePlayerData } from "./interfaces/PlayerData";
import { BaseGlobalData, CoreGlobalData } from "./interfaces/GlobalData";

export class DataStoreHelper<
	TPlayerData extends BasePlayerData = BasePlayerData,
	TGlobalData extends BaseGlobalData = BaseGlobalData,
> {
	private static instance: DataStoreHelper;
	private playerDataStore?: DataStore;
	private globalDataStore?: DataStore;
	private cache = new Map<string, { data: unknown; timestamp: number }>();
	private options: DataStoreOptions;
	private autoSaveActive = false;

	private constructor() {
		this.options = {
			retryAttempts: 2, // Reduce retry attempts
			retryDelay: 2, // Increase delay between retries
			cacheTimeout: 300, // 5 minutes
			autoSave: false, // Disable auto-save by default to prevent spam
			autoSaveInterval: 300, // 5 minutes instead of 1 minute
			enableStudioTesting: false, // Disable by default to avoid Studio errors
		};

		// Check if we're in Studio
		const isStudio = game.GetService("RunService").IsStudio();

		if (isStudio) {
			// Studio mode - use mock mode by default
			print("📝 Studio mode detected - Using mock DataStore (no persistence)");
			print("💡 To enable real DataStore testing, call dataStore.enableStudioTesting()");
		} else {
			// Production mode - try to initialize DataStores
			try {
				this.playerDataStore = DataStoreService.GetDataStore("PlayerData");
				this.globalDataStore = DataStoreService.GetDataStore("GlobalData");
				print("✅ DataStore initialized for production");
			} catch (error) {
				print(`❌ DataStore initialization failed: ${error}`);
				print("📝 Falling back to mock mode");
			}
		}

		if (this.options.autoSave) {
			this.startAutoSave();
		}
	}

	public static getInstance<
		T extends BasePlayerData = BasePlayerData,
		U extends BaseGlobalData = BaseGlobalData,
	>(): DataStoreHelper<T, U> {
		if (!DataStoreHelper.instance) {
			DataStoreHelper.instance = new DataStoreHelper();
		}
		return DataStoreHelper.instance as DataStoreHelper<T, U>;
	}

	public configure(options: Partial<DataStoreOptions>): void {
		this.options = { ...this.options, ...options };

		// Stop current auto-save if running
		this.autoSaveActive = false;

		if (this.options.autoSave) {
			this.startAutoSave();
		}
	}

	public enableStudioTesting(): void {
		if (!game.GetService("RunService").IsStudio()) {
			print("⚠️ enableStudioTesting() only works in Studio");
			return;
		}

		try {
			this.playerDataStore = DataStoreService.GetDataStore("PlayerData");
			this.globalDataStore = DataStoreService.GetDataStore("GlobalData");
			this.options.enableStudioTesting = true;
			print("✅ Studio DataStore testing enabled!");
			print("💡 Make sure 'Studio Access to API Services' is enabled in Game Settings");
		} catch (error) {
			print(`❌ Failed to enable Studio DataStore testing: ${error}`);
			print("💡 Please enable 'Studio Access to API Services' in Game Settings → Security");
		}
	}

	public disableAutoSave(): void {
		this.autoSaveActive = false;
		this.options.autoSave = false;
		print("🛑 Auto-save disabled");
	}

	// Player Data Methods
	public async loadPlayerData(userId: number): Promise<DataStoreResult<TPlayerData>> {
		const cacheKey = `player_${userId}`;

		// Check cache first
		const cached = this.getFromCache<TPlayerData>(cacheKey);
		if (cached) {
			return { success: true, data: cached, fromCache: true };
		}

		// If DataStore is not available, return default data
		if (!this.playerDataStore) {
			const defaultData = this.createDefaultPlayerData(userId);
			this.setCache(cacheKey, defaultData);
			return { success: true, data: defaultData, fromCache: false };
		}

		// Load from DataStore with retry logic
		return this.retryOperation(() => {
			return new Promise<DataStoreResult<TPlayerData>>((resolve) => {
				const [data] = this.playerDataStore!.GetAsync(`Player_${userId}`);
				const playerData =
					data !== undefined ? (data as unknown as TPlayerData) : this.createDefaultPlayerData(userId);

				// Cache the result
				this.setCache(cacheKey, playerData);

				resolve({ success: true, data: playerData });
			});
		});
	}

	public async savePlayerData(playerData: TPlayerData): Promise<DataStoreResult<boolean>> {
		const cacheKey = `player_${playerData.userId}`;

		// Update cache
		this.setCache(cacheKey, playerData);

		// If DataStore is not available, just return success (mock mode)
		if (!this.playerDataStore) {
			// Reduce spam - only log occasionally
			if (math.random() < 0.1) {
				// 10% chance to log
				print(`📝 Mock mode: Player data cached (not persisted)`);
			}
			return { success: true, data: true };
		}

		// Save to DataStore with retry logic
		return this.retryOperation(() => {
			return new Promise<DataStoreResult<boolean>>((resolve) => {
				this.playerDataStore!.SetAsync(`Player_${playerData.userId}`, playerData);
				resolve({ success: true, data: true });
			});
		});
	}

	public async updatePlayerData(
		userId: number,
		updates: Partial<TPlayerData>,
	): Promise<DataStoreResult<TPlayerData>> {
		const loadResult = await this.loadPlayerData(userId);
		if (!loadResult.success || !loadResult.data) {
			return { success: false, error: "Failed to load player data" };
		}

		const updatedData = { ...loadResult.data, ...updates } as TPlayerData;
		const saveResult = await this.savePlayerData(updatedData);

		if (saveResult.success) {
			return { success: true, data: updatedData };
		} else {
			return { success: false, error: saveResult.error };
		}
	}

	// Global Data Methods
	public async loadGlobalData(): Promise<DataStoreResult<TGlobalData>> {
		const cacheKey = "global_data";

		// Check cache first
		const cached = this.getFromCache<TGlobalData>(cacheKey);
		if (cached) {
			return { success: true, data: cached, fromCache: true };
		}

		// If DataStore is not available, return default data
		if (!this.globalDataStore) {
			const defaultData = this.createDefaultGlobalData();
			this.setCache(cacheKey, defaultData);
			return { success: true, data: defaultData, fromCache: false };
		}

		// Load from DataStore with retry logic
		return this.retryOperation(() => {
			return new Promise<DataStoreResult<TGlobalData>>((resolve) => {
				const [data] = this.globalDataStore!.GetAsync("GlobalData");
				const globalData =
					data !== undefined ? (data as unknown as TGlobalData) : this.createDefaultGlobalData();

				// Cache the result
				this.setCache(cacheKey, globalData);

				resolve({ success: true, data: globalData });
			});
		});
	}

	public async saveGlobalData(globalData: TGlobalData): Promise<DataStoreResult<boolean>> {
		const cacheKey = "global_data";

		// Update cache
		this.setCache(cacheKey, globalData);

		// If DataStore is not available, just return success (mock mode)
		if (!this.globalDataStore) {
			// Reduce spam - only log occasionally
			if (math.random() < 0.05) {
				// 5% chance to log
				print(`📝 Mock mode: Global data cached (not persisted)`);
			}
			return { success: true, data: true };
		}

		// Save to DataStore with retry logic
		return this.retryOperation(() => {
			return new Promise<DataStoreResult<boolean>>((resolve) => {
				this.globalDataStore!.SetAsync("GlobalData", globalData);
				resolve({ success: true, data: true });
			});
		});
	}

	// Generic utility method for updating game data
	public async updatePlayerGameData(
		userId: number,
		gameDataUpdates: Record<string, unknown>,
	): Promise<DataStoreResult<TPlayerData>> {
		const loadResult = await this.loadPlayerData(userId);
		if (!loadResult.success || !loadResult.data) {
			return { success: false, error: "Failed to load player data" };
		}

		const updatedGameData = { ...loadResult.data.gameData, ...gameDataUpdates };
		const updateResult = await this.updatePlayerData(userId, {
			gameData: updatedGameData,
		} as Partial<TPlayerData>);

		return updateResult;
	}

	public async updateGlobalGameData(gameDataUpdates: Record<string, unknown>): Promise<DataStoreResult<TGlobalData>> {
		const loadResult = await this.loadGlobalData();
		if (!loadResult.success || !loadResult.data) {
			return { success: false, error: "Failed to load global data" };
		}

		const updatedGameData = { ...loadResult.data.gameData, ...gameDataUpdates };
		const updatedGlobalData = { ...loadResult.data, gameData: updatedGameData } as TGlobalData;
		const saveResult = await this.saveGlobalData(updatedGlobalData);

		if (saveResult.success) {
			return { success: true, data: updatedGlobalData };
		} else {
			return { success: false, error: saveResult.error };
		}
	}

	public clearCache(): void {
		this.cache.clear();
	}

	public getCacheSize(): number {
		return this.cache.size();
	}

	public cleanup(): void {
		this.autoSaveActive = false;
		this.clearCache();
	}

	// Private Helper Methods
	private async retryOperation<T>(operation: () => Promise<DataStoreResult<T>>): Promise<DataStoreResult<T>> {
		let lastError = "";

		for (let attempt = 1; attempt <= this.options.retryAttempts; attempt++) {
			try {
				return await operation();
			} catch (error) {
				lastError = tostring(error);
				print(`DataStore operation failed (attempt ${attempt}/${this.options.retryAttempts}): ${lastError}`);

				if (attempt < this.options.retryAttempts) {
					await this.wait(this.options.retryDelay * attempt); // Exponential backoff
				}
			}
		}

		return { success: false, error: `Failed after ${this.options.retryAttempts} attempts: ${lastError}` };
	}

	private getFromCache<T>(key: string): T | undefined {
		const cached = this.cache.get(key);
		if (cached && tick() - cached.timestamp < this.options.cacheTimeout) {
			return cached.data as T;
		}

		// Remove expired cache entry
		if (cached) {
			this.cache.delete(key);
		}

		return undefined;
	}

	private setCache(key: string, data: unknown): void {
		this.cache.set(key, { data, timestamp: tick() });
	}

	private createDefaultPlayerData(userId: number): TPlayerData {
		const coreData: CorePlayerData = {
			userId,
			lastLogin: tick(),
			playtime: 0,
			createdAt: tick(),
			version: "1.0.0",
		};

		const baseData: BasePlayerData = {
			...coreData,
			gameData: {}, // Empty game data - games should extend this
			settings: {
				musicVolume: 0.8,
				sfxVolume: 0.8,
				language: "en",
			},
			metadata: {
				totalSessions: 1,
				averageSessionTime: 0,
				lastSaveTime: tick(),
				dataVersion: 1,
			},
		};

		return baseData as TPlayerData;
	}

	private createDefaultGlobalData(): TGlobalData {
		const coreData: CoreGlobalData = {
			serverVersion: "1.0.0",
			maintenanceMode: false,
			lastUpdated: tick(),
		};

		const baseData: BaseGlobalData = {
			...coreData,
			gameData: {}, // Empty game data - games should extend this
			serverConfig: {
				maxPlayers: 50,
				autoSaveInterval: 60,
				debugMode: false,
			},
		};

		return baseData as TGlobalData;
	}

	private startAutoSave(): void {
		// Use coroutine instead of Heartbeat for better performance
		this.autoSaveActive = true;
		task.spawn(() => {
			while (this.autoSaveActive) {
				task.wait(this.options.autoSaveInterval);
				if (this.autoSaveActive) {
					this.performAutoSave();
				}
			}
		});
	}

	private async performAutoSave(): Promise<void> {
		// Auto-save all online players
		for (const player of Players.GetPlayers()) {
			const cacheKey = `player_${player.UserId}`;
			const cached = this.cache.get(cacheKey);

			if (cached) {
				const result = await this.savePlayerData(cached.data as TPlayerData);
				if (result.success) {
					print(`Auto-saved data for player ${player.Name}`);
				} else {
					print(`Auto-save failed for player ${player.Name}: ${result.error}`);
				}
			}
		}
	}

	private wait(seconds: number): Promise<void> {
		return new Promise((resolve) => {
			task.delay(seconds, resolve);
		});
	}
}
