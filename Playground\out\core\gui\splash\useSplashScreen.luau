-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local SplashScreenManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "splash", "SplashScreenManager").SplashScreenManager
local function useSplashScreen()
	local state, setState = React.useState({
		isVisible = false,
		loadingProgress = 0,
		loadingText = "Initializing...",
	})
	local manager = React.useMemo(function()
		return SplashScreenManager:getInstance()
	end, {})
	React.useEffect(function()
		-- Set up state change listener
		manager:onStateChanged(function(newState)
			setState(newState)
		end)
		-- Get initial state
		setState(manager:getState())
	end, { manager })
	local startLoading = React.useCallback(TS.async(function()
		TS.await(manager:startLoading())
	end), { manager })
	local hide = React.useCallback(function()
		manager:hide()
	end, { manager })
	local setupDefaultTasks = React.useCallback(function()
		manager:setupDefaultCoreTasks()
	end, { manager })
	return {
		state = state,
		startLoading = startLoading,
		hide = hide,
		setupDefaultTasks = setupDefaultTasks,
		manager = manager,
	}
end
return {
	useSplashScreen = useSplashScreen,
}
