import { IService } from "./interfaces/IService";
import { Result } from "./types/Result";
import { ServiceName } from "./types/BrandedTypes";
import { Error, createError } from "./types/RobloxError";

export abstract class BaseService implements IService {
	protected isInitialized = false;
	protected isShutdown = false;

	constructor(public readonly name: string) {}

	public async initialize(): Promise<Result<void, Error>> {
		if (this.isInitialized) {
			return Result.ok(undefined);
		}

		try {
			const result = await this.onInitialize();
			if (result.isError()) {
				return result;
			}

			this.isInitialized = true;
			this.logInfo(`Service '${this.name}' initialized successfully`);
			return Result.ok(undefined);
		} catch (error) {
			const errorMessage = `Failed to initialize service '${this.name}': ${error}`;
			this.logError(errorMessage);
			return Result.err(createError(errorMessage));
		}
	}

	public async shutdown(): Promise<Result<void, Error>> {
		if (this.isShutdown || !this.isInitialized) {
			return Result.ok(undefined);
		}

		try {
			const result = await this.onShutdown();
			if (result.isError()) {
				return result;
			}

			this.isShutdown = true;
			this.isInitialized = false;
			this.logInfo(`Service '${this.name}' shutdown successfully`);
			return Result.ok(undefined);
		} catch (error) {
			const errorMessage = `Failed to shutdown service '${this.name}': ${error}`;
			this.logError(errorMessage);
			return Result.err(createError(errorMessage));
		}
	}

	protected abstract onInitialize(): Promise<Result<void, Error>>;
	protected abstract onShutdown(): Promise<Result<void, Error>>;

	protected ensureInitialized(): Result<void, Error> {
		if (!this.isInitialized) {
			return Result.err(createError(`Service '${this.name}' is not initialized`));
		}
		return Result.ok(undefined);
	}

	protected ensureNotShutdown(): Result<void, Error> {
		if (this.isShutdown) {
			return Result.err(createError(`Service '${this.name}' has been shutdown`));
		}
		return Result.ok(undefined);
	}

	protected logInfo(message: string): void {
		print(`[INFO] [${this.name}] ${message}`);
	}

	protected logWarning(message: string): void {
		warn(`[WARN] [${this.name}] ${message}`);
	}

	protected logError(message: string): void {
		warn(`[ERROR] [${this.name}] ${message}`);
	}

	protected logDebug(message: string): void {
		print(`[DEBUG] [${this.name}] ${message}`);
	}
}
