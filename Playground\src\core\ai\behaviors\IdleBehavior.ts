import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";
import { PositionHelper } from "../../helper/PositionHelper";

export class Idle<PERSON>ehavior implements AIBehavior {
	name = "Idle";
	priority = 1;

	canExecute(_context: AIContext): boolean {
		return true;
	}

	execute(context: AIContext): AIBehaviorResult {
		const idleTime = (context.blackboard.idleTime as number) || 0;
		const newIdleTime = idleTime + context.deltaTime;

		if (newIdleTime > 3) {
			this.lookAround(context);
			context.blackboard.idleTime = 0;
		} else {
			context.blackboard.idleTime = newIdleTime;
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		context.blackboard.idleTime = 0;
		print(`😴 ${context.entityId} is now idle`);
	}

	private lookAround(context: AIContext): void {
		const randomAngle = math.random() * math.pi * 2;
		const lookDirection = new Vector3(math.cos(randomAngle), 0, math.sin(randomAngle));
		const lookPosition = context.position.add(lookDirection.mul(10));

		PositionHelper.lookAt(context.entity, lookPosition);
	}
}
