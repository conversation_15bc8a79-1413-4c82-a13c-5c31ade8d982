import { IConfigurationService } from "../interfaces/IConfigurationService";
import { BaseService } from "../../foundation/BaseService";
import { ConfigKey, createError } from "../../foundation/types/BrandedTypes";
import { Result } from "../../foundation/types/Result";
import { Error } from "../../foundation/types/RobloxError";
import { ConfigurationEntry, ConfigurationScope, ConfigurationValidationRule } from "../types/ConfigurationTypes";

export class ConfigurationService extends BaseService implements IConfigurationService {
	private config = new Map<string, ConfigurationEntry>();
	private validationRules = new Map<string, ConfigurationValidationRule>();

	constructor() {
		super("ConfigurationService");
		this.setupDefaultConfiguration();
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		print("🔧 ConfigurationService: Initializing...");
		return Result.ok(undefined);
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.config.clear();
		this.validationRules.clear();
		return Result.ok(undefined);
	}

	public get<T>(key: ConfigKey, defaultValue?: T): Result<T, Error> {
		const entry = this.config.get(key);
		if (!entry) {
			if (defaultValue !== undefined) {
				return Result.ok(defaultValue);
			}
			return Result.err(createError(`Configuration key '${key}' not found`));
		}

		return Result.ok(entry.value as T);
	}

	public set<T>(key: ConfigKey, value: T): Result<void, Error> {
		const existingEntry = this.config.get(key);
		if (existingEntry?.isReadOnly) {
			return Result.err(createError(`Configuration key '${key}' is read-only`));
		}

		// Validate the value if rules exist
		const rule = this.validationRules.get(key);
		if (rule) {
			const validationResult = this.validateValue(value, rule);
			if (validationResult.isError()) {
				return validationResult;
			}
		}

		const entry: ConfigurationEntry<T> = {
			key,
			value,
			scope: ConfigurationScope.RUNTIME,
			timestamp: tick(),
		};

		this.config.set(key, entry);
		return Result.ok(undefined);
	}

	public has(key: ConfigKey): boolean {
		return this.config.has(key);
	}

	public remove(key: ConfigKey): Result<void, Error> {
		const entry = this.config.get(key);
		if (entry?.isReadOnly) {
			return Result.err(createError(`Configuration key '${key}' is read-only`));
		}

		this.config.delete(key);
		return Result.ok(undefined);
	}

	public getAll(): Record<string, unknown> {
		const result: Record<string, unknown> = {};
		for (const [key, entry] of this.config) {
			result[key] = entry.value;
		}
		return result;
	}

	public clear(): void {
		// Only clear non-read-only entries
		for (const [key, entry] of this.config) {
			if (!entry.isReadOnly) {
				this.config.delete(key);
			}
		}
	}

	public addValidationRule<T>(key: ConfigKey, rule: ConfigurationValidationRule<T>): void {
		this.validationRules.set(key, rule as ConfigurationValidationRule);
	}

	private validateValue<T>(value: T, rule: ConfigurationValidationRule<T>): Result<void, Error> {
		if (rule.required && value === undefined) {
			return Result.err(createError(rule.errorMessage || `Value is required for configuration key`));
		}

		if (rule.validator && !rule.validator(value)) {
			return Result.err(createError(rule.errorMessage || `Validation failed for configuration value`));
		}

		// Type-specific validation
		if (rule.type === "number" && typeOf(value) === "number") {
			const numberValue = value as unknown as number;
			if (rule.min !== undefined && numberValue < rule.min) {
				return Result.err(createError(`Value must be at least ${rule.min}`));
			}
			if (rule.max !== undefined && numberValue > rule.max) {
				return Result.err(createError(`Value must be at most ${rule.max}`));
			}
		}

		return Result.ok(undefined);
	}

	private setupDefaultConfiguration(): void {
		// Core system defaults
		this.setReadOnlyConfig("system.version", "1.0.0", "System version");
		this.setReadOnlyConfig("system.environment", "development", "Runtime environment");

		// Game defaults
		this.setConfig("game.debug.enabled", true, "Enable debug mode");
		this.setConfig("game.physics.gravity", 196.2, "World gravity");
		this.setConfig("network.timeout", 30, "Network timeout in seconds");
		this.setConfig("ui.animations.enabled", true, "Enable UI animations");
	}

	private setConfig<T>(key: string, value: T, description: string): void {
		const entry: ConfigurationEntry<T> = {
			key,
			value,
			scope: ConfigurationScope.GLOBAL,
			timestamp: tick(),
			description,
		};
		this.config.set(key, entry);
	}

	private setReadOnlyConfig<T>(key: string, value: T, description: string): void {
		const entry: ConfigurationEntry<T> = {
			key,
			value,
			scope: ConfigurationScope.GLOBAL,
			timestamp: tick(),
			description,
			isReadOnly: true,
		};
		this.config.set(key, entry);
	}
}
