-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local TYPOGRAPHY = _design.TYPOGRAPHY
local BORDER_RADIUS = _design.BORDER_RADIUS
local ZIndexManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager").ZIndexManager
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local _frame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame")
local ContainerFrame = _frame.ContainerFrame
local VerticalFrame = _frame.VerticalFrame
local Overlay = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay").Overlay
local function SplashScreen(props)
	local fadeOut, setFadeOut = React.useState(false)
	local splashZIndex = ZIndexManager:getCurrentZIndex() + 1000
	-- Handle loading completion
	React.useEffect(function()
		if props.loadingProgress >= 1 and props.isVisible then
			-- Start fade out animation
			setFadeOut(true)
			-- Complete loading after fade animation
			task.delay(1, function()
				if props.onLoadingComplete then
					props.onLoadingComplete()
				end
			end)
		end
	end, { props.loadingProgress, props.isVisible, props.onLoadingComplete })
	if not props.isVisible and not fadeOut then
		return React.createElement(React.Fragment)
	end
	local backgroundTransparency = if fadeOut then 1 else 0
	local contentTransparency = if fadeOut then 1 else 0
	return React.createElement(Overlay, {
		backgroundColor = Color3.fromHex(COLORS.bg.base),
		backgroundTransparency = backgroundTransparency,
		zIndex = splashZIndex,
		fullScreen = true,
	}, React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.base,
		backgroundTransparency = contentTransparency,
		size = UDim2.new(1, 0, 1, 0),
		padding = 0,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, React.createElement(VerticalFrame, {
		backgroundColor = COLORS.bg.base,
		backgroundTransparency = 1,
		size = UDim2.new(0.6, 0, 0.4, 0),
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		spacing = SIZES.padding * 3,
		horizontalAlignment = Enum.HorizontalAlignment.Center,
		padding = SIZES.padding * 2,
	}, React.createElement(Label, {
		text = "🎮 RoboxGames Core",
		fontSize = SIZES.fontSize * 3,
		textColor = COLORS.primary,
		size = UDim2.new(1, 0, 0, 60),
		alignment = Enum.TextXAlignment.Center,
		bold = true,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Advanced Game Framework",
		fontSize = SIZES.fontSize + 4,
		textColor = COLORS.text.secondary,
		size = UDim2.new(1, 0, 0, 30),
		alignment = Enum.TextXAlignment.Center,
		layoutOrder = 2,
	}), React.createElement("frame", {
		Size = UDim2.new(0, 120, 0, 25),
		Position = UDim2.new(0.5, 0, 0, 0),
		AnchorPoint = Vector2.new(0.5, 0),
		BackgroundColor3 = Color3.fromHex(COLORS.badge.bg),
		BorderSizePixel = 0,
		LayoutOrder = 2.5,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.badge.border),
		Thickness = 1,
	}), React.createElement("textlabel", {
		Text = "v3.0 Enterprise",
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(COLORS.badge.text),
		TextSize = SIZES.fontSize - 2,
		Font = TYPOGRAPHY.font,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
	})), React.createElement(ContainerFrame, {
		backgroundColor = COLORS["progress-bg"],
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 8),
		padding = 0,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
		layoutOrder = 3,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	}), React.createElement("frame", {
		BackgroundColor3 = Color3.fromHex(COLORS["progress-fill"]),
		Size = UDim2.new(props.loadingProgress, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	}))), React.createElement(Label, {
		text = props.loadingText,
		fontSize = SIZES.fontSize,
		textColor = COLORS.text.main,
		size = UDim2.new(1, 0, 0, 20),
		alignment = Enum.TextXAlignment.Center,
		layoutOrder = 4,
	}), React.createElement(Label, {
		text = `{math.floor(props.loadingProgress * 100)}%`,
		fontSize = SIZES.fontSize + 2,
		textColor = COLORS.primary,
		size = UDim2.new(1, 0, 0, 25),
		alignment = Enum.TextXAlignment.Center,
		bold = true,
		layoutOrder = 5,
	})), React.createElement(Label, {
		text = "Powered by @roboxgames/core framework",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.text.secondary,
		size = UDim2.new(1, 0, 0, 20),
		position = UDim2.new(0, 0, 1, -40),
		alignment = Enum.TextXAlignment.Center,
	})))
end
return {
	SplashScreen = SplashScreen,
}
