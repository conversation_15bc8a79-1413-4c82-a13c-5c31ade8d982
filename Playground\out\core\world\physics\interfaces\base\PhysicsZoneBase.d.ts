export type ObjectType = "players" | "parts" | "debris" | "projectiles" | "npcs";
export interface PhysicsZoneOptions {
    center: Vector3;
    radius: number;
    zoneType: "gravity" | "force" | "barrier" | "teleport" | "slow" | "accelerate";
    intensity: number;
    duration?: number;
    affectedObjects: ObjectType[];
    visualEffect?: boolean;
    soundEffect?: string;
}
export interface PhysicsZone {
    id: string;
    options: PhysicsZoneOptions;
    createdAt: number;
    expiresAt?: number;
    affectedObjects: Set<Instance>;
    zoneVisual?: Part;
}
export interface ForceApplication {
    target: Instance;
    forceType: "BodyVelocity" | "BodyPosition" | "BodyAngularVelocity";
    forceObject: Instance;
    originalValues?: Map<string, unknown>;
}
