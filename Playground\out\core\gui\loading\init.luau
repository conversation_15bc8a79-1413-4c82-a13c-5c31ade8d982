-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
local _LoadingIndicator = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "loading", "LoadingIndicator")
exports.LoadingSpinner = _LoadingIndicator.LoadingSpinner
exports.LoadingIndicator = _LoadingIndicator.LoadingIndicator
return exports
