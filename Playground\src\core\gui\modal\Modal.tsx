import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { IconButton } from "../button";
import { Label } from "../label/Label";
import { ContainerFrame, HorizontalFrame } from "../frame";
import { Overlay } from "../overlay/Overlay";

interface ModalProps {
	title: string;
	isOpen: boolean;
	onClose: () => void;
	children?: React.ReactNode;
	width?: number;
	height?: number;
}

export function Modal(props: ModalProps) {
	const [isAnimating, setIsAnimating] = React.useState(false);
	const width = props.width ?? 300;
	const height = props.height ?? 200;

	// Handle open/close animations
	React.useEffect(() => {
		if (props.isOpen) {
			setIsAnimating(true);
		}
	}, [props.isOpen]);

	const handleClose = () => {
		setIsAnimating(false);
		// Delay actual close to allow animation
		task.delay(0.2, () => {
			props.onClose();
		});
	};

	if (!props.isOpen && !isAnimating) return <></>;

	const modalScale = isAnimating && props.isOpen ? 1 : 0.8;
	const modalTransparency = isAnimating && props.isOpen ? 0 : 0.3;

	return (
		<Overlay onBackdropClick={handleClose} backgroundColor={Color3.fromHex(COLORS.bg.modal)}>
			{/* Modal container with click blocking and enhanced styling */}
			<textbutton
				Text=""
				BackgroundColor3={Color3.fromHex(COLORS.bg.base)}
				Size={new UDim2(modalScale, width * (1 - modalScale), modalScale, height * (1 - modalScale))}
				Position={new UDim2(0.5, 0, 0.5, 0)}
				AnchorPoint={new Vector2(0.5, 0.5)}
				ZIndex={12}
				AutoButtonColor={false}
				BorderSizePixel={0}
				BackgroundTransparency={modalTransparency}
				Event={{
					Activated: () => {
						// Stop propagation by doing nothing - this prevents backdrop click
					},
				}}
			>
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
				<uistroke Color={Color3.fromHex(COLORS.border.l2)} Thickness={1} />
				
				{/* Drop shadow effect */}
				<frame
					Size={new UDim2(1, 4, 1, 4)}
					Position={new UDim2(0, 2, 0, 2)}
					AnchorPoint={new Vector2(0, 0)}
					BackgroundColor3={Color3.fromHex(COLORS.shadow.lg)}
					BackgroundTransparency={0.6}
					BorderSizePixel={0}
					ZIndex={-1}
				>
					<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
				</frame>

				<ContainerFrame
					backgroundTransparency={1}
					size={new UDim2(1, 0, 1, 0)}
					padding={0}
					borderThickness={0}
					autoSize={Enum.AutomaticSize.None}
				>
					{/* Title bar */}
					<HorizontalFrame
						backgroundColor={COLORS.bg.secondary}
						size={new UDim2(1, 0, 0, 40)}
						backgroundTransparency={0}
						padding={SIZES.padding}
						spacing={0}
						horizontalAlignment={Enum.HorizontalAlignment.Left}
						verticalAlignment={Enum.VerticalAlignment.Center}
					>
						<Label
							text={props.title}
							fontSize={SIZES.fontSize + 2}
							size={new UDim2(1, -50, 1, 0)}
							bold={true}
							layoutOrder={1}
						/>

						<IconButton icon="X" onClick={handleClose} layoutOrder={2} size={new UDim2(0, 30, 0, 30)} />
					</HorizontalFrame>

					{/* Content area */}
					<ContainerFrame
						backgroundTransparency={1}
						size={new UDim2(1, 0, 1, -40)}
						position={new UDim2(0, 0, 0, 40)}
						padding={SIZES.padding}
						borderThickness={0}
						autoSize={Enum.AutomaticSize.None}
					>
						{props.children}
					</ContainerFrame>
				</ContainerFrame>
			</textbutton>
		</Overlay>
	);
}
