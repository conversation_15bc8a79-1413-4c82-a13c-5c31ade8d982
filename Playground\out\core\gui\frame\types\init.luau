-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Base frame properties
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "types", "BaseFrameProps") or {} do
	exports[_k] = _v
end
-- Specific frame component properties
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "types", "VerticalFrameProps") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "types", "HorizontalFrameProps") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "types", "ContainerFrameProps") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "types", "ScrollingFrameProps") or {} do
	exports[_k] = _v
end
return exports
