export enum ConfigurationScope {
	GLOBAL = "global",
	PLAYER = "player",
	SESSION = "session",
	RUNTIME = "runtime",
}

export interface ConfigurationEntry<T = unknown> {
	key: string;
	value: T;
	scope: ConfigurationScope;
	timestamp: number;
	description?: string;
	isReadOnly?: boolean;
}

export interface ConfigurationValidationRule<T = unknown> {
	type: "string" | "number" | "boolean" | "object" | "array" | "custom";
	required?: boolean;
	min?: number;
	max?: number;
	pattern?: string;
	validator?: (value: T) => boolean;
	errorMessage?: string;
}
