-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local TYPOGRAPHY = _design.TYPOGRAPHY
local BORDER_RADIUS = _design.BORDER_RADIUS
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local LoadingSpinner = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "loading", "LoadingIndicator").LoadingSpinner
local function Button(props)
	local hovered, setHovered = React.useState(false)
	local pressed, setPressed = React.useState(false)
	local focused, setFocused = React.useState(false)
	-- Enhanced color system with variants
	local getBackgroundColor = function()
		if props.disabled then
			return COLORS.bg.secondary
		end
		if props.loading then
			return COLORS.bg.surface
		end
		local variant = props.variant or "primary"
		if pressed then
			repeat
				if variant == "primary" then
					return COLORS["primary-dark"]
				end
				if variant == "secondary" then
					return COLORS.bg["surface-pressed"]
				end
				if variant == "danger" then
					return COLORS["error-dark"]
				end
				return COLORS.bg["surface-pressed"]
			until true
		end
		if hovered or focused then
			repeat
				if variant == "primary" then
					return COLORS["primary-light"]
				end
				if variant == "secondary" then
					return COLORS.bg["surface-hover"]
				end
				if variant == "danger" then
					return COLORS.error
				end
				return COLORS.bg["surface-hover"]
			until true
		end
		repeat
			if variant == "primary" then
				return COLORS.primary
			end
			if variant == "secondary" then
				return COLORS.bg.surface
			end
			if variant == "danger" then
				return COLORS.error
			end
			return COLORS.bg.surface
		until true
	end
	local getTextColor = function()
		if props.disabled then
			return COLORS.text.secondary
		end
		if props.loading then
			return COLORS.text.secondary
		end
		local variant = props.variant or "primary"
		repeat
			if variant == "primary" then
				return COLORS.bg.base
			end
			if variant == "secondary" then
				return COLORS.text.main
			end
			if variant == "danger" then
				return COLORS.text.main
			end
			return COLORS.text.main
		until true
	end
	local getBorderColor = function()
		if focused and not props.disabled then
			return COLORS.border.focus
		end
		return COLORS.border.l2
	end
	-- Responsive manager for dynamic sizing
	local responsiveManager = ResponsiveManager:getInstance()
	-- Smart sizing: use autoSize if specified or no size provided
	local _condition = props.autoSize
	if _condition == nil then
		_condition = props.size == nil
	end
	local useAutoSize = _condition
	-- Calculate responsive dimensions
	local _condition_1 = props.minWidth
	if _condition_1 == nil then
		_condition_1 = SIZES.button.width
	end
	local minWidth = _condition_1
	local buttonHeight = SIZES.button.height
	local _condition_2 = props.padding
	if _condition_2 == nil then
		_condition_2 = SIZES.padding
	end
	local padding = _condition_2
	if props.responsive then
		local deviceType = responsiveManager:getDeviceType()
		local scaleFactor = if deviceType == "mobile" then 0.9 elseif deviceType == "tablet" then 0.95 else 1.0
		minWidth = minWidth * scaleFactor
		buttonHeight = buttonHeight * scaleFactor
		padding = responsiveManager:getResponsiveMargin(padding)
	end
	-- Calculate size based on autoSize setting
	local size = props.size or (if useAutoSize then UDim2.new(0, minWidth, 0, buttonHeight) else UDim2.new(0, minWidth, 0, buttonHeight))
	-- Display text with loading state
	local displayText = if props.loading then "Loading..." else props.text
	return React.createElement("textbutton", {
		Text = displayText,
		TextColor3 = Color3.fromHex(getTextColor()),
		BackgroundColor3 = Color3.fromHex(getBackgroundColor()),
		Size = size,
		LayoutOrder = props.LayoutOrder,
		Font = TYPOGRAPHY.font,
		TextSize = SIZES.fontSize,
		AutoButtonColor = false,
		AutomaticSize = if useAutoSize then Enum.AutomaticSize.X else Enum.AutomaticSize.None,
		Event = {
			Activated = function()
				if not props.disabled and not props.loading then
					props.onClick()
				end
			end,
			MouseEnter = function()
				if not props.disabled and not props.loading then
					setHovered(true)
				end
			end,
			MouseLeave = function()
				setHovered(false)
				setPressed(false)
			end,
			MouseButton1Down = function()
				if not props.disabled and not props.loading then
					setPressed(true)
				end
			end,
			MouseButton1Up = function()
				setPressed(false)
			end,
			SelectionGained = function()
				setFocused(true)
			end,
			SelectionLost = function()
				setFocused(false)
			end,
		},
		BorderSizePixel = 0,
		Selectable = not props.disabled and not props.loading,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(getBorderColor()),
		Thickness = if focused then 2 else 1,
		Transparency = if props.disabled then 0.5 else 0,
	}), useAutoSize and React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	}), props.loading and (React.createElement("frame", {
		Size = UDim2.new(0, 20, 0, 20),
		Position = UDim2.new(0, padding / 2, 0.5, 0),
		AnchorPoint = Vector2.new(0, 0.5),
		BackgroundTransparency = 1,
	}, React.createElement(LoadingSpinner, {
		size = 16,
		color = getTextColor(),
		speed = 1,
	}))))
end
return {
	Button = Button,
}
