import * as React from "@rbxts/react";
interface FormFieldProps {
    label?: string;
    value: string;
    onChange: (value: string) => void;
    onBlur?: () => void;
    placeholder?: string;
    error?: string;
    required?: boolean;
    disabled?: boolean;
    type?: "text" | "password" | "number";
    maxLength?: number;
    layoutOrder?: number;
}
export declare const FormField: React.MemoExoticComponent<(props: FormFieldProps) => React.ReactElement>;
export declare const validators: {
    required: (value: string) => "This field is required" | undefined;
    minLength: (min: number) => (value: string) => string | undefined;
    maxLength: (max: number) => (value: string) => string | undefined;
    email: (value: string) => "Please enter a valid email address" | undefined;
    number: (value: string) => "Please enter a valid number" | undefined;
    range: (min: number, max: number) => (value: string) => string | undefined;
};
export declare function useFormValidation<T extends Record<string, string>>(initialValues: T, fieldNames: (keyof T)[], validationRules: Record<keyof T, ((value: string) => string | undefined)[]>): {
    values: T;
    errors: Partial<Record<keyof T, string>>;
    touched: Record<keyof T, boolean>;
    isValid: boolean;
    setValue: (field: keyof T, value: string) => void;
    setTouched: (field: keyof T) => void;
    validateAll: () => boolean;
};
export {};
