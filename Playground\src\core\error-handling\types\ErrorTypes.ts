export enum ErrorSeverity {
	LOW = "low",
	MEDIUM = "medium",
	HIGH = "high",
	CRITICAL = "critical",
}

export enum ErrorCategory {
	NETWORK = "network",
	VALIDATION = "validation",
	SYSTEM = "system",
	USER_INPUT = "user_input",
	BUSINESS_LOGIC = "business_logic",
	EXTERNAL_SERVICE = "external_service",
}

export interface ErrorContext {
	userId?: number;
	sessionId?: string;
	component?: string;
	operation?: string;
	additionalData?: Record<string, unknown>;
}

export interface ErrorEntry {
	id: string;
	message: string;
	severity: ErrorSeverity;
	category: ErrorCategory;
	timestamp: number;
	context?: ErrorContext;
	stackTrace?: string;
	resolved?: boolean;
	resolution?: string;
}
