import * as React from "@rbxts/react";

export interface BaseFrameProps {
	children?: React.ReactNode;
	size?: UDim2;
	position?: UDim2;
	anchorPoint?: Vector2;
	layoutOrder?: number;
	backgroundColor?: string;
	backgroundTransparency?: number;
	padding?: number;
	zIndex?: number;
	autoSize?: Enum.AutomaticSize;
	fillDirection?: "auto" | "manual"; // "auto" uses AutomaticSize, "manual" uses explicit size

	// Responsive properties
	responsive?: boolean; // Enable responsive behavior
	minSize?: UDim2; // Minimum size constraints
	maxSize?: UDim2; // Maximum size constraints
	responsiveMargin?: boolean; // Use responsive margins based on device type
}
