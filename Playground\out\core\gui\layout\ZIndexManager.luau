-- Compiled with roblox-ts v3.0.0
--[[
	*
	 * Global Z-Index Management System
	 * Automatically manages layering of UI elements so new windows always appear on top
	 *
	 * Z-Index Ranges:
	 * - 500-599: Debug overlay panels (performance monitor, debug info) - background
	 * - 1000+: Interactive panels (world testing, debug controls) - foreground
	 * - Each interactive panel gets +50 to ensure proper stacking order
	 
]]
local ZIndexManagerClass
do
	ZIndexManagerClass = setmetatable({}, {
		__tostring = function()
			return "ZIndexManagerClass"
		end,
	})
	ZIndexManagerClass.__index = ZIndexManagerClass
	function ZIndexManagerClass.new(...)
		local self = setmetatable({}, ZIndexManagerClass)
		return self:constructor(...) or self
	end
	function ZIndexManagerClass:constructor()
		self.currentZIndex = 1000
		self.registeredElements = {}
	end
	function ZIndexManagerClass:getNextZIndex(elementId)
		self.currentZIndex += 10
		if elementId ~= "" and elementId then
			local _registeredElements = self.registeredElements
			local _elementId = elementId
			local _currentZIndex = self.currentZIndex
			_registeredElements[_elementId] = _currentZIndex
		end
		-- Reduced verbosity - only log for important elements
		local _condition = elementId
		if _condition ~= "" and _condition then
			_condition = ((string.find(elementId, "PANEL")) ~= nil or (string.find(elementId, "MODAL")) ~= nil)
		end
		if _condition ~= "" and _condition then
			print(`📊 ZIndexManager.getNextZIndex("{elementId}") = {self.currentZIndex}`)
		end
		return self.currentZIndex
	end
	function ZIndexManagerClass:getCurrentZIndex()
		return self.currentZIndex
	end
	function ZIndexManagerClass:bringToFront(elementId)
		local newZIndex = self:getNextZIndex(elementId)
		return newZIndex
	end
	function ZIndexManagerClass:getZIndex(elementId)
		local _registeredElements = self.registeredElements
		local _elementId = elementId
		return _registeredElements[_elementId]
	end
	function ZIndexManagerClass:unregister(elementId)
		local _registeredElements = self.registeredElements
		local _elementId = elementId
		_registeredElements[_elementId] = nil
	end
	function ZIndexManagerClass:reset()
		self.currentZIndex = 1000
		table.clear(self.registeredElements)
	end
	function ZIndexManagerClass:getRegisteredElements()
		local copy = {}
		local _exp = self.registeredElements
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			local _key = key
			local _value = value
			copy[_key] = _value
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return copy
	end
	function ZIndexManagerClass:printZIndexSummary()
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.registeredElements do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		print(`📊 === Z-INDEX SUMMARY ({_size} elements) ===`)
		-- Sort by z-index value
		local sortedElements = {}
		local _exp = self.registeredElements
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			local _arg0 = { key, value }
			table.insert(sortedElements, _arg0)
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.sort(sortedElements, function(a, b)
			return a[2] < b[2]
		end)
		-- ▼ ReadonlyArray.forEach ▼
		local _callback_1 = function(_param)
			local elementId = _param[1]
			local zIndex = _param[2]
			local priority = if zIndex >= 1000 then "[HIGH PRIORITY]" elseif zIndex >= 500 then "[LOW PRIORITY]" else "[REGULAR]"
			print(`  {elementId}: {zIndex} {priority}`)
		end
		for _k, _v in sortedElements do
			_callback_1(_v, _k - 1, sortedElements)
		end
		-- ▲ ReadonlyArray.forEach ▲
		print(`📊 Current highest z-index: {self.currentZIndex}`)
		print(`📊 === END Z-INDEX SUMMARY ===`)
	end
	function ZIndexManagerClass:getDebugOverlayZIndex(elementId)
		-- Use a fixed lower range for debug overlay panels (500-599)
		local debugOverlayBase = 500
		-- Count existing debug overlay elements to calculate offset
		local debugOverlayCount = 0
		local _exp = self.registeredElements
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			if value >= 500 and value < 600 then
				debugOverlayCount += 1
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		local debugOverlayZIndex = debugOverlayBase + debugOverlayCount * 5
		if elementId ~= "" and elementId then
			local _registeredElements = self.registeredElements
			local _elementId = elementId
			_registeredElements[_elementId] = debugOverlayZIndex
		end
		print(`🔧 ZIndexManager.getDebugOverlayZIndex("{elementId}") = {debugOverlayZIndex} [LOW PRIORITY]`)
		print(`📋 Debug overlay count in range 500-599: {debugOverlayCount}`)
		return debugOverlayZIndex
	end
	function ZIndexManagerClass:getDebugZIndex(elementId)
		-- Force a much higher z-index for interactive panels - start at 2000 minimum
		local minInteractiveZIndex = 2000
		if self.currentZIndex < minInteractiveZIndex then
			self.currentZIndex = minInteractiveZIndex
		end
		self.currentZIndex += 100
		if elementId ~= "" and elementId then
			local _registeredElements = self.registeredElements
			local _elementId = elementId
			local _currentZIndex = self.currentZIndex
			_registeredElements[_elementId] = _currentZIndex
		end
		-- Only log debug ZIndex for important elements to reduce console spam
		local _condition = elementId
		if _condition ~= "" and _condition then
			_condition = (string.find(elementId, "DEBUG")) ~= nil
		end
		if _condition ~= "" and _condition then
			print(`🌍 ZIndexManager.getDebugZIndex("{elementId}") = {self.currentZIndex} [HIGH PRIORITY]`)
		end
		return self.currentZIndex
	end
end
-- Export a singleton instance
local ZIndexManager = ZIndexManagerClass.new()
return {
	ZIndexManager = ZIndexManager,
}
