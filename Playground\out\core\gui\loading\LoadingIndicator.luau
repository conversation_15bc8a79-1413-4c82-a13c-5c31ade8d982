-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local RunService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").RunService
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local TYPOGRAPHY = _design.TYPOGRAPHY
local LoadingSpinner = React.memo(function(props)
	local _condition = props.size
	if _condition == nil then
		_condition = 20
	end
	local size = _condition
	local _condition_1 = props.color
	if _condition_1 == nil then
		_condition_1 = COLORS.primary
	end
	local color = _condition_1
	local _condition_2 = props.speed
	if _condition_2 == nil then
		_condition_2 = 1.5
	end
	local speed = _condition_2
	-- Memoize the spinner characters for smooth animation
	local spinnerChars = React.useMemo(function()
		return { "◐", "◓", "◑", "◒" }
	end, {})
	local currentIndex, setCurrentIndex = React.useState(0)
	-- Animated rotation effect
	React.useEffect(function()
		local timeoutId
		local animate
		animate = function()
			setCurrentIndex(function(prev)
				return (prev + 1) % #spinnerChars
			end)
			timeoutId = task.delay((speed * 1000) / #spinnerChars / 1000, animate)
		end
		animate()
		return function()
			if timeoutId ~= 0 and timeoutId == timeoutId and timeoutId ~= "" and timeoutId then
				task.cancel(timeoutId)
			end
		end
	end, { speed, spinnerChars })
	return React.createElement("textlabel", {
		Text = spinnerChars[currentIndex + 1],
		Size = UDim2.new(0, size, 0, size),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(color),
		TextSize = size,
		Font = TYPOGRAPHY.font,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
	})
end)
local DotsLoader, PulseLoader
local LoadingIndicator = React.memo(function(props)
	local sizeMap = {
		sm = {
			spinner = 16,
			text = SIZES.fontSize - 2,
		},
		md = {
			spinner = 20,
			text = SIZES.fontSize,
		},
		lg = {
			spinner = 24,
			text = SIZES.fontSize + 2,
		},
	}
	local currentSize = sizeMap[props.size or "md"]
	local variant = props.variant or "spinner"
	local _condition = props.color
	if _condition == nil then
		_condition = COLORS.primary
	end
	local color = _condition
	-- Memoize loading content based on variant
	local loadingContent = React.useMemo(function()
		repeat
			if variant == "dots" then
				return React.createElement(DotsLoader, {
					color = color,
					size = currentSize.spinner,
				})
			end
			if variant == "pulse" then
				return React.createElement(PulseLoader, {
					color = color,
					size = currentSize.spinner,
				})
			end
			return React.createElement(LoadingSpinner, {
				color = color,
				size = currentSize.spinner,
			})
		until true
	end, { variant, color, currentSize.spinner })
	local _value = props.text
	if not (_value ~= "" and _value) then
		return loadingContent
	end
	local _attributes = {}
	local _exp = currentSize.spinner
	local _value_1 = props.text
	_attributes.Size = UDim2.new(0, 0, 0, _exp + (if _value_1 ~= "" and _value_1 then currentSize.text + 10 else 0))
	_attributes.BackgroundTransparency = 1
	_attributes.AutomaticSize = Enum.AutomaticSize.X
	local _exp_1 = React.createElement("uilistlayout", {
		FillDirection = Enum.FillDirection.Horizontal,
		SortOrder = Enum.SortOrder.LayoutOrder,
		VerticalAlignment = Enum.VerticalAlignment.Center,
		Padding = UDim.new(0, 8),
	})
	local _exp_2 = loadingContent
	local _condition_1 = props.text
	if _condition_1 ~= "" and _condition_1 then
		_condition_1 = (React.createElement("textlabel", {
			Text = props.text,
			Size = UDim2.new(0, 0, 0, currentSize.text),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex(COLORS.text.secondary),
			TextSize = currentSize.text,
			Font = TYPOGRAPHY.font,
			TextXAlignment = Enum.TextXAlignment.Left,
			TextYAlignment = Enum.TextYAlignment.Center,
			AutomaticSize = Enum.AutomaticSize.X,
			LayoutOrder = 2,
		}))
	end
	return React.createElement("frame", _attributes, _exp_1, _exp_2, _condition_1)
end)
-- Dots animation loader
DotsLoader = React.memo(function(props)
	local activeIndex, setActiveIndex = React.useState(0)
	React.useEffect(function()
		local timeoutId
		local animate
		animate = function()
			setActiveIndex(function(prev)
				return (prev + 1) % 3
			end)
			timeoutId = task.delay(0.4, animate)
		end
		animate()
		return function()
			if timeoutId ~= 0 and timeoutId == timeoutId and timeoutId ~= "" and timeoutId then
				task.cancel(timeoutId)
			end
		end
	end, {})
	local dotSize = math.floor(props.size / 3)
	local _exp = React.createElement("uilistlayout", {
		FillDirection = Enum.FillDirection.Horizontal,
		SortOrder = Enum.SortOrder.LayoutOrder,
		HorizontalAlignment = Enum.HorizontalAlignment.Center,
		VerticalAlignment = Enum.VerticalAlignment.Center,
		Padding = UDim.new(0, 2),
	})
	local _exp_1 = { 0, 1, 2 }
	-- ▼ ReadonlyArray.map ▼
	local _newValue = table.create(#_exp_1)
	local _callback = function(index)
		return React.createElement("frame", {
			key = index,
			Size = UDim2.new(0, dotSize, 0, dotSize),
			BackgroundColor3 = Color3.fromHex(props.color),
			BackgroundTransparency = if activeIndex == index then 0 else 0.6,
			BorderSizePixel = 0,
			LayoutOrder = index,
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0.5, 0),
		}))
	end
	for _k, _v in _exp_1 do
		_newValue[_k] = _callback(_v, _k - 1, _exp_1)
	end
	-- ▲ ReadonlyArray.map ▲
	return React.createElement("frame", {
		Size = UDim2.new(0, props.size, 0, dotSize),
		BackgroundTransparency = 1,
	}, _exp, _newValue)
end)
-- Pulse animation loader
PulseLoader = React.memo(function(props)
	local scale, setScale = React.useState(1)
	React.useEffect(function()
		local tween = {
			start = 0.7,
			["end"] = 1.3,
			duration = 1000,
		}
		local startTime = tick()
		local connection = RunService.Heartbeat:Connect(function()
			local elapsed = tick() - startTime
			local progress = (elapsed % (tween.duration / 1000)) / (tween.duration / 1000)
			local pingPong = if progress <= 0.5 then progress * 2 else (1 - progress) * 2
			setScale(tween.start + (tween["end"] - tween.start) * pingPong)
		end)
		return function()
			return connection:Disconnect()
		end
	end, {})
	return React.createElement("frame", {
		Size = UDim2.new(scale, 0, scale, 0),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		BackgroundColor3 = Color3.fromHex(props.color),
		BackgroundTransparency = 0.3,
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0.5, 0),
	}))
end)
return {
	LoadingSpinner = LoadingSpinner,
	LoadingIndicator = LoadingIndicator,
}
