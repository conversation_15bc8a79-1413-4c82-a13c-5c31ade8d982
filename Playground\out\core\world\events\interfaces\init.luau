-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local exports = {}
-- Base world event interfaces
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "interfaces", "base", "WorldEventBase") or {} do
	exports[_k] = _v
end
-- Specific event type interfaces
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "interfaces", "types", "EarthquakeEvent") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "interfaces", "types", "TsunamiEvent") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "interfaces", "types", "MeteorEvent") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "interfaces", "types", "LightningStormEvent") or {} do
	exports[_k] = _v
end
return exports
