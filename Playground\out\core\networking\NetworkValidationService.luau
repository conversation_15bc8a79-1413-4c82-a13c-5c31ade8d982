-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local NetworkError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "errors", "NetworkError").NetworkError
local NetworkValidationService
do
	local super = BaseService
	NetworkValidationService = setmetatable({}, {
		__tostring = function()
			return "NetworkValidationService"
		end,
		__index = super,
	})
	NetworkValidationService.__index = NetworkValidationService
	function NetworkValidationService.new(...)
		local self = setmetatable({}, NetworkValidationService)
		return self:constructor(...) or self
	end
	function NetworkValidationService:constructor()
		super.constructor(self, "NetworkValidationService")
		self.rateLimitMap = {}
		self.authenticatedPlayers = {}
	end
	NetworkValidationService.onInitialize = TS.async(function(self)
		self:startCleanupTask()
		return Result:ok(nil)
	end)
	NetworkValidationService.onShutdown = TS.async(function(self)
		table.clear(self.rateLimitMap)
		table.clear(self.authenticatedPlayers)
		return Result:ok(nil)
	end)
	function NetworkValidationService:checkRateLimit(player, eventName, rateLimit)
		local key = `{player.UserId}_{eventName}`
		local now = tick() * 1000
		local entry = self.rateLimitMap[key]
		if not entry or now - entry.windowStart > rateLimit.windowMs then
			entry = {
				calls = 0,
				windowStart = now,
			}
			local _rateLimitMap = self.rateLimitMap
			local _entry = entry
			_rateLimitMap[key] = _entry
		end
		entry.calls += 1
		if entry.calls > rateLimit.maxCalls then
			self:logWarning(`Rate limit exceeded for player {player.Name} on event {eventName}`)
			return Result:err(NetworkError.new("Rate limit exceeded"))
		end
		return Result:ok(nil)
	end
	function NetworkValidationService:validateAuthentication(player)
		local _authenticatedPlayers = self.authenticatedPlayers
		local _userId = player.UserId
		if not (_authenticatedPlayers[_userId] ~= nil) then
			return Result:err(NetworkError.new("Player not authenticated"))
		end
		return Result:ok(nil)
	end
	function NetworkValidationService:authenticatePlayer(playerId)
		local _authenticatedPlayers = self.authenticatedPlayers
		local _playerId = playerId
		_authenticatedPlayers[_playerId] = true
		self:logInfo(`Player {playerId} authenticated`)
	end
	function NetworkValidationService:deauthenticatePlayer(playerId)
		local _authenticatedPlayers = self.authenticatedPlayers
		local _playerId = playerId
		_authenticatedPlayers[_playerId] = nil
		self:logInfo(`Player {playerId} deauthenticated`)
	end
	function NetworkValidationService:validatePlayerPosition(player, reportedPosition, maxDistance)
		if maxDistance == nil then
			maxDistance = 100
		end
		local character = player.Character
		if not character then
			return Result:err(NetworkError.new("Player has no character"))
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return Result:err(NetworkError.new("Player has no HumanoidRootPart"))
		end
		local actualPosition = humanoidRootPart.Position
		local _reportedPosition = reportedPosition
		local distance = (actualPosition - _reportedPosition).Magnitude
		if distance > maxDistance then
			self:logWarning(`Position validation failed for player {player.Name}: distance {distance}`)
			return Result:err(NetworkError.new("Invalid player position"))
		end
		return Result:ok(nil)
	end
	function NetworkValidationService:validatePlayerExists(playerId)
		local player = Players:GetPlayerByUserId(playerId)
		if not player then
			return Result:err(NetworkError.new(`Player with ID {playerId} not found`))
		end
		return Result:ok(player)
	end
	function NetworkValidationService:startCleanupTask()
		task.spawn(function()
			while self.isInitialized and not self.isShutdown do
				self:cleanupExpiredEntries()
				task.wait(60)
			end
		end)
	end
	function NetworkValidationService:cleanupExpiredEntries()
		local now = tick() * 1000
		local expiredKeys = {}
		for key, entry in self.rateLimitMap do
			if now - entry.windowStart > 300000 then
				-- 5 minutes
				table.insert(expiredKeys, key)
			end
		end
		for _, key in expiredKeys do
			self.rateLimitMap[key] = nil
		end
		if #expiredKeys > 0 then
			self:logDebug(`Cleaned up {#expiredKeys} expired rate limit entries`)
		end
	end
end
return {
	NetworkValidationService = NetworkValidationService,
}
