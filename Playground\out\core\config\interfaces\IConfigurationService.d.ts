import { Config<PERSON><PERSON> } from "../../foundation/types/BrandedTypes";
import { Result } from "../../foundation/types/Result";
import { Error } from "../../foundation/types/RobloxError";
export interface IConfigurationService {
    get<T>(key: Config<PERSON><PERSON>, defaultValue?: T): Result<T, Error>;
    set<T>(key: Config<PERSON><PERSON>, value: T): Result<void, Error>;
    has(key: Config<PERSON><PERSON>): boolean;
    remove(key: ConfigKey): Result<void, Error>;
    getAll(): Record<string, unknown>;
    clear(): void;
}
