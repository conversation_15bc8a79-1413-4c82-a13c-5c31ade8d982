export interface LoadingTask {
    name: string;
    weight: number;
    task: () => Promise<void> | void;
}
export interface SplashScreenState {
    isVisible: boolean;
    loadingProgress: number;
    loadingText: string;
}
export declare class SplashScreenManager {
    private static instance?;
    private loadingTasks;
    private currentProgress;
    private currentText;
    private isVisible;
    private onStateChange?;
    private constructor();
    static getInstance(): SplashScreenManager;
    /**
     * Add a loading task to be executed during splash screen
     */
    addLoadingTask(task: LoadingTask): void;
    /**
     * Add multiple loading tasks
     */
    addLoadingTasks(tasks: LoadingTask[]): void;
    /**
     * Set the state change callback
     */
    onStateChanged(callback: (state: SplashScreenState) => void): void;
    /**
     * Start the loading process
     */
    startLoading(): Promise<void>;
    /**
     * Hide the splash screen
     */
    hide(): void;
    /**
     * Get current state
     */
    getState(): SplashScreenState;
    /**
     * Setup default core framework loading tasks
     */
    setupDefaultCoreTasks(): void;
    private notifyStateChange;
    private delay;
}
