import { RemoteDataStoreResult } from "../base/DataStoreBase";
export interface AddExperienceResult extends RemoteDataStoreResult {
    data?: {
        level: number;
        experience: number;
        leveledUp: boolean;
    };
}
export interface AddExperienceRequestData {
    amount: number;
}
export interface UnlockAchievementResult extends RemoteDataStoreResult {
    data?: boolean;
}
export interface UnlockAchievementRequestData {
    achievementId: string;
}
