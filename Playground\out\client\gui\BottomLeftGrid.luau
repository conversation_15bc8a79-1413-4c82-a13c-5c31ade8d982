-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Button = _core.Button
local VerticalFrame = _core.VerticalFrame
local ClientState = _core.ClientState
local useUIState = _core.useUIState
local WorldTestingPanel = TS.import(script, script.Parent, "WorldTestingPanel").WorldTestingPanel
local DebugPanel = TS.import(script, script.Parent, "DebugPanel").DebugPanel
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function BottomLeftGrid(_props)
	-- Use Core state management instead of local state
	local worldTestingState = useUIState("worldTestingPanel")
	local debugState = useUIState("debugPanel")
	local debugPanelOpen, setDebugPanelOpen = React.useState(false)
	-- Get responsive manager for dynamic positioning (memoized)
	local responsiveManager = React.useMemo(function()
		return ResponsiveManager:getInstance()
	end, {})
	-- Memoize expensive calculations
	local responsiveSettings = React.useMemo(function()
		local safeAreaInsets = responsiveManager:getSafeAreaInsets()
		local containerWidth = if responsiveManager:isMobile() then 100 else 120
		local containerHeight = if responsiveManager:isMobile() then 160 else 200
		local marginLeft = responsiveManager:getResponsiveMargin(16)
		local marginBottom = responsiveManager:getResponsiveMargin(20) + safeAreaInsets.bottom
		return {
			containerWidth = containerWidth,
			containerHeight = containerHeight,
			marginLeft = marginLeft,
			marginBottom = marginBottom,
		}
	end, { responsiveManager })
	-- Memoize button handlers
	local handleWorldClick = React.useCallback(function()
		ClientState:updateUI("worldTestingPanel", {
			isOpen = true,
		})
	end, {})
	local handleDebugClick = React.useCallback(function()
		setDebugPanelOpen(true)
	end, {})
	-- Memoize frame props
	local frameProps = React.useMemo(function()
		return {
			backgroundTransparency = 1,
			size = UDim2.new(0, responsiveSettings.containerWidth, 0, responsiveSettings.containerHeight),
			position = UDim2.new(0, responsiveSettings.marginLeft, 1, -responsiveSettings.marginBottom),
			anchorPoint = Vector2.new(0, 1),
			spacing = 8,
			padding = 0,
			responsive = true,
			responsiveMargin = true,
		}
	end, { responsiveSettings })
	local _attributes = table.clone(frameProps)
	setmetatable(_attributes, nil)
	return React.createElement(React.Fragment, nil, React.createElement(VerticalFrame, _attributes, React.createElement(Button, {
		text = "🌍 World",
		variant = "primary",
		onClick = handleWorldClick,
		LayoutOrder = 5,
		responsive = true,
	}), React.createElement(Button, {
		text = "🔧 Debug",
		variant = "secondary",
		onClick = handleDebugClick,
		LayoutOrder = 6,
		responsive = true,
	})), React.createElement(WorldTestingPanel), React.createElement(DebugPanel, {
		isOpen = debugPanelOpen,
		onClose = function()
			return setDebugPanelOpen(false)
		end,
	}))
end
return {
	BottomLeftGrid = BottomLeftGrid,
}
