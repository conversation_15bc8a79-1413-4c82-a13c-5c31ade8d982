import { IErrorHandlingService } from "../interfaces/IErrorHandlingService";
import { BaseService } from "../../foundation/BaseService";
import { ErrorEntry, ErrorSeverity, ErrorCategory, ErrorContext } from "../types/ErrorTypes";
import { Result } from "../../foundation/types/Result";
import { Error } from "../../foundation/types/RobloxError";
export declare class ErrorHandlingService extends BaseService implements IErrorHandlingService {
    private errors;
    private errorIdCounter;
    constructor();
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    logApplicationError(message: string, severity: ErrorSeverity, category: ErrorCategory, context?: ErrorContext): string;
    getErrors(severity?: ErrorSeverity, category?: ErrorCategory): ErrorEntry[];
    resolveError(errorId: string, resolution: string): Result<void, Error>;
    clearResolvedErrors(): void;
    getErrorCount(severity?: ErrorSeverity): number;
    logNetworkError(message: string, context?: ErrorContext): string;
    logValidationError(message: string, context?: ErrorContext): string;
    logSystemError(message: string, context?: ErrorContext): string;
    logUserInputError(message: string, context?: ErrorContext): string;
}
