-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _react = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local useState = _react.useState
local useEffect = _react.useEffect
local ZIndexManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager").ZIndexManager
--[[
	*
	 * React Hook for automatic Z-Index management
	 * Ensures that UI elements automatically layer correctly
	 
]]
local function useZIndex(elementId, bringToFrontOnMount)
	if bringToFrontOnMount == nil then
		bringToFrontOnMount = true
	end
	local zIndex, setZIndex = useState(1000)
	useEffect(function()
		-- Get the next Z-Index when component mounts
		local newZIndex = if bringToFrontOnMount then ZIndexManager:getNextZIndex(elementId) else ZIndexManager:getCurrentZIndex()
		setZIndex(newZIndex)
		-- Cleanup when component unmounts
		return function()
			if elementId ~= "" and elementId then
				ZIndexManager:unregister(elementId)
			end
		end
	end, { elementId, bringToFrontOnMount })
	-- Function to bring this element to front manually
	local bringToFront = function()
		local _condition = elementId
		if _condition == nil then
			_condition = `element-{math.random()}`
		end
		local newZIndex = ZIndexManager:bringToFront(_condition)
		setZIndex(newZIndex)
		return newZIndex
	end
	return {
		zIndex = zIndex,
		bringToFront = bringToFront,
		currentHighest = ZIndexManager:getCurrentZIndex(),
	}
end
return {
	useZIndex = useZIndex,
}
