-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
-- Simple placeholder DropdownButton component
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local function DropdownButton(props)
	local _attributes = {}
	local _result = props.selectedOption
	if _result ~= nil then
		_result = _result.text
	end
	local _condition = _result
	if _condition == nil then
		_condition = props.placeholder
		if _condition == nil then
			_condition = "Select..."
		end
	end
	_attributes.Text = _condition
	_attributes.Size = UDim2.new(1, 0, 1, 0)
	_attributes.BackgroundTransparency = 1
	_attributes.TextColor3 = Color3.fromHex(COLORS.text.main)
	return React.createElement("frame", {
		Size = props.size or UDim2.new(0, 150, 0, 40),
		BackgroundTransparency = 1,
		LayoutOrder = props.layoutOrder,
	}, React.createElement("textlabel", _attributes))
end
return {
	DropdownButton = DropdownButton,
}
