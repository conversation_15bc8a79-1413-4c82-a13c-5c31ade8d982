import { Ability } from "../types/Ability";
import { AbilityType } from "../types/AbilityType";

// Dictionary of all available abilities
export const ABILITIES: Record<string, Ability> = {
	CAPSULE_EFFECT: {
		id: "CAPSULE_EFFECT",
		name: "Energy Capsule",
		description: "Launch an energy capsule that explodes on impact",
		type: AbilityType.ATTACK,
		cooldown: 5,
		damage: 25,
		range: 50,
	},
	HAKI_BURST: {
		id: "HAKI_BURST",
		name: "<PERSON><PERSON>urs<PERSON>",
		description: "Release a burst of Haki energy, pushing back nearby enemies",
		type: AbilityType.DEFENSE,
		cooldown: 8,
		range: 15,
	},
	GEAR_SECOND: {
		id: "GEAR_SECOND",
		name: "Gear Second",
		description: "Increase movement and attack speed temporarily",
		type: AbilityType.MOVEMENT,
		cooldown: 15,
		duration: 10,
	},
	DEVIL_FRUIT_SLAM: {
		id: "DEVIL_FRUIT_SLAM",
		name: "Devil Fruit Slam",
		description: "Slam the ground with devil fruit power, creating a shockwave",
		type: AbilityType.ATTACK,
		cooldown: 12,
		damage: 40,
		range: 20,
	},
	ORB_MASTERY: {
		id: "ORB_MASTERY",
		name: "Orb Mastery",
		description: "Summon orbiting energy orbs that attack nearby enemies",
		type: AbilityType.SPECIAL,
		cooldown: 20,
		damage: 15,
		duration: 30,
	},
	LIGHTNING_STRIKE: {
		id: "LIGHTNING_STRIKE",
		name: "Lightning Strike",
		description: "Call down lightning at the target location",
		type: AbilityType.ATTACK,
		cooldown: 10,
		damage: 35,
		range: 60,
	},
	ROOM: {
		id: "ROOM",
		name: "Room",
		description: "Create a spherical operating room that expands outward",
		type: AbilityType.SPECIAL,
		cooldown: 30,
		range: 50,
		icon: "🔵", // Blue circle emoji
	},
	QUAKE_PUNCH: {
		id: "QUAKE_PUNCH",
		name: "Quake Punch",
		description: "Create devastating cracks in the air with earthquake power",
		type: AbilityType.ATTACK,
		cooldown: 3,
		damage: 35,
		range: 25,
	},
	HAKI_DOMINANCE: {
		id: "HAKI_DOMINANCE",
		name: "Haki Dominance",
		description: "Rayleigh's overwhelming Haki that fills the entire map with red lightning",
		type: AbilityType.SPECIAL,
		cooldown: 25,
		damage: 50,
		range: 200,
		duration: 8,
		icon: "🔴",
	},
	ICE_AGE: {
		id: "ICE_AGE",
		name: "Ice Age",
		description: "Aokiji's ultimate technique that freezes the entire battlefield in ice",
		type: AbilityType.SPECIAL,
		cooldown: 30,
		damage: 40,
		range: 300,
		duration: 12,
		icon: "❄️",
	},
	FIRE_FIST: {
		id: "FIRE_FIST",
		name: "Fire Fist",
		description: "Ace's signature technique that launches a massive fire projectile",
		type: AbilityType.ATTACK,
		cooldown: 8,
		damage: 60,
		range: 150,
		duration: 5,
		icon: "🔥",
	},
	THREE_SWORD_STYLE: {
		id: "THREE_SWORD_STYLE",
		name: "Three Sword Style",
		description: "Zoro's signature technique with devastating triple slash attacks",
		type: AbilityType.ATTACK,
		cooldown: 10,
		damage: 75,
		range: 100,
		duration: 4,
		icon: "⚔️",
	},
};
