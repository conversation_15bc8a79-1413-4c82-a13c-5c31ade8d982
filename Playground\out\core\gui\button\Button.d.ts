interface ButtonProps {
    text: string;
    onClick: () => void;
    disabled?: boolean;
    loading?: boolean;
    variant?: "primary" | "secondary" | "danger";
    LayoutOrder?: number;
    size?: UDim2;
    autoSize?: boolean;
    minWidth?: number;
    padding?: number;
    responsive?: boolean;
    ariaLabel?: string;
    tooltip?: string;
}
export declare function Button(props: ButtonProps): JSX.Element;
export {};
