import { UserInputService } from "@rbxts/services";
import { PlayerMovement } from "./PlayerMovement";

export class MovementExample {
	private movement: PlayerMovement;

	constructor() {
		this.movement = new PlayerMovement();
		this.setupControls();
	}

	private setupControls(): void {
		// Example controls for testing movement
		UserInputService.InputBegan.Connect((input, gameProcessed) => {
			if (gameProcessed) return;

			switch (input.KeyCode) {
				case Enum.KeyCode.Q:
					// Dash forward
					this.movement.dash(new Vector3(0, 0, -1), 80, 0.5);
					break;

				case Enum.KeyCode.E:
					// Launch upward
					this.movement.launch(60, 20);
					break;

				case Enum.KeyCode.R:
					// Super speed
					this.movement.setWalkSpeed(100);
					break;

				case Enum.KeyCode.T:
					// Normal speed
					this.movement.setWalkSpeed(16);
					break;

				case Enum.KeyCode.Y:
					// Jump
					this.movement.jump();
					break;

				case Enum.KeyCode.U:
					// Teleport to spawn
					this.movement.teleport(new Vector3(0, 10, 0));
					break;

				case Enum.KeyCode.I:
					// Enable custom movement
					this.movement.setupCustomMovement(25);
					break;

				case Enum.KeyCode.O:
					// Restore default movement
					this.movement.restoreDefaultMovement();
					break;

				case Enum.KeyCode.P: {
					// Check if moving
					const isMoving = this.movement.isMoving();
					const velocity = this.movement.getVelocity();
					print(`Moving: ${isMoving}, Velocity: ${velocity}`);
					break;
				}
			}
		});

		print("🎮 Movement controls setup:");
		print("Q - Dash forward");
		print("E - Launch upward");
		print("R - Super speed");
		print("T - Normal speed");
		print("Y - Jump");
		print("U - Teleport to spawn");
		print("I - Enable custom WASD movement");
		print("O - Restore default movement");
		print("P - Check movement status");
	}
}

// Initialize the movement example
new MovementExample();
