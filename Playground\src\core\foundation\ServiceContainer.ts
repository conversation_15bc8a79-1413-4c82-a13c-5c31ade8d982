import { Result } from "./types/Result";
import { ServiceLifecycle } from "./enums/ServiceLifecycle";
import { IService } from "./interfaces/IService";
import { ServiceDescriptor } from "./interfaces/ServiceDescriptor";
import { ServiceError } from "./errors/ServiceError";
import { createError } from "./types/RobloxError";

export class ServiceContainer {
	private static instance: ServiceContainer;
	private services = new Map<string, ServiceDescriptor>();
	private singletons = new Map<string, IService>();
	private isInitialized = false;

	private constructor() {}

	public static getInstance(): ServiceContainer {
		if (!ServiceContainer.instance) {
			ServiceContainer.instance = new ServiceContainer();
		}
		return ServiceContainer.instance;
	}

	public register<T extends IService>(
		name: string,
		factory: () => T,
		lifecycle: ServiceLifecycle = ServiceLifecycle.Singleton,
		dependencies: string[] = [],
	): Result<void, ServiceError> {
		if (this.services.has(name)) {
			return Result.err(new ServiceError(`Service '${name}' is already registered`));
		}

		this.services.set(name, {
			name,
			factory,
			lifecycle,
			dependencies,
			isInitialized: false,
		});

		return Result.ok(undefined);
	}

	public resolve<T extends IService>(name: string): Result<T, ServiceError> {
		const descriptor = this.services.get(name);
		if (!descriptor) {
			return Result.err(new ServiceError(`Service '${name}' is not registered`));
		}

		if (descriptor.lifecycle === ServiceLifecycle.Singleton) {
			const existing = this.singletons.get(name);
			if (existing) {
				return Result.ok(existing as T);
			}
		}

		const dependencyResult = this.resolveDependencies(descriptor.dependencies);
		if (dependencyResult.isError()) {
			return Result.err(dependencyResult.getError());
		}

		try {
			const service = descriptor.factory() as T;

			if (descriptor.lifecycle === ServiceLifecycle.Singleton) {
				this.singletons.set(name, service);
			}

			return Result.ok(service);
		} catch (error) {
			return Result.err(new ServiceError(`Failed to create service '${name}': ${error}`));
		}
	}

	public async initialize(): Promise<Result<void, ServiceError>> {
		if (this.isInitialized) {
			return Result.ok(undefined);
		}

		const initOrder = this.calculateInitializationOrder();
		if (initOrder.isError()) {
			return Result.err(initOrder.getError());
		}

		for (const serviceName of initOrder.getValue()) {
			const serviceResult = this.resolve(serviceName);
			if (serviceResult.isError()) {
				return Result.err(serviceResult.getError());
			}

			const service = serviceResult.getValue();
			if (service.initialize !== undefined) {
				const initResult = await service.initialize();
				if (initResult.isError()) {
					return Result.err(
						new ServiceError(
							`Failed to initialize service '${serviceName}': ${initResult.getError().message}`,
						),
					);
				}
			}

			const descriptor = this.services.get(serviceName)!;
			descriptor.isInitialized = true;
		}

		this.isInitialized = true;
		return Result.ok(undefined);
	}

	public async shutdown(): Promise<Result<void, ServiceError>> {
		const serviceNames: string[] = [];
		for (const [name] of this.singletons) {
			serviceNames.push(name);
		}
		// Reverse the array manually
		const shutdownOrder: string[] = [];
		for (let i = serviceNames.size() - 1; i >= 0; i--) {
			shutdownOrder.push(serviceNames[i]);
		}

		for (const serviceName of shutdownOrder) {
			const service = this.singletons.get(serviceName);
			if (service && service.shutdown) {
				const shutdownResult = await service.shutdown();
				if (shutdownResult.isError()) {
					print(`Warning: Failed to shutdown service '${serviceName}': ${shutdownResult.getError()}`);
				}
			}
		}

		this.singletons.clear();
		this.isInitialized = false;
		return Result.ok(undefined);
	}

	private resolveDependencies(dependencies: string[]): Result<IService[], ServiceError> {
		const resolved: IService[] = [];

		for (const dep of dependencies) {
			const result = this.resolve(dep);
			if (result.isError()) {
				return Result.err(result.getError());
			}
			resolved.push(result.getValue());
		}

		return Result.ok(resolved);
	}

	private calculateInitializationOrder(): Result<string[], ServiceError> {
		const visited = new Set<string>();
		const visiting = new Set<string>();
		const order: string[] = [];

		const visit = (serviceName: string): Result<void, ServiceError> => {
			if (visiting.has(serviceName)) {
				return Result.err(new ServiceError(`Circular dependency detected involving service '${serviceName}'`));
			}

			if (visited.has(serviceName)) {
				return Result.ok(undefined);
			}

			visiting.add(serviceName);

			const descriptor = this.services.get(serviceName);
			if (!descriptor) {
				return Result.err(new ServiceError(`Service '${serviceName}' is not registered`));
			}

			for (const dep of descriptor.dependencies) {
				const result = visit(dep);
				if (result.isError()) {
					return result;
				}
			}

			visiting.delete(serviceName);
			visited.add(serviceName);
			order.push(serviceName);

			return Result.ok(undefined);
		};

		for (const [serviceName] of this.services) {
			const result = visit(serviceName);
			if (result.isError()) {
				return Result.err(result.getError());
			}
		}

		return Result.ok(order);
	}
}
