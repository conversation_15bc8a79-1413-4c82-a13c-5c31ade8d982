import { EffectPartBuilder, FrameAnimationHelper } from "../../../../core";

export function createDustCloudShockwave(centerPosition: Vector3, maxRadius: number, duration: number): void {
	// Create multiple dust cloud rings for realistic effect
	const numClouds = 8;

	for (let cloud = 0; cloud < numClouds; cloud++) {
		// Position clouds in a circle pattern at ground level
		const angle = (cloud / numClouds) * math.pi * 2;
		const startRadius = 5;
		const cloudPos = new Vector3(
			centerPosition.X + math.cos(angle) * startRadius,
			centerPosition.Y - 2,
			centerPosition.Z + math.sin(angle) * startRadius,
		);

		// Create dust cloud using EffectPartBuilder
		const dustCloud = EffectPartBuilder.create()
			.shape(Enum.PartType.Ball)
			.size(new Vector3(2, 1, 2)) // Start as small dust puffs
			.color(Color3.fromRGB(180, 160, 140)) // Dusty brown color
			.material(Enum.Material.Sand)
			.transparency(0.4)
			.position(cloudPos)
			.spawn();

		dustCloud.Name = `DustCloud_${cloud}`;

		// Animate dust cloud expansion using FrameAnimationHelper
		const cloudDelay = cloud * 0.05; // Stagger the clouds

		FrameAnimationHelper.animate(
			`dustcloud_${cloud}_${tick()}`, // Unique ID for each cloud
			duration,
			(progress: number) => {
				// Expand and move outward
				const currentRadius = startRadius + maxRadius * progress * 0.8;
				const newPos = new Vector3(
					centerPosition.X + math.cos(angle) * currentRadius,
					centerPosition.Y - 2 + progress * 3, // Rise up slightly
					centerPosition.Z + math.sin(angle) * currentRadius,
				);
				dustCloud.Position = newPos;

				// Scale up the dust cloud
				const scale = 2 + progress * 4;
				dustCloud.Size = new Vector3(scale, scale * 0.5, scale);

				// Fade out
				dustCloud.Transparency = 0.4 + progress * 0.6;
			},
			() => {
				// Cleanup when animation completes
				dustCloud.Destroy();
			},
			cloudDelay,
		);
	}
}
