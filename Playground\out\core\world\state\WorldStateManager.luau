-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local WeatherController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "WeatherController").WeatherController
local TimeController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "TimeController").TimeController
local GravityController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "GravityController").GravityController
--[[
	*
	 * WorldStateManager - Central manager for all world environmental states
	 * Provides a unified interface for weather, time, gravity, and atmosphere management
	 
]]
local WorldStateManager
do
	WorldStateManager = setmetatable({}, {
		__tostring = function()
			return "WorldStateManager"
		end,
	})
	WorldStateManager.__index = WorldStateManager
	function WorldStateManager.new(...)
		local self = setmetatable({}, WorldStateManager)
		return self:constructor(...) or self
	end
	function WorldStateManager:constructor()
		self.eventListeners = {}
		self.weatherController = WeatherController:getInstance()
		self.timeController = TimeController:getInstance()
		self.gravityController = GravityController:getInstance()
	end
	function WorldStateManager:getInstance()
		if not WorldStateManager.instance then
			WorldStateManager.instance = WorldStateManager.new()
		end
		return WorldStateManager.instance
	end
	function WorldStateManager:applyWorldState(options, playerId)
		local previousState = self:getCurrentState()
		-- Apply weather changes
		if options.weather then
			self.weatherController:setWeather(options.weather)
			self:emitEvent("weather_change", previousState, options, playerId)
		end
		-- Apply time changes
		if options.time then
			self.timeController:setTime(options.time)
			self:emitEvent("time_change", previousState, options, playerId)
		end
		-- Apply gravity changes
		if options.gravity then
			self.gravityController:setGravity(options.gravity)
			self:emitEvent("gravity_change", previousState, options, playerId)
		end
		local _condition = playerId
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = "system"
		end
		print(`🌍 World state updated by player {_condition}`)
	end
	function WorldStateManager:getCurrentState()
		return {
			weather = {
				type = self.weatherController:getCurrentWeather(),
				intensity = 0.5,
			},
			time = {
				timeOfDay = self.timeController:getCurrentTimeOfDay(),
				clockTime = self.timeController:getCurrentClockTime(),
			},
			gravity = {
				level = self.gravityController:getCurrentGravityLevel(),
				customValue = self.gravityController:getCurrentGravityMultiplier(),
			},
		}
	end
	function WorldStateManager:getWeatherController()
		return self.weatherController
	end
	function WorldStateManager:getTimeController()
		return self.timeController
	end
	function WorldStateManager:getGravityController()
		return self.gravityController
	end
	function WorldStateManager:setStormyNight()
		self:applyWorldState({
			weather = {
				type = "thunderstorm",
				intensity = 0.8,
			},
			time = {
				timeOfDay = "night",
				transitionDuration = 3,
			},
			gravity = {
				level = "normal",
			},
		})
	end
	function WorldStateManager:setSunnyDay()
		self:applyWorldState({
			weather = {
				type = "clear",
			},
			time = {
				timeOfDay = "noon",
				transitionDuration = 3,
			},
			gravity = {
				level = "normal",
			},
		})
	end
	function WorldStateManager:setWinterStorm()
		self:applyWorldState({
			weather = {
				type = "blizzard",
				intensity = 0.9,
			},
			time = {
				timeOfDay = "dusk",
				transitionDuration = 2,
			},
			gravity = {
				level = "normal",
			},
		})
	end
	function WorldStateManager:setLowGravityDawn()
		self:applyWorldState({
			weather = {
				type = "clear",
			},
			time = {
				timeOfDay = "dawn",
				transitionDuration = 4,
			},
			gravity = {
				level = "low",
			},
		})
	end
	function WorldStateManager:setSpaceMode()
		self:applyWorldState({
			weather = {
				type = "clear",
			},
			time = {
				timeOfDay = "night",
				transitionDuration = 2,
			},
			gravity = {
				level = "zero",
			},
		})
	end
	function WorldStateManager:addEventListener(listener)
		local _eventListeners = self.eventListeners
		local _listener = listener
		table.insert(_eventListeners, _listener)
	end
	function WorldStateManager:removeEventListener(listener)
		local _eventListeners = self.eventListeners
		local _listener = listener
		local index = (table.find(_eventListeners, _listener) or 0) - 1
		if index ~= -1 then
			table.remove(self.eventListeners, index + 1)
		end
	end
	function WorldStateManager:emitEvent(eventType, previousState, newState, playerId)
		local event = {
			type = eventType,
			previousState = previousState,
			newState = newState,
			timestamp = tick(),
			playerId = playerId,
		}
		for _, listener in self.eventListeners do
			task.spawn(function()
				return listener(event)
			end)
		end
	end
	function WorldStateManager:resetToDefaults()
		self:applyWorldState({
			weather = {
				type = "clear",
			},
			time = {
				timeOfDay = "noon",
				transitionDuration = 1,
			},
			gravity = {
				level = "normal",
			},
		})
		print("🌍 World state reset to defaults")
	end
	function WorldStateManager:cleanup()
		self.weatherController:cleanup()
		self.timeController:cleanup()
		self.gravityController:cleanup()
		self.eventListeners = {}
		print("🌍 World state manager cleaned up")
	end
	function WorldStateManager:initialize()
		local manager = WorldStateManager:getInstance()
		print("🌍 World State Manager initialized")
		return manager
	end
end
return {
	WorldStateManager = WorldStateManager,
}
