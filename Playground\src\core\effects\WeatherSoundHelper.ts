import { SoundService, Workspace, RunService } from "@rbxts/services";

export interface WeatherSoundConfig {
	soundId: string;
	baseVolume: number;
	pitchRange: [number, number];
	looping: boolean;
	fadeInDuration: number;
	fadeOutDuration: number;
	is3D: boolean;
	rollOffMode: Enum.RollOffMode;
	maxDistance: number;
	minDistance: number;
}

export class WeatherSoundSystem {
	private static activeSounds: Map<string, Sound[]> = new Map();
	private static ambientSounds: Map<string, Sound> = new Map();
	private static fadeConnections: Map<string, RBXScriptConnection> = new Map();

	// Comprehensive weather sound library with realistic audio layers
	private static readonly WEATHER_SOUNDS: Map<string, WeatherSoundConfig> = new Map([
		// Rain sounds - Multiple layers for realism
		[
			"rain_light",
			{
				soundId: "rbxassetid://131961136", // Light rain drops
				baseVolume: 0.4,
				pitchRange: [0.95, 1.05],
				looping: true,
				fadeInDuration: 4,
				fadeOutDuration: 5,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 400,
				minDistance: 10,
			},
		],
		[
			"rain_medium",
			{
				soundId: "rbxassetid://131961136", // Medium rain
				baseVolume: 0.6,
				pitchRange: [0.9, 1.1],
				looping: true,
				fadeInDuration: 3,
				fadeOutDuration: 4,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 500,
				minDistance: 10,
			},
		],
		[
			"rain_heavy",
			{
				soundId: "rbxassetid://131961136", // Heavy rain downpour
				baseVolume: 0.8,
				pitchRange: [0.8, 1.0],
				looping: true,
				fadeInDuration: 2,
				fadeOutDuration: 3,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 600,
				minDistance: 10,
			},
		],
		// Thunder sounds - Various intensities
		[
			"thunder_distant",
			{
				soundId: "rbxassetid://138143457", // Distant thunder rumble
				baseVolume: 0.5,
				pitchRange: [0.6, 0.8],
				looping: false,
				fadeInDuration: 0,
				fadeOutDuration: 2,
				is3D: true,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 3000,
				minDistance: 200,
			},
		],
		[
			"thunder_close",
			{
				soundId: "rbxassetid://138143457", // Close thunder crack
				baseVolume: 0.9,
				pitchRange: [0.9, 1.3],
				looping: false,
				fadeInDuration: 0,
				fadeOutDuration: 1,
				is3D: true,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 2000,
				minDistance: 100,
			},
		],
		// Wind sounds - Atmospheric layers
		[
			"wind_light",
			{
				soundId: "rbxassetid://131961136", // Light breeze
				baseVolume: 0.25,
				pitchRange: [0.7, 0.9],
				looping: true,
				fadeInDuration: 6,
				fadeOutDuration: 8,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 300,
				minDistance: 10,
			},
		],
		[
			"wind_medium",
			{
				soundId: "rbxassetid://131961136", // Medium wind
				baseVolume: 0.4,
				pitchRange: [0.6, 0.8],
				looping: true,
				fadeInDuration: 4,
				fadeOutDuration: 6,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 400,
				minDistance: 10,
			},
		],
		[
			"wind_strong",
			{
				soundId: "rbxassetid://131961136", // Strong wind gusts
				baseVolume: 0.7,
				pitchRange: [0.4, 0.6],
				looping: true,
				fadeInDuration: 2,
				fadeOutDuration: 3,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 500,
				minDistance: 10,
			},
		],
		// Snow and winter sounds
		[
			"snow_light",
			{
				soundId: "rbxassetid://131961136", // Light snow falling
				baseVolume: 0.2,
				pitchRange: [0.8, 1.0],
				looping: true,
				fadeInDuration: 8,
				fadeOutDuration: 10,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 200,
				minDistance: 10,
			},
		],
		[
			"blizzard",
			{
				soundId: "rbxassetid://131961136", // Blizzard winds
				baseVolume: 0.8,
				pitchRange: [0.3, 0.5],
				looping: true,
				fadeInDuration: 3,
				fadeOutDuration: 4,
				is3D: false,
				rollOffMode: Enum.RollOffMode.Linear,
				maxDistance: 600,
				minDistance: 10,
			},
		],
	]);

	/**
	 * Play weather ambient sound with proper configuration
	 */
	static playWeatherAmbient(weatherType: string, intensity: number): void {
		print(`🔊 Starting ${weatherType} ambient sound at intensity ${intensity}`);

		const soundKey = this.getWeatherSoundKey(weatherType, intensity);
		const config = this.WEATHER_SOUNDS.get(soundKey);

		if (!config) {
			print(`⚠️ No sound configuration for weather: ${weatherType}`);
			return;
		}

		// Stop existing ambient sound for this weather type
		this.stopWeatherAmbient(weatherType);

		// Create and configure the sound
		const sound = new Instance("Sound");
		sound.Name = `${weatherType}_ambient`;
		sound.SoundId = config.soundId;
		sound.Volume = 0; // Start at 0 for fade in
		sound.PlaybackSpeed = math.random(config.pitchRange[0], config.pitchRange[1]);
		sound.Looped = config.looping;
		sound.Parent = Workspace;

		// Configure 3D sound properties (using modern Roblox audio system)
		if (config.is3D) {
			sound.RollOffMode = config.rollOffMode;
			// Note: Spatial audio properties are now handled by SoundGroups in modern Roblox
			// The sound will use default spatial audio behavior
		}

		// Calculate target volume based on intensity
		const targetVolume = math.clamp(config.baseVolume * intensity, 0, 1);

		// Play the sound
		const playSuccess = pcall(() => sound.Play());
		if (!playSuccess[0]) {
			print(`⚠️ Failed to play ${weatherType} sound`);
			sound.Destroy();
			return;
		}

		// Fade in the sound
		if (config.fadeInDuration > 0) {
			this.fadeSound(sound, 0, targetVolume, config.fadeInDuration);
		} else {
			sound.Volume = targetVolume;
		}

		// Store the ambient sound
		this.ambientSounds.set(weatherType, sound);

		print(`✅ Playing ${weatherType} ambient sound at ${targetVolume} volume`);
	}

	/**
	 * Fade sound volume over time with better performance
	 */
	private static fadeSound(
		sound: Sound,
		fromVolume: number,
		toVolume: number,
		duration: number,
		onComplete?: () => void,
	): void {
		const soundId = sound.Name;

		// Clear any existing fade for this sound
		const existingConnection = this.fadeConnections.get(soundId);
		if (existingConnection) {
			existingConnection.Disconnect();
		}

		const startTime = tick();
		const volumeDiff = toVolume - fromVolume;

		const fadeConnection = RunService.Heartbeat.Connect(() => {
			if (!sound.Parent) {
				this.fadeConnections.delete(soundId);
				return;
			}

			const elapsed = tick() - startTime;
			const progress = math.min(elapsed / duration, 1);

			// Use smooth easing for better audio experience
			const easedProgress = progress * progress * (3 - 2 * progress); // Smoothstep
			sound.Volume = fromVolume + volumeDiff * easedProgress;

			if (progress >= 1) {
				this.fadeConnections.delete(soundId);
				if (onComplete) {
					onComplete();
				}
			}
		});

		this.fadeConnections.set(soundId, fadeConnection);
	}

	/**
	 * Stop weather ambient sound with fade out
	 */
	static stopWeatherAmbient(weatherType: string): void {
		const sound = this.ambientSounds.get(weatherType);
		if (!sound || !sound.Parent) {
			return;
		}

		const soundKey = this.getWeatherSoundKey(weatherType, 1);
		const config = this.WEATHER_SOUNDS.get(soundKey);
		const fadeOutDuration = config?.fadeOutDuration || 2;

		print(`🔇 Stopping ${weatherType} ambient sound`);

		// Fade out and destroy
		this.fadeSound(sound, sound.Volume, 0, fadeOutDuration, () => {
			if (sound.Parent) {
				sound.Destroy();
			}
			this.ambientSounds.delete(weatherType);
		});
	}

	/**
	 * Get the appropriate sound key for weather type and intensity
	 */
	private static getWeatherSoundKey(weatherType: string, intensity: number): string {
		switch (weatherType) {
			case "rain":
				if (intensity < 0.3) return "rain_light";
				if (intensity < 0.7) return "rain_medium";
				return "rain_heavy";
			case "heavy_rain":
				return "rain_heavy";
			case "storm":
				return intensity > 0.6 ? "rain_heavy" : "rain_medium";
			case "thunderstorm":
				return "rain_heavy"; // Will also trigger thunder sounds separately
			case "snow":
				return "snow_light";
			case "blizzard":
				return "blizzard";
			case "wind":
				if (intensity < 0.4) return "wind_light";
				if (intensity < 0.7) return "wind_medium";
				return "wind_strong";
			case "thunder":
				return intensity > 0.7 ? "thunder_close" : "thunder_distant";
			default:
				return "rain_light";
		}
	}

	/**
	 * Play layered weather sounds for more realistic audio experience
	 */
	static playLayeredWeatherAmbient(weatherType: string, intensity: number): void {
		print(`🔊 Starting layered ${weatherType} ambient sounds at intensity ${intensity}`);

		// Stop existing sounds first
		this.stopWeatherAmbient(weatherType);

		// Play primary weather sound
		this.playWeatherAmbient(weatherType, intensity);

		// Add atmospheric layers based on weather type
		switch (weatherType) {
			case "rain":
			case "heavy_rain":
				// Add wind layer for rain
				if (intensity > 0.4) {
					this.playWeatherAmbient("wind", intensity * 0.6);
				}
				break;
			case "storm":
			case "thunderstorm":
				// Add wind and occasional thunder
				this.playWeatherAmbient("wind", intensity * 0.8);
				if (weatherType === "thunderstorm") {
					// Schedule random thunder sounds
					this.scheduleThunderSounds(intensity);
				}
				break;
			case "blizzard":
				// Add strong wind for blizzard
				this.playWeatherAmbient("wind", intensity * 0.9);
				break;
		}

		print(`✅ Layered ${weatherType} ambient sounds started`);
	}

	/**
	 * Schedule random thunder sounds for thunderstorms
	 */
	private static scheduleThunderSounds(intensity: number): void {
		const thunderInterval = math.max(5, 20 - intensity * 15); // 5-20 seconds between thunder

		const playThunder = () => {
			if (this.ambientSounds.has("thunderstorm")) {
				this.playWeatherAmbient("thunder", intensity);
				// Schedule next thunder
				task.wait(thunderInterval + math.random() * 10);
				playThunder();
			}
		};

		// Start thunder sequence
		task.spawn(() => {
			task.wait(math.random() * 5); // Initial delay
			playThunder();
		});
	}

	/**
	 * Stop all weather sounds
	 */
	static stopAllWeatherSounds(): void {
		print("🔇 Stopping all weather sounds");

		// Stop all ambient sounds
		this.ambientSounds.forEach((_, weatherType) => {
			this.stopWeatherAmbient(weatherType);
		});

		// Clear fade connections
		this.fadeConnections.forEach((connection) => {
			if (connection.Connected) {
				connection.Disconnect();
			}
		});
		this.fadeConnections.clear();

		// Clear any remaining active sounds
		this.activeSounds.forEach((sounds) => {
			sounds.forEach((sound) => {
				if (sound.Parent) {
					sound.Stop();
					sound.Destroy();
				}
			});
		});
		this.activeSounds.clear();

		print("✅ All weather sounds stopped");
	}
}

// Legacy alias for backward compatibility
export const WeatherSoundHelper = WeatherSoundSystem;
