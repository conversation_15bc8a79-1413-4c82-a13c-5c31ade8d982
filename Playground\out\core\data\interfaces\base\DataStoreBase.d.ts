export interface DataStoreRequest {
    action: DataStoreAction;
    data?: unknown;
}
export type DataStoreAction = "loadPlayerData" | "updateGameData" | "updateSettings" | "getStats" | "error";
export interface DataStoreResponse {
    action: DataStoreAction;
    result: RemoteDataStoreResult;
}
export interface RemoteDataStoreResult {
    success: boolean;
    data?: unknown;
    error?: string;
}
