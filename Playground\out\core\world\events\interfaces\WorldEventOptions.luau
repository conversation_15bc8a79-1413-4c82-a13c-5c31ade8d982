-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Re-export all world event interfaces from their organized locations
-- This maintains backward compatibility while providing clean architecture
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "interfaces") or {} do
	exports[_k] = _v
end
return exports
