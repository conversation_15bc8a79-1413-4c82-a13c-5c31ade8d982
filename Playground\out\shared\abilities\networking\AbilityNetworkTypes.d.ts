export interface CastAbilityRequest {
    abilityId: string;
    targetPosition?: Vector3;
    targetDirection?: Vector3;
    targetPlayer?: number;
    timestamp: number;
}
export interface CastAbilityResponse {
    success: boolean;
    abilityId: string;
    cooldownEndTime: number;
    errorMessage?: string;
}
export interface AbilityEffectData {
    abilityId: string;
    casterUserId: number;
    startPosition: Vector3;
    direction: Vector3;
    timestamp: number;
    effectId: string;
    targetPosition?: Vector3;
    targetUserId?: number;
}
