import { WeatherOptions, WeatherType } from "./interfaces/WeatherOptions";
/**
 * WeatherController - Advanced weather system with realistic effects
 * Provides weather effects with proper particles, sounds, and transitions
 */
export declare class WeatherController {
    private static instance;
    private currentWeather;
    private currentIntensity;
    private isTransitioning;
    private constructor();
    static getInstance(): WeatherController;
    /**
     * Set weather with optional smooth transition
     */
    setWeather(options: WeatherOptions): void;
    /**
     * Apply weather change immediately without transition
     */
    private applyWeatherImmediate;
    private setClearWeather;
    private setRainWeather;
    private setSnowWeather;
    private setStormWeather;
    private setThunderstormWeather;
    private setFogWeather;
    private setSandstormWeather;
    /**
     * Get current weather type
     */
    getCurrentWeather(): WeatherType;
    /**
     * Get current weather intensity
     */
    getCurrentIntensity(): number;
    /**
     * Calculate realistic wind speed based on rain intensity
     * Light rain: 0-2 m/s, Heavy rain: 5-15 m/s
     */
    private calculateRealisticWindSpeed;
    /**
     * Calculate gravity effect for rain physics
     * Subtle effect that doesn't interfere with gameplay
     */
    private calculateRainGravityEffect;
    /**
     * Calculate realistic snow fall speed
     * Snowflakes: 0.5-2 m/s terminal velocity
     */
    private calculateSnowFallSpeed;
    /**
     * Clear weather effects
     */
    private clearWeatherEffects;
    /**
     * Cleanup all weather effects
     */
    cleanup(): void;
}
