-- Compiled with roblox-ts v3.0.0
local COLORS = {
	text = {
		main = "#F1F5F9",
		secondary = "#A0AEC0",
		muted = "#6B7280",
		inverse = "#1F2937",
	},
	bg = {
		base = "#1F2023",
		secondary = "#18191B",
		surface = "#242529",
		["surface-hover"] = "#2E3033",
		["surface-pressed"] = "#1A1B1E",
		avatar = "#3B3C40",
		badge = "#2F3237",
		hover = "#2E3033",
		modal = "rgba(0, 0, 0, 0.8)",
	},
	label = {
		text = "#BCCCDC",
		focus = "#E5EFF9",
		bg = "#2F3134",
		border = "#393C3F",
		muted = "#A7B0BF",
		hover = "#D4DCE3",
	},
	span = {
		default = "#F1F5F9",
		muted = "#A0AEC0",
		highlight = "#6FC2B2",
		subtle = "#9AA3B5",
		hover = "#FFFFFF",
	},
	border = {
		base = "#404040",
		l1 = "#4A4A4A",
		l2 = "#555555",
		l3 = "#666666",
		strong = "#777777",
		focus = "#5CB2BC",
		danger = "#EF4444",
	},
	primary = "#5CB2BC",
	["primary-dark"] = "#4AA3A3",
	["primary-light"] = "#7DD3F0",
	success = "#22C55E",
	["success-dark"] = "#16A34A",
	warning = "#F59E0B",
	["warning-dark"] = "#D97706",
	error = "#EF4444",
	["error-dark"] = "#DC2626",
	info = "#5CB2BC",
	["info-dark"] = "#0891B2",
	["progress-bg"] = "#16191C",
	["progress-fill"] = "#5CB2BC",
	["account-active"] = "#2C2D2F",
	["account-active-hover"] = "#2C2D2F",
	badge = {
		bg = "#2F3237",
		text = "#D7E6F4",
		border = "#3B3E45",
	},
	ring = {
		["focus-accent"] = "#5CB2BC",
	},
	shadow = {
		sm = "rgba(0, 0, 0, 0.2)",
		md = "rgba(0, 0, 0, 0.3)",
		lg = "rgba(0, 0, 0, 0.4)",
		xl = "rgba(0, 0, 0, 0.5)",
	},
}
return {
	COLORS = COLORS,
}
