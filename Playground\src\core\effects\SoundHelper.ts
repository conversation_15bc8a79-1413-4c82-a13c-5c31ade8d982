import { SoundService, Workspace } from "@rbxts/services";

export function playSound(soundId: string, volume = 0.8, pitch = 1, parent: Instance = Workspace, delay = 0): Sound {
	const sound = new Instance("Sound");
	sound.SoundId = soundId;
	sound.Volume = volume;
	sound.PlaybackSpeed = pitch;
	sound.Parent = parent;

	if (delay > 0) {
		task.delay(delay, () => sound.Play());
	} else {
		sound.Play();
	}

	sound.Ended.Connect(() => sound.Destroy());
	return sound;
}
