-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local ReplicatedStorage = _services.ReplicatedStorage
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local ContainerFrame = _core.ContainerFrame
local VerticalFrame = _core.VerticalFrame
local Button = _core.Button
local Label = _core.Label
local IconButton = _core.IconButton
local ScrollingFrame = _core.ScrollingFrame
local EntityManager = _core.EntityManager
local EntityType = _core.EntityType
local AIController = _core.AIController
local LimbAnimator = _core.LimbAnimator
local DataStoreHelper = _core.DataStoreHelper
local DebugUtils = _core.DebugUtils
local ZIndexManager = _core.ZIndexManager
local useUIState = _core.useUIState
local ClientState = _core.ClientState
local fireServer = _core.fireServer
local Core = _core.Core
local ClientCore_Instance = _core.ClientCore_Instance
local EffectPartBuilder = _core.EffectPartBuilder
local EffectTweenBuilder = _core.EffectTweenBuilder
local playSound = _core.playSound
local createParticleExplosion = _core.createParticleExplosion
local createParticleStorm = _core.createParticleStorm
local createImpactFlash = _core.createImpactFlash
local cameraShake = _core.cameraShake
local ResponsiveManager = _core.ResponsiveManager
local Modal = _core.Modal
local function WorldTestingPanel(props)
	-- Use Core state management instead of props
	local worldTestingState = useUIState("worldTestingPanel")
	local isPanelOpen = worldTestingState.isOpen
	local player = Players.LocalPlayer
	-- Functions to control panel state
	local closePanel = function()
		ClientState:updateUI("worldTestingPanel", {
			isOpen = false,
		})
	end
	local openPanel = function()
		ClientState:updateUI("worldTestingPanel", {
			isOpen = true,
		})
	end
	-- Use debug priority Z-Index to ensure this appears above debug overlay panels
	local zIndex = ZIndexManager:getDebugZIndex("world-testing-panel")
	-- FORCE an extremely high z-index to ensure it's always on top
	local forceHighZIndex = math.max(zIndex, 5000)
	-- Debug logging to verify z-index values
	React.useEffect(function()
		local timestamp = tick()
		print(`🌍 [{timestamp}] World Testing Panel created with z-index: {zIndex}`)
		print(`🌍 [{timestamp}] World Testing Panel FORCING z-index to: {forceHighZIndex}`)
		print(`🌍 [{timestamp}] World Testing Panel applying z-index {forceHighZIndex} to ContainerFrame`)
		-- Check actual ScreenGui DisplayOrder values
		local player = Players.LocalPlayer
		local playerGui = player:WaitForChild("PlayerGui")
		local mainReactGUI = playerGui:FindFirstChild("MainReactGUI")
		local debugGUI = playerGui:FindFirstChild("DebugGUI")
		if mainReactGUI then
			print(`🎮 [{timestamp}] MainReactGUI DisplayOrder: {mainReactGUI.DisplayOrder}`)
		else
			print(`❌ [{timestamp}] MainReactGUI not found!`)
		end
		if debugGUI then
			print(`🔧 [{timestamp}] DebugGUI DisplayOrder: {debugGUI.DisplayOrder}`)
		else
			print(`❌ [{timestamp}] DebugGUI not found!`)
		end
		print(`🌍 [{timestamp}] *** DisplayOrder determines ScreenGui layering - higher values appear on top ***`)
		-- Print z-index summary after a short delay to see all assignments
		wait(0.1)
		ZIndexManager:printZIndexSummary()
	end, { zIndex, forceHighZIndex })
	-- Get player position for testing
	local getPlayerPosition = function()
		local character = player.Character
		local _result = character
		if _result ~= nil then
			_result = _result:FindFirstChild("HumanoidRootPart")
		end
		local humanoidRootPart = _result
		local _result_1 = humanoidRootPart
		if _result_1 ~= nil then
			_result_1 = _result_1.Position
		end
		local _condition = _result_1
		if not _condition then
			_condition = Vector3.new(0, 10, 0)
		end
		return _condition
	end
	-- Send world test request to server using Core framework
	local sendWorldTestRequest = function(testType)
		local position = getPlayerPosition()
		local result = fireServer(Core.eventName("WorldTesting"), {
			testType = testType,
			position = position,
			playerId = player.UserId,
		})
		if result:isError() then
			warn(`Failed to send world test: {result:getError().message}`)
		else
			print(`🌍 Sent {testType} test request to server`)
		end
	end
	-- Gravity test functions
	local testLowGravity = function()
		return sendWorldTestRequest("low_gravity_world")
	end
	local testHighGravity = function()
		return sendWorldTestRequest("high_gravity_world")
	end
	local testZeroGravity = function()
		return sendWorldTestRequest("zero_gravity_world")
	end
	local testNormalGravity = function()
		return sendWorldTestRequest("normal_gravity_world")
	end
	-- Weather test functions
	local testClearWeather = function()
		return sendWorldTestRequest("clear_weather")
	end
	local testRain = function()
		return sendWorldTestRequest("rain_weather")
	end
	local testHeavyRain = function()
		return sendWorldTestRequest("heavy_rain_weather")
	end
	local testSnow = function()
		return sendWorldTestRequest("snow_weather")
	end
	local testBlizzard = function()
		return sendWorldTestRequest("blizzard_weather")
	end
	local testStorm = function()
		return sendWorldTestRequest("storm_weather")
	end
	local testThunderstorm = function()
		return sendWorldTestRequest("thunderstorm_weather")
	end
	local testFog = function()
		return sendWorldTestRequest("fog_weather")
	end
	local testSandstorm = function()
		return sendWorldTestRequest("sandstorm_weather")
	end
	-- Day/Night test functions
	local testDawn = function()
		return sendWorldTestRequest("dawn_time")
	end
	local testNoon = function()
		return sendWorldTestRequest("noon_time")
	end
	local testDusk = function()
		return sendWorldTestRequest("dusk_time")
	end
	local testNight = function()
		return sendWorldTestRequest("night_time")
	end
	-- Entity Management test functions
	local entityManager = EntityManager:getInstance()
	local spawnNPC = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(5, 0, 0)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.NPC,
			position = position,
			data = {
				name = "Test NPC",
				health = 100,
			},
		})
		print(`🤖 Spawned NPC with ID: {entity.id} at position: {position}`)
	end
	local spawnProjectile = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 5, 0)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.Projectile,
			position = position,
			lifetime = 5,
			data = {
				speed = 50,
				damage = 25,
			},
		})
		print(`🚀 Spawned Projectile with ID: {entity.id} at position: {position}`)
	end
	local spawnEffect = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(-5, 0, 0)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.Effect,
			position = position,
			lifetime = 3,
			data = {
				effectType = "explosion",
				intensity = 0.8,
			},
		})
		print(`✨ Spawned Effect with ID: {entity.id} at position: {position}`)
	end
	local spawnPickup = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 0, 5)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.Pickup,
			position = position,
			data = {
				itemType = "health_potion",
				value = 50,
			},
		})
		print(`💎 Spawned Pickup with ID: {entity.id} at position: {position}`)
	end
	local listEntities = function()
		local activeCount = entityManager:getActiveEntityCount()
		print(`📊 Active entities: {activeCount}`)
		local npcs = entityManager:getEntitiesByType(EntityType.NPC)
		local projectiles = entityManager:getEntitiesByType(EntityType.Projectile)
		local effects = entityManager:getEntitiesByType(EntityType.Effect)
		local pickups = entityManager:getEntitiesByType(EntityType.Pickup)
		print(`🤖 NPCs: {#npcs}`)
		print(`🚀 Projectiles: {#projectiles}`)
		print(`✨ Effects: {#effects}`)
		print(`💎 Pickups: {#pickups}`)
	end
	local findNearbyEntities = function()
		local playerPos = getPlayerPosition()
		local nearbyEntities = entityManager:getEntitiesInRadius(playerPos, 20)
		print(`🔍 Found {#nearbyEntities} entities within 20 studs of player`)
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(entity)
			local distance = (entity.position - playerPos).Magnitude
			print(`  - {entity.type} (ID: {entity.id}) at distance: {math.floor(distance)} studs`)
		end
		for _k, _v in nearbyEntities do
			_callback(_v, _k - 1, nearbyEntities)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	local cleanupAllEntities = function()
		local beforeCount = entityManager:getActiveEntityCount()
		entityManager:cleanup()
		print(`🧹 Cleaned up {beforeCount} entities`)
	end
	-- Data Persistence test functions using RemoteEvents
	local sendDataStoreRequest = function(action, data)
		local result = fireServer(Core.eventName("DataStoreTesting"), {
			action = action,
			data = data,
		})
		if result:isError() then
			warn(`Failed to send data store request: {result:getError().message}`)
		else
			print(`📡 Sent {action} request to server`)
		end
	end
	-- Set up response handler
	React.useEffect(function()
		local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
		local _result = remoteEventsFolder
		if _result ~= nil then
			_result = _result:FindFirstChild("DATA_STORE_RESPONSE")
		end
		local responseEvent = _result
		if responseEvent then
			local connection = responseEvent.OnClientEvent:Connect(function(response)
				local typedResponse = response
				local _binding = typedResponse
				local action = _binding.action
				local result = _binding.result
				if result.success then
					repeat
						if action == "loadPlayerData" then
							if result.data ~= nil then
								local playerData = result.data
								print(`📊 Loaded player data:`)
								print(`  User ID: {playerData.userId}`)
								print(`  Created: {playerData.createdAt}`)
								print(`  Last Login: {playerData.lastLogin}`)
								print(`  Playtime: {math.floor(playerData.playtime)} seconds`)
								print(`  Total Sessions: {playerData.metadata.totalSessions}`)
								print(`  Music Volume: {playerData.settings.musicVolume}`)
								print(`  Game Data:`, playerData.gameData)
							end
							break
						end
						if action == "updateGameData" then
							if result.data ~= nil then
								local updatedData = result.data
								print(`🎮 Updated game data!`)
								print(`  Game Data:`, updatedData.gameData)
							end
							break
						end
						if action == "updateSettings" then
							if result.data ~= nil then
								local settings = result.data
								print(`⚙️ Updated settings:`)
								print(`  Music Volume: {settings.musicVolume}`)
								print(`  Graphics: {settings.graphics}`)
							end
							break
						end
						if action == "getStats" then
							if result.data ~= nil then
								local stats = result.data
								print(`📈 DataStore Statistics:`)
								print(`  Cache entries: {stats.cacheSize}`)
								print(`  Loaded players: {stats.loadedPlayers}`)
								print(`  Player loaded: {stats.isPlayerLoaded}`)
							end
							break
						end
					until true
				else
					print(`❌ {action} failed: {result.error}`)
				end
			end)
			return function()
				return connection:Disconnect()
			end
		end
	end, {})
	local testLoadPlayerData = function()
		sendDataStoreRequest("loadPlayerData")
	end
	local testUpdateGameData = function()
		sendDataStoreRequest("updateGameData", {
			gameDataUpdates = {
				coins = 100,
				level = 5,
				experience = 250,
				lastAction = "test_update",
				timestamp = tick(),
			},
		})
	end
	local testUpdateSettings = function()
		sendDataStoreRequest("updateSettings", {
			musicVolume = 0.6,
			graphics = "High",
		})
	end
	local testDataStoreStats = function()
		sendDataStoreRequest("getStats")
	end
	local enableStudioDataStore = function()
		-- Access the DataStoreHelper directly to enable Studio testing
		local dataStore = DataStoreHelper:getInstance()
		dataStore:enableStudioTesting()
	end
	local disableAutoSave = function()
		-- Access the DataStoreHelper directly to disable auto-save
		local dataStore = DataStoreHelper:getInstance()
		dataStore:disableAutoSave()
	end
	-- AI System test functions
	local aiController = AIController:getInstance()
	local spawnedAIEntities, setSpawnedAIEntities = React.useState({})
	local spawnAINPC = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(10, 0, 0)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.NPC,
			position = position,
			data = {
				name = "AI NPC",
				aiEnabled = true,
			},
		})
		-- Register AI for this entity
		local aiAgent = aiController:registerAI(entity.id, {
			detectionRange = 30,
			followRange = 20,
			moveSpeed = 12,
			patrolRadius = 15,
		})
		local _array = {}
		local _length = #_array
		local _spawnedAIEntitiesLength = #spawnedAIEntities
		table.move(spawnedAIEntities, 1, _spawnedAIEntitiesLength, _length + 1, _array)
		_length += _spawnedAIEntitiesLength
		_array[_length + 1] = entity.id
		setSpawnedAIEntities(_array)
		print(`🤖 Spawned AI NPC with ID: {entity.id}`)
		print(`🧠 AI registered with default behaviors`)
	end
	local spawnPatrollingNPC = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(-10, 0, 10)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.NPC,
			position = position,
			data = {
				name = "Patrol NPC",
				behavior = "patrol",
			},
		})
		-- Register AI with patrol focus
		aiController:registerAI(entity.id, {
			detectionRange = 15,
			followRange = 8,
			moveSpeed = 8,
			patrolRadius = 20,
		})
		local _array = {}
		local _length = #_array
		local _spawnedAIEntitiesLength = #spawnedAIEntities
		table.move(spawnedAIEntities, 1, _spawnedAIEntitiesLength, _length + 1, _array)
		_length += _spawnedAIEntitiesLength
		_array[_length + 1] = entity.id
		setSpawnedAIEntities(_array)
		print(`🚶 Spawned Patrolling NPC with ID: {entity.id}`)
	end
	local spawnFleeingNPC = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 0, -10)
		local position = _exp + _vector3
		local entity = entityManager:spawnEntity({
			type = EntityType.NPC,
			position = position,
			data = {
				name = "Scared NPC",
				behavior = "flee",
			},
		})
		-- Register AI that prefers to flee
		local aiAgent = aiController:registerAI(entity.id, {
			detectionRange = 25,
			followRange = 5,
			moveSpeed = 16,
			patrolRadius = 10,
		})
		-- Set initial flee state
		aiAgent:setBlackboardValue("shouldFlee", true)
		local _array = {}
		local _length = #_array
		local _spawnedAIEntitiesLength = #spawnedAIEntities
		table.move(spawnedAIEntities, 1, _spawnedAIEntitiesLength, _length + 1, _array)
		_length += _spawnedAIEntitiesLength
		_array[_length + 1] = entity.id
		setSpawnedAIEntities(_array)
		print(`😱 Spawned Fleeing NPC with ID: {entity.id}`)
	end
	local makeNPCInvestigate = function()
		if #spawnedAIEntities == 0 then
			print("❌ No AI NPCs spawned yet!")
			return nil
		end
		local entityId = spawnedAIEntities[1]
		local aiAgent = aiController:getAI(entityId)
		if aiAgent then
			local _exp = getPlayerPosition()
			local _vector3 = Vector3.new(5, 0, 5)
			local investigatePos = _exp + _vector3
			aiAgent:setBlackboardValue("investigatePosition", investigatePos)
			print(`🔍 {entityId} will investigate position: {investigatePos}`)
		end
	end
	local showAIStats = function()
		local aiCount = aiController:getAICount()
		local allAIs = aiController:getAllAIs()
		print(`🧠 AI System Statistics:`)
		print(`  Total AI entities: {aiCount}`)
		print(`  Spawned entities: {#spawnedAIEntities}`)
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(aiAgent, index)
			print(`  AI {index + 1}: State = {aiAgent:getState()}`)
		end
		for _k, _v in allAIs do
			_callback(_v, _k - 1, allAIs)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	local cleanupAllAI = function()
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(entityId)
			aiController:unregisterAI(entityId)
			entityManager:destroyEntity(entityId)
		end
		for _k, _v in spawnedAIEntities do
			_callback(_v, _k - 1, spawnedAIEntities)
		end
		-- ▲ ReadonlyArray.forEach ▲
		setSpawnedAIEntities({})
		print(`🧹 Cleaned up all AI entities`)
	end
	local demonstrateDebugSystem = function()
		local playerPos = getPlayerPosition()
		-- Draw some debug lines and shapes
		local _vector3 = Vector3.new(10, 0, 0)
		DebugUtils.drawLine(playerPos, playerPos + _vector3, Color3.fromRGB(255, 0, 0), 5)
		local _vector3_1 = Vector3.new(0, 10, 0)
		DebugUtils.drawLine(playerPos, playerPos + _vector3_1, Color3.fromRGB(0, 255, 0), 5)
		local _vector3_2 = Vector3.new(0, 0, 10)
		DebugUtils.drawLine(playerPos, playerPos + _vector3_2, Color3.fromRGB(0, 0, 255), 5)
		-- Draw some spheres
		local _vector3_3 = Vector3.new(5, 5, 5)
		DebugUtils.drawSphere(playerPos + _vector3_3, 2, Color3.fromRGB(255, 255, 0), 5)
		local _vector3_4 = Vector3.new(-5, 5, -5)
		DebugUtils.drawSphere(playerPos + _vector3_4, 1.5, Color3.fromRGB(255, 0, 255), 5)
		-- Draw some text
		local _vector3_5 = Vector3.new(0, 8, 0)
		DebugUtils.drawText(playerPos + _vector3_5, "Debug System Demo!", Color3.fromRGB(255, 255, 255), 5)
		local _vector3_6 = Vector3.new(5, 3, 0)
		DebugUtils.drawText(playerPos + _vector3_6, "Red = X Axis", Color3.fromRGB(255, 0, 0), 5)
		local _vector3_7 = Vector3.new(0, 13, 0)
		DebugUtils.drawText(playerPos + _vector3_7, "Green = Y Axis", Color3.fromRGB(0, 255, 0), 5)
		local _vector3_8 = Vector3.new(0, 3, 5)
		DebugUtils.drawText(playerPos + _vector3_8, "Blue = Z Axis", Color3.fromRGB(0, 0, 255), 5)
		-- Log debug messages
		DebugUtils.log("Demo", "Debug system demonstration started", {
			position = playerPos,
		})
		DebugUtils.log("Demo", "Drawing coordinate axes and markers")
		print(`🔍 Debug demonstration active! Press F3 to toggle debug overlay`)
		print(`🎨 Visual elements will appear for 5 seconds`)
	end
	-- Existing Animation System test functions
	local limbAnimator, setLimbAnimator = React.useState()
	local initializeAnimationSystem = function()
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			print("❌ No character found! Spawn first.")
			return nil
		end
		TS.try(function()
			local limbAnim = LimbAnimator:forCharacter(character)
			setLimbAnimator(limbAnim)
			print("🎬 Existing animation system initialized!")
			print("🦴 Using LimbAnimator with moveLimb, moveBodyPart, and lunge methods")
		end, function(error)
			print(`❌ Failed to initialize animation system: {error}`)
		end)
	end
	local testLimbAnimation = function()
		if not limbAnimator then
			print("❌ Animation system not initialized!")
			return nil
		end
		print("👋 Testing limb animation...")
		-- Move right arm up (wave gesture)
		local _cFrame = CFrame.new()
		local _arg0 = CFrame.fromEulerAnglesXYZ(0, 0, math.rad(-90))
		limbAnimator:moveLimb("RightShoulder", _cFrame * _arg0, 0.5)
		print("👋 Wave animation started!")
	end
	local testPresetAnimations = function()
		if not limbAnimator then
			print("❌ Animation system not initialized!")
			return nil
		end
		print("🦾 Testing preset animations...")
		-- Test lunge animation (existing method)
		limbAnimator:lunge(Vector3.new(1, 0, 0), 5, 0.5)
		print("🏃 Lunge animation started!")
		-- Test body part movement
		local _cFrame = CFrame.new()
		local _arg0 = CFrame.fromEulerAnglesXYZ(0, math.rad(30), 0)
		limbAnimator:moveBodyPart("Head", _cFrame * _arg0, 0.5)
		print("🗣️ Head turn started!")
	end
	local showAnimationStats = function()
		if not limbAnimator then
			print("❌ Animation system not initialized!")
			return nil
		end
		print("🎬 Animation System Statistics:")
		print("  LimbAnimator: Available for moveLimb, moveBodyPart, and lunge")
		print("  AnimationBuilder: Available for complex animation sequences")
	end
	local cleanupAnimationSystem = function()
		if limbAnimator then
			setLimbAnimator(nil)
		end
		print("🧹 Animation system cleaned up")
	end
	-- Effects System test functions
	local testBasicEffects = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 5, 0)
		local position = _exp + _vector3
		print("✨ Testing basic effects...")
		-- Create a glowing sphere effect
		local sphere = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(2, 2, 2)):color(Color3.fromRGB(0, 255, 255)):material(Enum.Material.Neon):transparency(0.3):position(position):withLight(15, 10, Color3.fromRGB(0, 255, 255)):spawn()
		-- Animate the sphere
		EffectTweenBuilder["for"](EffectTweenBuilder, sphere):expand(Vector3.new(8, 8, 8)):fade(1):duration(3):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):onComplete(function()
			return sphere:Destroy()
		end):play()
		-- Play sound effect
		playSound("rbxassetid://131961136", 0.5, 1.2, sphere)
		print("🔮 Basic sphere effect created!")
	end
	local testParticleEffects = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(3, 0, 0)
		local position = _exp + _vector3
		print("🎆 Testing particle effects...")
		-- Create particle explosion
		createParticleExplosion(position, 15, Color3.fromRGB(255, 100, 0), { 10, 25 }, { 0.3, 1.0 })
		-- Create particle storm
		local _vector3_1 = Vector3.new(0, 10, 0)
		createParticleStorm(position + _vector3_1, 20, Color3.fromRGB(255, 255, 0), { 5, 15 })
		print("💥 Particle effects created!")
	end
	local testVisualEffects = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(-3, 0, 0)
		local position = _exp + _vector3
		print("🌟 Testing visual effects...")
		-- Create impact flash
		createImpactFlash(position, 6, 0.5)
		-- Create camera shake
		cameraShake(3, 0.4)
		-- Create expanding ring effect
		local ring = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(0.5, 1, 0.5)):color(Color3.fromRGB(255, 0, 255)):material(Enum.Material.ForceField):transparency(0.5):position(position):spawn()
		EffectTweenBuilder["for"](EffectTweenBuilder, ring):expand(Vector3.new(0.5, 20, 20)):fade(1):duration(2):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.Out):onComplete(function()
			return ring:Destroy()
		end):play()
		print("💫 Visual effects created!")
	end
	local testSoundEffects = function()
		print("🔊 Testing sound effects...")
		-- Test different sound effects
		playSound("rbxassetid://131961136", 0.7, 1.0)
		task.wait(0.5)
		playSound("rbxassetid://131961136", 0.5, 1.5)
		task.wait(0.5)
		playSound("rbxassetid://131961136", 0.8, 0.8)
		print("🎵 Sound effects played!")
	end
	local testComplexEffectCombo = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 8, 0)
		local position = _exp + _vector3
		print("🎭 Testing complex effect combination...")
		-- Create multiple layered effects
		for i = 0, 2 do
			local offset = Vector3.new(math.random(-5, 5), math.random(-2, 2), math.random(-5, 5))
			local effectPos = position + offset
			local effect = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(1, 1, 1)):color(Color3.fromRGB(math.random(100, 255), math.random(100, 255), math.random(100, 255))):material(Enum.Material.Neon):transparency(0.2):position(effectPos):withLight(12, 8):spawn()
			EffectTweenBuilder["for"](EffectTweenBuilder, effect):expand(Vector3.new(6, 6, 6)):fade(1):duration(2 + i * 0.5):delay(i * 0.3):easing(Enum.EasingStyle.Elastic, Enum.EasingDirection.Out):onComplete(function()
				-- Create explosion when effect completes
				createParticleExplosion(effect.Position, 8, effect.Color, { 3, 8 }, { 0.2, 0.6 })
				effect:Destroy()
			end):play()
		end
		-- Add sound sequence
		playSound("rbxassetid://131961136", 0.6, 1.0)
		task.delay(1, function()
			return playSound("rbxassetid://131961136", 0.8, 1.3)
		end)
		task.delay(2, function()
			return playSound("rbxassetid://131961136", 1.0, 0.9)
		end)
		print("🎪 Complex effect combination created!")
	end
	-- Ability System test functions
	local testAbilityValidation = function()
		print("⚔️ Testing client-side ability validation...")
		-- Test client-side ability request validation
		print("📊 Client Ability Validation Status:")
		print("  ✅ Client-side validation available")
		local _object = {
			abilityId = "ROOM_ABILITY",
		}
		local _left = "targetPosition"
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 0, 10)
		_object[_left] = _exp + _vector3
		_object.targetDirection = Vector3.new(0, 0, 1)
		_object.timestamp = tick()
		local testRequest = _object
		print("🔍 Validating ability request structure...")
		print(`  Ability ID: {testRequest.abilityId}`)
		print(`  Target Position: {testRequest.targetPosition}`)
		print(`  Timestamp: {testRequest.timestamp}`)
		-- Basic client-side validation
		local isValid = testRequest.abilityId ~= nil and testRequest.abilityId ~= "" and testRequest.targetPosition ~= nil and testRequest.timestamp > 0
		print(`  ✅ Request structure: {if isValid then "Valid" else "Invalid"}`)
		print("✅ Client ability validation test completed!")
	end
	local testAbilityCooldowns = function()
		print("⏰ Testing ability cooldowns...")
		-- Simulate ability cooldown system
		local abilities = { "ROOM_ABILITY", "QUAKE_ABILITY", "HAKI_BURST", "GEAR_SECOND" }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(abilityId, index)
			local cooldownTime = 5 + index * 2
			print(`🔥 {abilityId}: {cooldownTime}s cooldown`)
			-- Simulate cooldown end time
			local endTime = tick() + cooldownTime
			print(`  ⏳ Cooldown ends at: {endTime}`)
		end
		for _k, _v in abilities do
			_callback(_v, _k - 1, abilities)
		end
		-- ▲ ReadonlyArray.forEach ▲
		print("✅ Cooldown system test completed!")
	end
	local testAbilityEffects = function()
		local _exp = getPlayerPosition()
		local _vector3 = Vector3.new(0, 0, 8)
		local position = _exp + _vector3
		print("💥 Testing ability-style effects...")
		-- Room Ability Effect
		local roomSphere = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(1, 1, 1)):color(Color3.fromRGB(100, 200, 255)):material(Enum.Material.ForceField):transparency(0.7):position(position):withLight(25, 15, Color3.fromRGB(100, 200, 255)):spawn()
		EffectTweenBuilder["for"](EffectTweenBuilder, roomSphere):expand(Vector3.new(30, 30, 30)):duration(2):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):onComplete(function()
			-- Create inner sphere
			local innerSphere = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(25, 25, 25)):color(Color3.fromRGB(150, 220, 255)):material(Enum.Material.Neon):transparency(0.9):position(position):spawn()
			EffectTweenBuilder["for"](EffectTweenBuilder, innerSphere):fade(1):duration(3):onComplete(function()
				roomSphere:Destroy()
				innerSphere:Destroy()
			end):play()
		end):play()
		playSound("rbxassetid://131961136", 0.8, 0.7)
		print("🌐 Room ability effect created!")
	end
	-- UI Component testing functions
	local testModalOpen, setTestModalOpen = React.useState(false)
	local testDropdownOpen, setTestDropdownOpen = React.useState(false)
	local testResponsiveDesign = function()
		print("📱 Testing responsive design...")
		-- Test ResponsiveManager
		local responsiveManager = ResponsiveManager:getInstance()
		print(`📊 Responsive Manager Status:`)
		print(`  Manager initialized: {responsiveManager ~= nil}`)
		print(`  Testing responsive behavior...`)
		-- Test responsive behavior by checking current viewport
		local _viewport = game:GetService("Workspace").CurrentCamera
		if _viewport ~= nil then
			_viewport = _viewport.ViewportSize
		end
		local viewport = _viewport
		if viewport then
			print(`  Current Viewport: {viewport.X} x {viewport.Y}`)
		end
		print("✅ Responsive design test completed!")
	end
	local testZIndexManager = function()
		print("🔢 Testing ZIndex Manager...")
		-- Test ZIndex management
		local debugZIndex = ZIndexManager:getZIndex("DEBUG_PANEL")
		local worldTestZIndex = ZIndexManager:getZIndex("WORLD_TEST_PANEL")
		local modalZIndex = ZIndexManager:getZIndex("MODAL")
		print(`📊 ZIndex Values:`)
		print(`  Debug Panel: {debugZIndex}`)
		print(`  World Test Panel: {worldTestZIndex}`)
		print(`  Modal: {modalZIndex}`)
		print("✅ ZIndex Manager test completed!")
	end
	local testModalSystem = function()
		print("🪟 Testing Modal System...")
		setTestModalOpen(true)
		print("✅ Test modal opened!")
	end
	local testUIComponents = function()
		print("🎨 Testing UI Components...")
		-- Test various UI states
		print("📊 UI Component Status:")
		print(`  Modal Open: {testModalOpen}`)
		print(`  Dropdown Open: {testDropdownOpen}`)
		print(`  Panel Open: {isPanelOpen}`)
		print("✅ UI Components test completed!")
	end
	-- Networking & Validation testing functions
	local testNetworkService = function()
		print("🌐 Testing Client Network Service...")
		-- On client side, we test ClientCore networking capabilities
		print("📊 Client Network Service Status:")
		print(`  ✅ ClientCore available: {ClientCore_Instance ~= nil}`)
		-- Test available RemoteEvents
		local availableEvents = ClientCore_Instance:getEventNames()
		print(`  📡 Available RemoteEvents: {#availableEvents}`)
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(eventName)
			print(`    - {eventName}`)
		end
		for _k, _v in availableEvents do
			_callback(_v, _k - 1, availableEvents)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Test network communication with an existing event
		local testData = {
			testType = "client_network_test",
			timestamp = tick(),
			playerId = player.UserId,
		}
		-- Use WorldTesting event which we know exists
		local result = fireServer(Core.eventName("WorldTesting"), testData)
		if result:isError() then
			print(`❌ Client network test failed: {result:getError().message}`)
		else
			print("✅ Client network test sent successfully!")
		end
	end
	local testValidationService = function()
		print("🔍 Testing Client-Side Validation...")
		-- On client side, we test basic data validation capabilities
		print("📊 Client Validation Status:")
		print("  ✅ Client-side validation available")
		print("  🔍 Testing data validation patterns...")
		-- Test various validation scenarios (client-side validation)
		local testCases = { {
			data = {
				playerId = player.UserId,
				action = "test",
			},
			expected = "valid",
		}, {
			data = {
				playerId = -1,
				action = "invalid",
			},
			expected = "invalid",
		}, {
			data = nil,
			expected = "invalid",
		} }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(testCase, index)
			local _condition = testCase.data
			if _condition then
				local _data = testCase.data
				_condition = typeof(_data) == "table"
				if _condition then
					_condition = testCase.data.playerId ~= nil and testCase.data.playerId > 0
				end
			end
			local isValid = _condition
			local result = if isValid then "✅ Valid" else "❌ Invalid"
			print(`  Test {index + 1}: {result} - {testCase.expected} data`)
		end
		for _k, _v in testCases do
			_callback(_v, _k - 1, testCases)
		end
		-- ▲ ReadonlyArray.forEach ▲
		print("✅ Client validation test completed!")
	end
	local testStateManagement = function()
		print("📊 Testing State Management...")
		-- Create a test state manager
		local testStateManager = Core.createStateManager({
			counter = 0,
			testValue = "initial",
		}, "WorldTestingState")
		print("📊 State Manager Status:")
		print("  ✅ State manager created")
		-- Test state operations
		local currentState = testStateManager:getState()
		print(`  Current state: counter={currentState.counter}, testValue="{currentState.testValue}"`)
		-- Update state
		testStateManager:dispatch({
			type = "UPDATE_COUNTER",
			payload = {
				counter = currentState.counter + 1,
			},
			reducer = function(state, payload)
				local _object = table.clone(state)
				setmetatable(_object, nil)
				_object.counter = payload.counter
				return _object
			end,
		})
		local newState = testStateManager:getState()
		print(`  Updated state: counter={newState.counter}`)
		print("✅ State management test completed!")
	end
	-- Advanced Systems testing functions
	local testCoreFramework = function()
		print("🚀 Testing Core Framework...")
		local coreInstance = Core.getInstance()
		print("📊 Core Framework Status:")
		print(`  ✅ Core instance available: {coreInstance ~= nil}`)
		-- Test branded types
		local testPlayerId = Core.playerId(player.UserId)
		local testEntityId = Core.entityId("test-entity-123")
		local testEventName = Core.eventName("TestEvent")
		print("🏷️ Branded Types Test:")
		print(`  Player ID: {testPlayerId}`)
		print(`  Entity ID: {testEntityId}`)
		print(`  Event Name: {testEventName}`)
		print("✅ Core Framework test completed!")
	end
	local testErrorHandling = function()
		print("⚠️ Testing Client Error Handling...")
		TS.try(function()
			print("📊 Client Error Handling Status:")
			-- Test client-side error handling patterns
			print("  🔍 Testing Result pattern with client operations...")
			-- Test network communication error handling
			local invalidEventResult = fireServer("InvalidEvent", {})
			if invalidEventResult:isError() then
				print(`  ✅ Network error handled: {invalidEventResult:getError().message}`)
			else
				print("  ❌ Expected network error but got success")
			end
			-- Test ClientCore event availability
			local isEventAvailable = ClientCore_Instance:isEventAvailable("NonExistentEvent")
			print(`  ✅ Event availability check: {if isEventAvailable then "Found" else "Not found (expected)"}`)
			print("✅ Client error handling test completed!")
		end, function(error)
			print(`❌ Error handling test failed: {error}`)
		end)
	end
	local testPerformanceMonitoring = function()
		print("⚡ Testing Performance Monitoring...")
		local startTime = tick()
		-- Simulate some work
		for i = 0, 999 do
			math.random()
		end
		local endTime = tick()
		local duration = endTime - startTime
		print("📊 Performance Metrics:")
		print(`  ✅ Test duration: {string.format("%.4f", duration)}s`)
		print(`  ✅ Operations completed: 1000`)
		print(`  ✅ Ops/second: {string.format("%.0f", 1000 / duration)}`)
		-- Test memory usage (basic check)
		local memoryStats = game:GetService("Stats"):GetTotalMemoryUsageMb()
		print(`  📊 Memory usage: {string.format("%.2f", memoryStats)} MB`)
		print("✅ Performance monitoring test completed!")
	end
	local testRemoteEventStatus = function()
		print("📡 Testing RemoteEvent Status...")
		-- Check if RemoteEvents folder exists
		local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
		if not remoteEventsFolder then
			print("❌ RemoteEvents folder not found in ReplicatedStorage")
			print("🔍 Checking ReplicatedStorage contents:")
			local children = ReplicatedStorage:GetChildren()
			-- ▼ ReadonlyArray.forEach ▼
			local _callback = function(child)
				print(`  📁 {child.Name} ({child.ClassName})`)
			end
			for _k, _v in children do
				_callback(_v, _k - 1, children)
			end
			-- ▲ ReadonlyArray.forEach ▲
			return nil
		end
		print("✅ RemoteEvents folder found")
		print(`📊 RemoteEvents in folder: {#remoteEventsFolder:GetChildren()}`)
		-- List all RemoteEvents
		local remoteEvents = remoteEventsFolder:GetChildren()
		print("📡 All RemoteEvents:")
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(child)
			if child:IsA("RemoteEvent") then
				print(`  ✅ {child.Name}`)
			else
				print(`  ❓ {child.Name} ({child.ClassName})`)
			end
		end
		for _k, _v in remoteEvents do
			_callback(_v, _k - 1, remoteEvents)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Test specific events we need
		local testEvents = { "WhitebeardEffectReplicate", "WhitebeardVisualSync", "WorldTesting", "NetworkTest" }
		print("🔍 Checking specific events:")
		-- ▼ ReadonlyArray.forEach ▼
		local _callback_1 = function(eventName)
			local event = remoteEventsFolder:FindFirstChild(eventName)
			if event then
				print(`  ✅ {eventName}: Found`)
			else
				print(`  ❌ {eventName}: Missing`)
			end
		end
		for _k, _v in testEvents do
			_callback_1(_v, _k - 1, testEvents)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Check server initialization status
		print("🔍 Server Status Check:")
		print("  If events are missing, the server may not be fully initialized yet.")
		print("  Try waiting a few seconds and testing again.")
		print("✅ RemoteEvent status check completed!")
	end
	local testServerStatus = function()
		print("🖥️ Testing Server Status...")
		-- Check if we're in a server environment
		local runService = game:GetService("RunService")
		if runService:IsStudio() then
			print("🎮 Running in Roblox Studio")
			if runService:IsServer() then
				print("  ✅ Server context available")
			else
				print("  ❌ No server context (client-only)")
			end
		else
			print("🌐 Running in live game")
		end
		-- Check for server scripts
		local serverScriptService = game:GetService("ServerScriptService")
		local serverScripts = serverScriptService:GetChildren()
		print(`📊 Server scripts found: {#serverScripts}`)
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(serverScript)
			if serverScript:IsA("Script") or serverScript:IsA("ModuleScript") then
				print(`  📜 {serverScript.Name}`)
			end
		end
		for _k, _v in serverScripts do
			_callback(_v, _k - 1, serverScripts)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Check if Core server is initialized
		local coreServerIndicator = ReplicatedStorage:FindFirstChild("CoreServerInitialized")
		if coreServerIndicator then
			print("✅ Core server appears to be initialized")
		else
			print("❌ Core server may not be initialized yet")
			print("   Try waiting a few seconds for server startup")
		end
		print("✅ Server status check completed!")
	end
	local testQuakeAbilityNetworking
	local testAllSystems = function()
		print("🚀 ========================================")
		print("🚀 COMPREHENSIVE CORE FRAMEWORK TEST")
		print("🚀 ========================================")
		print("")
		-- Test 1: Server Status
		print("📋 TEST 1/10: Server Status")
		print("----------------------------------------")
		testServerStatus()
		task.wait(1)
		print("")
		-- Test 2: RemoteEvent Status
		print("📋 TEST 2/10: RemoteEvent Status")
		print("----------------------------------------")
		testRemoteEventStatus()
		task.wait(1)
		print("")
		-- Test 3: Core Framework
		print("📋 TEST 3/10: Core Framework")
		print("----------------------------------------")
		testCoreFramework()
		task.wait(1)
		print("")
		-- Test 4: Error Handling
		print("📋 TEST 4/10: Error Handling")
		print("----------------------------------------")
		testErrorHandling()
		task.wait(1)
		print("")
		-- Test 5: Networking & Validation
		print("📋 TEST 5/10: Networking & Validation")
		print("----------------------------------------")
		testNetworkService()
		task.wait(0.5)
		testValidationService()
		task.wait(0.5)
		testStateManagement()
		task.wait(1)
		print("")
		-- Test 6: QuakeAbility Networking
		print("📋 TEST 6/10: QuakeAbility Networking")
		print("----------------------------------------")
		testQuakeAbilityNetworking()
		task.wait(1)
		print("")
		-- Test 7: UI Components
		print("📋 TEST 7/10: UI Components")
		print("----------------------------------------")
		testResponsiveDesign()
		task.wait(0.5)
		testZIndexManager()
		task.wait(0.5)
		testUIComponents()
		task.wait(1)
		print("")
		-- Test 8: Effects System
		print("📋 TEST 8/10: Effects System")
		print("----------------------------------------")
		testBasicEffects()
		task.wait(2)
		testParticleEffects()
		task.wait(1)
		testVisualEffects()
		task.wait(2)
		print("")
		-- Test 9: Ability System
		print("📋 TEST 9/10: Ability System")
		print("----------------------------------------")
		testAbilityValidation()
		task.wait(0.5)
		testAbilityCooldowns()
		task.wait(0.5)
		testAbilityEffects()
		task.wait(3)
		print("")
		-- Test 10: Performance Monitoring
		print("📋 TEST 10/10: Performance Monitoring")
		print("----------------------------------------")
		testPerformanceMonitoring()
		task.wait(1)
		print("")
		-- Final Summary
		print("🎯 ========================================")
		print("🎯 COMPREHENSIVE TEST SUMMARY")
		print("🎯 ========================================")
		print("✅ All 10 test categories completed!")
		print("📊 Check the detailed logs above for any issues")
		print("💡 Key indicators to look for:")
		print("   - Server Status: Should show server context available")
		print("   - RemoteEvents: WhitebeardEffectReplicate should be found")
		print("   - Core Framework: Should be initialized")
		print("   - Effects: Should create visual effects without errors")
		print("   - Networking: Should not show 'Core Framework is not initialized'")
		print("")
		print("🚀 Comprehensive Core Framework test completed!")
		print("🚀 ========================================")
	end
	testQuakeAbilityNetworking = function()
		print("🥊 Testing QuakeAbility Networking...")
		-- Try to get the QuakeAbility instance (this is a bit hacky but for debugging)
		-- In a real implementation, you'd have a proper reference to the ability
		TS.try(function()
			-- We'll need to access the ability through the ClientAbilityManager
			-- For now, let's just test the RemoteEvent directly
			local eventName = Core.eventName("WhitebeardEffectReplicate")
			local visualSyncEvent = Core.eventName("WhitebeardVisualSync")
			print("📊 QuakeAbility Networking Status:")
			print(`  Event 1: {eventName}`)
			print(`  Event 2: {visualSyncEvent}`)
			-- Check if RemoteEvents exist
			local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
			if remoteEventsFolder then
				local event1 = remoteEventsFolder:FindFirstChild(eventName)
				local event2 = remoteEventsFolder:FindFirstChild(visualSyncEvent)
				print(`  ✅ {eventName}: {if event1 then "Found" else "Missing"}`)
				print(`  ✅ {visualSyncEvent}: {if event2 then "Found" else "Missing"}`)
			else
				print("  ❌ RemoteEvents folder not found")
			end
			print("✅ QuakeAbility networking test completed!")
		end, function(error)
			warn(`❌ QuakeAbility networking test failed: {error}`)
		end)
	end
	local forceReinitializeQuakeNetworking = function()
		print("🔧 Force reinitializing QuakeAbility networking...")
		TS.try(function()
			-- Access the global QuakeAbility instance through _G (Roblox global table)
			-- This is a temporary solution for testing
			local globalTable = _G
			if globalTable._quakeAbilityInstance then
				local success = globalTable._quakeAbilityInstance:forceInitializeNetworking()
				if success then
					print("✅ QuakeAbility networking reinitialized successfully!")
				else
					warn("❌ Failed to reinitialize QuakeAbility networking")
				end
			else
				warn("❌ QuakeAbility instance not found in global scope")
				print("💡 Tip: The QuakeAbility needs to be exposed globally for this to work")
				print("💡 Alternative: Try activating the ability - it will attempt to reinitialize automatically")
			end
		end, function(error)
			warn(`❌ Force reinitialize failed: {error}`)
		end)
	end
	return React.createElement(React.Fragment, nil, isPanelOpen and (React.createElement(ContainerFrame, {
		size = UDim2.new(0, 300, 1, 0),
		position = UDim2.new(1, -300, 0, 0),
		backgroundTransparency = 0,
		borderThickness = 0,
		zIndex = forceHighZIndex,
	}, React.createElement(ContainerFrame, {
		size = UDim2.new(1, 0, 0, 60),
		position = UDim2.new(0, 0, 0, 0),
		backgroundTransparency = 0,
		borderThickness = 0,
		zIndex = forceHighZIndex + 1,
	}, React.createElement(Label, {
		text = "🌍 World Testing Lab",
		fontSize = 16,
		bold = true,
		position = UDim2.new(0, 16, 0.5, 0),
		anchorPoint = Vector2.new(0, 0.5),
		size = UDim2.new(1, -60, 0, 20),
	}), React.createElement(IconButton, {
		icon = "✕",
		onClick = closePanel,
		size = UDim2.new(0, 32, 0, 32),
		position = UDim2.new(1, -16, 0.5, 0),
		anchorPoint = Vector2.new(1, 0.5),
	})), React.createElement(ScrollingFrame, {
		size = UDim2.new(1, 0, 1, -60),
		position = UDim2.new(0, 0, 0, 60),
		backgroundTransparency = 1,
		borderThickness = 0,
		scrollingDirection = Enum.ScrollingDirection.Y,
		automaticCanvasSize = Enum.AutomaticSize.Y,
		zIndex = forceHighZIndex + 1,
	}, React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 16,
	}, React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🚀 Comprehensive Testing",
		fontSize = 18,
		bold = true,
	}), React.createElement(Label, {
		text = "Run all Core framework tests in sequence with detailed console output for complete system diagnostics.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	}), React.createElement(Button, {
		text = "🚀 TEST ALL SYSTEMS",
		onClick = function()
			print("🎯 Starting comprehensive Core framework test...")
			task.spawn(function()
				TS.try(function()
					testAllSystems()
				end, function(err)
					warn(`Test All failed: {err}`)
				end)
			end)
		end,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🌍 Gravity Testing",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test different gravity levels to see how they affect the entire world.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🔮 Low Gravity (0.3x)",
		onClick = testLowGravity,
	}), React.createElement(Button, {
		text = "🌪️ High Gravity (2.5x)",
		onClick = testHighGravity,
	}), React.createElement(Button, {
		text = "🌀 Zero Gravity (0.05x)",
		onClick = testZeroGravity,
	}), React.createElement(Button, {
		text = "🌍 Normal Gravity (1.0x)",
		onClick = testNormalGravity,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🌦️ Weather Testing",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test different weather conditions and atmospheric effects.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "☀️ Clear Weather",
		onClick = testClearWeather,
	}), React.createElement(Button, {
		text = "🌧️ Light Rain",
		onClick = testRain,
	}), React.createElement(Button, {
		text = "🌧️ Heavy Rain",
		onClick = testHeavyRain,
	}), React.createElement(Button, {
		text = "❄️ Snow",
		onClick = testSnow,
	}), React.createElement(Button, {
		text = "🌨️ Blizzard",
		onClick = testBlizzard,
	}), React.createElement(Button, {
		text = "🌪️ Storm",
		onClick = testStorm,
	}), React.createElement(Button, {
		text = "⛈️ Thunderstorm",
		onClick = testThunderstorm,
	}), React.createElement(Button, {
		text = "🌫️ Fog",
		onClick = testFog,
	}), React.createElement(Button, {
		text = "🏜️ Sandstorm",
		onClick = testSandstorm,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🌅 Day/Night Cycle",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Control the time of day and lighting conditions.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🌅 Dawn",
		onClick = testDawn,
	}), React.createElement(Button, {
		text = "☀️ Noon",
		onClick = testNoon,
	}), React.createElement(Button, {
		text = "🌇 Dusk",
		onClick = testDusk,
	}), React.createElement(Button, {
		text = "🌙 Night",
		onClick = testNight,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🤖 Entity Management",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the new Entity Management system - spawn, track, and manage game entities.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🤖 Spawn NPC",
		onClick = spawnNPC,
	}), React.createElement(Button, {
		text = "🚀 Spawn Projectile",
		onClick = spawnProjectile,
	}), React.createElement(Button, {
		text = "✨ Spawn Effect",
		onClick = spawnEffect,
	}), React.createElement(Button, {
		text = "💎 Spawn Pickup",
		onClick = spawnPickup,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "📊 List All Entities",
		onClick = listEntities,
	}), React.createElement(Button, {
		text = "🔍 Find Nearby Entities",
		onClick = findNearbyEntities,
	}), React.createElement(Button, {
		text = "🧹 Cleanup All Entities",
		onClick = cleanupAllEntities,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "💾 Data Persistence",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the DataStore system - save/load player data, currency, inventory, and settings.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "📊 Load Player Data",
		onClick = testLoadPlayerData,
	}), React.createElement(Button, {
		text = "🎮 Update Game Data",
		onClick = testUpdateGameData,
	}), React.createElement(Button, {
		text = "⚙️ Update Settings",
		onClick = testUpdateSettings,
	}), React.createElement(Button, {
		text = "📈 Show DataStore Stats",
		onClick = testDataStoreStats,
	}), React.createElement(Button, {
		text = "🔧 Enable Studio DataStore",
		onClick = enableStudioDataStore,
	}), React.createElement(Button, {
		text = "🛑 Disable Auto-Save",
		onClick = disableAutoSave,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🧠 AI System",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the AI behavior system - spawn NPCs with different AI behaviors and watch them interact.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🤖 Spawn AI NPC",
		onClick = spawnAINPC,
	}), React.createElement(Button, {
		text = "🚶 Spawn Patrolling NPC",
		onClick = spawnPatrollingNPC,
	}), React.createElement(Button, {
		text = "😱 Spawn Fleeing NPC",
		onClick = spawnFleeingNPC,
	}), React.createElement(Button, {
		text = "🔍 Make NPC Investigate",
		onClick = makeNPCInvestigate,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "📊 Show AI Stats",
		onClick = showAIStats,
	}), React.createElement(Button, {
		text = "🔍 Debug System Demo",
		onClick = demonstrateDebugSystem,
	}), React.createElement(Button, {
		text = "🧹 Cleanup All AI",
		onClick = cleanupAllAI,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🎬 Animation System",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the animation system - create programmatic animations without animation assets.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🎬 Initialize Animation System",
		onClick = initializeAnimationSystem,
	}), React.createElement(Button, {
		text = "👋 Test Limb Animation",
		onClick = testLimbAnimation,
	}), React.createElement(Button, {
		text = "🦾 Test Preset Animations",
		onClick = testPresetAnimations,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "📊 Show Animation Stats",
		onClick = showAnimationStats,
	}), React.createElement(Button, {
		text = "🧹 Cleanup Animation System",
		onClick = cleanupAnimationSystem,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "✨ Effects System",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the comprehensive effects system - EffectPartBuilder, EffectTweenBuilder, particles, visual effects, and sound integration.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🔮 Test Basic Effects",
		onClick = testBasicEffects,
	}), React.createElement(Button, {
		text = "🎆 Test Particle Effects",
		onClick = testParticleEffects,
	}), React.createElement(Button, {
		text = "🌟 Test Visual Effects",
		onClick = testVisualEffects,
	}), React.createElement(Button, {
		text = "🔊 Test Sound Effects",
		onClick = testSoundEffects,
	}), React.createElement(Button, {
		text = "🎭 Test Complex Effect Combo",
		onClick = testComplexEffectCombo,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "⚔️ Ability System",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the ability system including validation, cooldowns, and ability-specific effects for One Piece inspired abilities.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🔍 Test Ability Validation",
		onClick = testAbilityValidation,
	}), React.createElement(Button, {
		text = "⏰ Test Ability Cooldowns",
		onClick = testAbilityCooldowns,
	}), React.createElement(Button, {
		text = "💥 Test Ability Effects",
		onClick = testAbilityEffects,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🎨 UI Components",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the Core GUI framework including responsive design, ZIndex management, modals, and component systems.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "📱 Test Responsive Design",
		onClick = testResponsiveDesign,
	}), React.createElement(Button, {
		text = "🔢 Test ZIndex Manager",
		onClick = testZIndexManager,
	}), React.createElement(Button, {
		text = "🪟 Test Modal System",
		onClick = testModalSystem,
	}), React.createElement(Button, {
		text = "🎨 Test UI Components",
		onClick = testUIComponents,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🌐 Networking & Validation",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test the Core framework's networking, validation, and state management systems for robust client-server communication.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🌐 Test Network Service",
		onClick = testNetworkService,
	}), React.createElement(Button, {
		text = "🔍 Test Validation Service",
		onClick = testValidationService,
	}), React.createElement(Button, {
		text = "📊 Test State Management",
		onClick = testStateManagement,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "🚀 Advanced Systems",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = "Test advanced Core framework features including framework initialization, error handling, performance monitoring, and system diagnostics.",
		fontSize = 12,
		textWrapped = true,
		size = UDim2.new(1, 0, 0, 0),
		autoSize = true,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Button, {
		text = "🚀 Test Core Framework",
		onClick = testCoreFramework,
	}), React.createElement(Button, {
		text = "⚠️ Test Error Handling",
		onClick = testErrorHandling,
	}), React.createElement(Button, {
		text = "⚡ Test Performance Monitoring",
		onClick = testPerformanceMonitoring,
	}), React.createElement(Button, {
		text = "📡 Test RemoteEvent Status",
		onClick = testRemoteEventStatus,
	}), React.createElement(Button, {
		text = "🥊 Test QuakeAbility Networking",
		onClick = testQuakeAbilityNetworking,
	}), React.createElement(Button, {
		text = "🔧 Force Reinit Quake Networking",
		onClick = forceReinitializeQuakeNetworking,
	}), React.createElement(Button, {
		text = "🖥️ Test Server Status",
		onClick = testServerStatus,
	})))))), testModalOpen and (React.createElement(Modal, {
		title = "🧪 UI Component Test Modal",
		isOpen = testModalOpen,
		onClose = function()
			return setTestModalOpen(false)
		end,
	}, React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 20,
	}, React.createElement(Label, {
		text = "This is a test modal to verify the Modal component functionality in the Core framework.",
		fontSize = 14,
		textWrapped = true,
	}), React.createElement(Button, {
		text = "✅ Modal Works!",
		onClick = function()
			print("✅ Modal button clicked!")
			setTestModalOpen(false)
		end,
	}), React.createElement(Button, {
		text = "❌ Close Modal",
		onClick = function()
			return setTestModalOpen(false)
		end,
	})))))
end
return {
	WorldTestingPanel = WorldTestingPanel,
}
