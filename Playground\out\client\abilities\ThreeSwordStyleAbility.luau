-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent, "AbilityBase").AbilityBase
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local RunService = _services.RunService
local Workspace = _services.Workspace
local Players = _services.Players
local Lighting = _services.Lighting
local ThreeSwordStyleAbility
do
	local super = AbilityBase
	ThreeSwordStyleAbility = setmetatable({}, {
		__tostring = function()
			return "ThreeSwordStyleAbility"
		end,
		__index = super,
	})
	ThreeSwordStyleAbility.__index = ThreeSwordStyleAbility
	function ThreeSwordStyleAbility.new(...)
		local self = setmetatable({}, ThreeSwordStyleAbility)
		return self:constructor(...) or self
	end
	function ThreeSwordStyleAbility:constructor()
		super.constructor(self, "THREE_SWORD_STYLE", 10)
		self.swordEffects = {}
		self.cooldownEndTime = 0
		self.isActive = false
	end
	function ThreeSwordStyleAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function ThreeSwordStyleAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function ThreeSwordStyleAbility:activate()
		if self:isOnCooldown() or self.isActive then
			return nil
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		print("⚔️ Activating Zoro's Three Sword Style!")
		self.isActive = true
		-- Phase 1: Character sword preparation animation
		self:createThreeSwordStyleAnimation(character)
		-- Phase 2: Create sword aura
		self:createSwordAura(character)
		-- Phase 2: Create three swords
		self:createThreeSwords(character)
		-- Phase 3: Execute triple slash sequence
		task.delay(1, function()
			self:executeTripleSlash(humanoidRootPart)
		end)
		-- Phase 4: Create air blade attacks
		task.delay(1.5, function()
			self:createAirBlades(humanoidRootPart)
		end)
		-- Phase 5: Environmental sword effects
		self:createEnvironmentalSwordEffects()
		-- Duration: 4 seconds
		task.delay(4, function()
			self:cleanupEffects()
			self.isActive = false
		end)
		self:startCooldown()
	end
	function ThreeSwordStyleAbility:createThreeSwordStyleAnimation(character)
		print("⚔️ Creating Zoro's Three Sword Style animation")
		local humanoid = character:FindFirstChild("Humanoid")
		if not humanoid then
			return nil
		end
		-- Create proper Motor6D animation like QuakeAbility
		self:createCharacterAnimation(character)
	end
	function ThreeSwordStyleAbility:createCharacterAnimation(character)
		local torso = character:FindFirstChild("Torso")
		local upperTorso = character:FindFirstChild("UpperTorso")
		if torso then
			-- R6 character
			self:animateR6Character(torso)
		elseif upperTorso then
			-- R15 character
			self:animateR15Character(upperTorso)
		end
	end
	function ThreeSwordStyleAbility:animateR6Character(torso)
		local rightShoulder = torso:FindFirstChild("Right Shoulder")
		local leftShoulder = torso:FindFirstChild("Left Shoulder")
		local rightHip = torso:FindFirstChild("Right Hip")
		local leftHip = torso:FindFirstChild("Left Hip")
		local neck = torso:FindFirstChild("Neck")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		local originalRightHipC0 = if rightHip then rightHip.C0 else nil
		local originalLeftHipC0 = if leftHip then leftHip.C0 else nil
		local originalNeckC0 = if neck then neck.C0 else nil
		-- Phase 1: Sword drawing stance (0-0.5s) - Arms crossed
		print("⚔️ Sword drawing stance")
		local _exp = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(-math.pi / 4, 0, -math.pi / 2)
		_object[_left] = originalRightC0 * _arg0
		local rightArmDraw = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(-math.pi / 4, 0, math.pi / 2)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmDraw = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmDraw:Play()
		leftArmDraw:Play()
		-- Phase 2: Three sword ready position (0.5-1s) - Classic Santoryu pose
		task.delay(0.5, function()
			print("🗡️ Three Sword ready position")
			local _exp_2 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 6, 0, -math.pi / 3)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmReady = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 6, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmReady = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmReady:Play()
			leftArmReady:Play()
			-- Head slightly down for mouth sword
			if neck and originalNeckC0 then
				local _exp_4 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
				local _object_4 = {}
				local _left_4 = "C0"
				local _arg0_4 = CFrame.Angles(math.pi / 8, 0, 0)
				_object_4[_left_4] = originalNeckC0 * _arg0_4
				local neckTilt = TweenService:Create(neck, _exp_4, _object_4)
				neckTilt:Play()
			end
		end)
		-- Phase 3: Slash preparation (1-1.5s) - Arms back for power
		task.delay(1, function()
			print("⚔️ Slash preparation")
			local _exp_2 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, -math.pi / 2)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmPrep = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 2, 0, math.pi / 2)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmPrep = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmPrep:Play()
			leftArmPrep:Play()
		end)
		-- Phase 4: Triple slash execution (1.5-2.5s) - Sequential slashing
		task.delay(1.5, function()
			print("💥 Triple slash execution!")
			-- First slash - Right diagonal
			local _exp_2 = TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(math.pi / 4, 0, -math.pi / 4)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightSlash1 = TweenService:Create(rightShoulder, _exp_2, _object_2)
			rightSlash1:Play()
			-- Second slash - Left diagonal (0.2s delay)
			task.delay(0.2, function()
				local _exp_3 = TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
				local _object_3 = {}
				local _left_3 = "C0"
				local _arg0_3 = CFrame.Angles(math.pi / 4, 0, math.pi / 4)
				_object_3[_left_3] = originalLeftC0 * _arg0_3
				local leftSlash = TweenService:Create(leftShoulder, _exp_3, _object_3)
				leftSlash:Play()
			end)
			-- Third slash - Both arms horizontal (0.4s delay)
			task.delay(0.4, function()
				local _exp_3 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
				local _object_3 = {}
				local _left_3 = "C0"
				local _arg0_3 = CFrame.Angles(0, 0, -math.pi / 2)
				_object_3[_left_3] = originalRightC0 * _arg0_3
				local rightSlash2 = TweenService:Create(rightShoulder, _exp_3, _object_3)
				local _exp_4 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
				local _object_4 = {}
				local _left_4 = "C0"
				local _arg0_4 = CFrame.Angles(0, 0, math.pi / 2)
				_object_4[_left_4] = originalLeftC0 * _arg0_4
				local leftSlash2 = TweenService:Create(leftShoulder, _exp_4, _object_4)
				rightSlash2:Play()
				leftSlash2:Play()
			end)
		end)
		-- Phase 5: Final stance (2.5-3.5s) - Dramatic finish pose
		task.delay(2.5, function()
			print("⚔️ Final sword stance")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 3, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmFinal = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 3, 0, math.pi / 6)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmFinal = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmFinal:Play()
			leftArmFinal:Play()
			-- Wide stance for dramatic finish
			if rightHip and leftHip and originalRightHipC0 and originalLeftHipC0 then
				local _exp_4 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
				local _object_4 = {}
				local _left_4 = "C0"
				local _arg0_4 = CFrame.Angles(0, 0, math.pi / 12)
				_object_4[_left_4] = originalRightHipC0 * _arg0_4
				local rightLegStance = TweenService:Create(rightHip, _exp_4, _object_4)
				local _exp_5 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
				local _object_5 = {}
				local _left_5 = "C0"
				local _arg0_5 = CFrame.Angles(0, 0, -math.pi / 12)
				_object_5[_left_5] = originalLeftHipC0 * _arg0_5
				local leftLegStance = TweenService:Create(leftHip, _exp_5, _object_5)
				rightLegStance:Play()
				leftLegStance:Play()
			end
		end)
		-- Phase 6: Return to normal (3.5-4s)
		task.delay(3.5, function()
			print("⚔️ Returning to normal stance")
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
			-- Restore legs and neck
			if rightHip and originalRightHipC0 then
				local rightLegReturn = TweenService:Create(rightHip, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalRightHipC0,
				})
				rightLegReturn:Play()
			end
			if leftHip and originalLeftHipC0 then
				local leftLegReturn = TweenService:Create(leftHip, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalLeftHipC0,
				})
				leftLegReturn:Play()
			end
			if neck and originalNeckC0 then
				local neckReturn = TweenService:Create(neck, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalNeckC0,
				})
				neckReturn:Play()
			end
		end)
	end
	function ThreeSwordStyleAbility:animateR15Character(upperTorso)
		local rightShoulder = upperTorso:FindFirstChild("RightShoulder")
		local leftShoulder = upperTorso:FindFirstChild("LeftShoulder")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		-- Similar animation sequence for R15 (simplified)
		print("⚔️ R15 Three Sword Style animation")
		-- Phase 1: Drawing
		local _exp = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(-math.pi / 4, 0, -math.pi / 2)
		_object[_left] = originalRightC0 * _arg0
		local rightArmDraw = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(-math.pi / 4, 0, math.pi / 2)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmDraw = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmDraw:Play()
		leftArmDraw:Play()
		-- Continue with similar phases...
		task.delay(0.5, function()
			local _exp_2 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 6, 0, -math.pi / 3)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmReady = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 6, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmReady = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmReady:Play()
			leftArmReady:Play()
		end)
		-- Return to normal (3.5s delay)
		task.delay(3.5, function()
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
		end)
	end
	function ThreeSwordStyleAbility:createSwordAura(character)
		print("⚔️ Creating Zoro's sword aura")
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Create sword aura around character
		local swordAura = Instance.new("Part")
		swordAura.Name = "SwordAura"
		swordAura.Shape = Enum.PartType.Ball
		swordAura.Size = Vector3.new(8, 8, 8)
		swordAura.Color = Color3.fromRGB(200, 200, 255)
		swordAura.Material = Enum.Material.ForceField
		swordAura.Transparency = 0.7
		swordAura.CanCollide = false
		swordAura.Anchored = true
		swordAura.Position = humanoidRootPart.Position
		swordAura.Parent = Workspace
		local _exp = self.swordEffects
		table.insert(_exp, swordAura)
		-- Add sharp metallic light
		local auraLight = Instance.new("PointLight")
		auraLight.Color = Color3.fromRGB(200, 220, 255)
		auraLight.Brightness = 4
		auraLight.Range = 20
		auraLight.Parent = swordAura
		-- Pulsing aura effect
		local pulseTween = TweenService:Create(swordAura, TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Size = Vector3.new(10, 10, 10),
			Transparency = 0.5,
		})
		pulseTween:Play()
		-- Follow character
		local followConnection = RunService.Heartbeat:Connect(function()
			if humanoidRootPart.Parent and swordAura.Parent then
				swordAura.Position = humanoidRootPart.Position
			end
		end)
		-- Cleanup after duration
		task.delay(4, function()
			followConnection:Disconnect()
			pulseTween:Cancel()
			local fadeTween = TweenService:Create(swordAura, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				swordAura:Destroy()
				local index = (table.find(self.swordEffects, swordAura) or 0) - 1
				if index > -1 then
					table.remove(self.swordEffects, index + 1)
				end
			end)
		end)
	end
	function ThreeSwordStyleAbility:createThreeSwords(character)
		print("🗡️ Creating Zoro's three swords")
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Create three swords around the character
		local swordPositions = { Vector3.new(2, 1, 0), Vector3.new(-2, 1, 0), Vector3.new(0, 2, 0) }
		local swordColors = { Color3.fromRGB(255, 255, 255), Color3.fromRGB(220, 220, 220), Color3.fromRGB(200, 200, 200) }
		for i = 0, 2 do
			local sword = Instance.new("Part")
			sword.Name = `ZoroSword{i + 1}`
			sword.Shape = Enum.PartType.Block
			sword.Size = Vector3.new(0.3, 6, 0.8)
			sword.Color = swordColors[i + 1]
			sword.Material = Enum.Material.Metal
			sword.Transparency = 0.1
			sword.CanCollide = false
			sword.Anchored = true
			local _position = humanoidRootPart.Position
			local _arg0 = swordPositions[i + 1]
			local swordPosition = _position + _arg0
			sword.Position = swordPosition
			-- Rotate sword to look like a blade
			local _cFrame = sword.CFrame
			local _arg0_1 = CFrame.Angles(0, 0, math.pi / 6)
			sword.CFrame = _cFrame * _arg0_1
			sword.Parent = Workspace
			local _exp = self.swordEffects
			table.insert(_exp, sword)
			-- Add metallic shine
			local swordLight = Instance.new("PointLight")
			swordLight.Color = Color3.fromRGB(255, 255, 255)
			swordLight.Brightness = 2
			swordLight.Range = 8
			swordLight.Parent = sword
			-- Floating animation
			local _exp_1 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
			local _object = {}
			local _left = "Position"
			local _vector3 = Vector3.new(0, 0.5, 0)
			_object[_left] = swordPosition + _vector3
			local floatTween = TweenService:Create(sword, _exp_1, _object)
			floatTween:Play()
			-- Spinning animation
			local _exp_2 = TweenInfo.new(2, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1)
			local _object_1 = {}
			local _left_1 = "CFrame"
			local _cFrame_1 = sword.CFrame
			local _arg0_2 = CFrame.Angles(0, math.pi * 2, 0)
			_object_1[_left_1] = _cFrame_1 * _arg0_2
			local spinTween = TweenService:Create(sword, _exp_2, _object_1)
			spinTween:Play()
			-- Create sword trail effect
			self:createSwordTrail(sword)
		end
	end
	function ThreeSwordStyleAbility:createSwordTrail(sword)
		-- Create attachment for trail
		local attachment1 = Instance.new("Attachment")
		attachment1.Name = "TrailAttachment1"
		attachment1.Position = Vector3.new(0, 3, 0)
		attachment1.Parent = sword
		local attachment2 = Instance.new("Attachment")
		attachment2.Name = "TrailAttachment2"
		attachment2.Position = Vector3.new(0, -3, 0)
		attachment2.Parent = sword
		-- Create trail
		local trail = Instance.new("Trail")
		trail.Name = "SwordTrail"
		trail.Attachment0 = attachment1
		trail.Attachment1 = attachment2
		trail.Color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 255)), ColorSequenceKeypoint.new(0.5, Color3.fromRGB(200, 220, 255)), ColorSequenceKeypoint.new(1, Color3.fromRGB(100, 150, 255)) })
		trail.Transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.2), NumberSequenceKeypoint.new(1, 1) })
		trail.Lifetime = 0.5
		trail.MinLength = 0
		trail.Parent = sword
	end
	function ThreeSwordStyleAbility:executeTripleSlash(humanoidRootPart)
		print("💥 Executing triple slash sequence!")
		local direction = humanoidRootPart.CFrame.LookVector
		-- Create three slash effects in sequence
		for i = 0, 2 do
			task.delay(i * 0.2, function()
				self:createSlashEffect(humanoidRootPart.Position, direction, i)
			end)
		end
	end
	function ThreeSwordStyleAbility:createSlashEffect(position, direction, slashIndex)
		-- Create slash visual effect
		local slash = Instance.new("Part")
		slash.Name = `SlashEffect{slashIndex + 1}`
		slash.Shape = Enum.PartType.Block
		slash.Size = Vector3.new(0.2, 8, 12)
		slash.Color = Color3.fromRGB(255, 255, 255)
		slash.Material = Enum.Material.Neon
		slash.Transparency = 0.3
		slash.CanCollide = false
		slash.Anchored = true
		-- Position slash in front of character
		local _position = position
		local _direction = direction
		local _arg0 = 5 + slashIndex * 3
		local slashPosition = _position + (_direction * _arg0)
		slash.Position = slashPosition
		-- Rotate slash based on index for different angles
		local rotationAngles = { CFrame.Angles(0, 0, math.pi / 4), CFrame.Angles(0, 0, -math.pi / 4), CFrame.Angles(0, 0, 0) }
		local _direction_1 = direction
		local _exp = CFrame.lookAt(slashPosition, slashPosition + _direction_1)
		local _arg0_1 = rotationAngles[slashIndex + 1]
		slash.CFrame = _exp * _arg0_1
		slash.Parent = Workspace
		local _exp_1 = self.swordEffects
		table.insert(_exp_1, slash)
		-- Add slash light
		local slashLight = Instance.new("PointLight")
		slashLight.Color = Color3.fromRGB(255, 255, 255)
		slashLight.Brightness = 6
		slashLight.Range = 15
		slashLight.Parent = slash
		-- Slash animation - expand and fade
		local slashTween = TweenService:Create(slash, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Size = Vector3.new(0.2, 12, 18),
			Transparency = 1,
		})
		slashTween:Play()
		-- Create slash impact effect
		self:createSlashImpact(slashPosition)
		-- Remove slash after animation
		slashTween.Completed:Connect(function()
			slash:Destroy()
			local index = (table.find(self.swordEffects, slash) or 0) - 1
			if index > -1 then
				table.remove(self.swordEffects, index + 1)
			end
		end)
	end
	function ThreeSwordStyleAbility:createSlashImpact(position)
		-- Create impact sparkles
		for i = 0, 7 do
			local sparkle = Instance.new("Part")
			sparkle.Name = "SlashSparkle"
			sparkle.Shape = Enum.PartType.Block
			sparkle.Size = Vector3.new(0.2, 0.2, 0.2)
			sparkle.Color = Color3.fromRGB(255, 255, 255)
			sparkle.Material = Enum.Material.Neon
			sparkle.Transparency = 0.2
			sparkle.CanCollide = false
			sparkle.Anchored = true
			local _position = position
			local _vector3 = Vector3.new(math.random(-3, 3), math.random(-3, 3), math.random(-3, 3))
			sparkle.Position = _position + _vector3
			sparkle.Parent = Workspace
			local _exp = self.swordEffects
			table.insert(_exp, sparkle)
			-- Sparkle animation
			local sparkleTween = TweenService:Create(sparkle, TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Size = Vector3.new(0.05, 0.05, 0.05),
				Transparency = 1,
			})
			sparkleTween:Play()
			sparkleTween.Completed:Connect(function()
				sparkle:Destroy()
				local index = (table.find(self.swordEffects, sparkle) or 0) - 1
				if index > -1 then
					table.remove(self.swordEffects, index + 1)
				end
			end)
		end
	end
	function ThreeSwordStyleAbility:createAirBlades(humanoidRootPart)
		print("🌪️ Creating air blade attacks!")
		local direction = humanoidRootPart.CFrame.LookVector
		-- Create multiple air blades
		for i = 0, 4 do
			task.delay(i * 0.1, function()
				self:launchAirBlade(humanoidRootPart.Position, direction, i)
			end)
		end
	end
	function ThreeSwordStyleAbility:launchAirBlade(startPosition, direction, bladeIndex)
		-- Create air blade projectile
		local airBlade = Instance.new("Part")
		airBlade.Name = `AirBlade{bladeIndex + 1}`
		airBlade.Shape = Enum.PartType.Block
		airBlade.Size = Vector3.new(0.3, 4, 8)
		airBlade.Color = Color3.fromRGB(200, 220, 255)
		airBlade.Material = Enum.Material.ForceField
		airBlade.Transparency = 0.4
		airBlade.CanCollide = false
		airBlade.Anchored = true
		-- Slight angle variation for each blade
		local angleVariation = (bladeIndex - 2) * 0.2
		local _exp = CFrame.fromAxisAngle(Vector3.new(0, 1, 0), angleVariation)
		local _vector3 = Vector3.new(direction.X, direction.Y, direction.Z)
		local variedDirection = _exp * _vector3
		local _startPosition = startPosition
		local _arg0 = direction * 3
		airBlade.Position = _startPosition + _arg0
		airBlade.CFrame = CFrame.lookAt(airBlade.Position, airBlade.Position + variedDirection)
		airBlade.Parent = Workspace
		local _exp_1 = self.swordEffects
		table.insert(_exp_1, airBlade)
		-- Add air blade light
		local bladeLight = Instance.new("PointLight")
		bladeLight.Color = Color3.fromRGB(200, 220, 255)
		bladeLight.Brightness = 3
		bladeLight.Range = 12
		bladeLight.Parent = airBlade
		-- Create air blade trail
		self:createAirBladeTrail(airBlade)
		-- Move air blade
		local speed = 60
		local maxDistance = 100
		local travelDistance = 0
		local moveConnection
		moveConnection = RunService.Heartbeat:Connect(function(deltaTime)
			if not airBlade.Parent then
				moveConnection:Disconnect()
				return nil
			end
			local moveDistance = speed * deltaTime
			travelDistance += moveDistance
			if travelDistance >= maxDistance then
				-- Create impact effect at max range
				self:createAirBladeImpact(airBlade.Position)
				moveConnection:Disconnect()
				airBlade:Destroy()
				return nil
			end
			-- Move air blade
			local _position = airBlade.Position
			local _arg0_1 = variedDirection * moveDistance
			airBlade.Position = _position + _arg0_1
			-- Check for collision
			self:checkAirBladeCollision(airBlade, variedDirection, moveConnection)
		end)
		-- Safety cleanup
		task.delay(2, function()
			if moveConnection.Connected then
				moveConnection:Disconnect()
			end
			if airBlade.Parent then
				self:createAirBladeImpact(airBlade.Position)
				airBlade:Destroy()
			end
		end)
	end
	function ThreeSwordStyleAbility:createAirBladeTrail(airBlade)
		-- Create trail attachments
		local attachment1 = Instance.new("Attachment")
		attachment1.Name = "BladeTrailAttachment1"
		attachment1.Position = Vector3.new(0, 0, 4)
		attachment1.Parent = airBlade
		local attachment2 = Instance.new("Attachment")
		attachment2.Name = "BladeTrailAttachment2"
		attachment2.Position = Vector3.new(0, 0, -4)
		attachment2.Parent = airBlade
		-- Create air trail
		local trail = Instance.new("Trail")
		trail.Name = "AirBladeTrail"
		trail.Attachment0 = attachment1
		trail.Attachment1 = attachment2
		trail.Color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 255)), ColorSequenceKeypoint.new(0.5, Color3.fromRGB(200, 220, 255)), ColorSequenceKeypoint.new(1, Color3.fromRGB(150, 180, 255)) })
		trail.Transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.3), NumberSequenceKeypoint.new(1, 1) })
		trail.Lifetime = 0.8
		trail.MinLength = 0
		trail.Parent = airBlade
	end
	function ThreeSwordStyleAbility:checkAirBladeCollision(airBlade, direction, moveConnection)
		-- Simple collision detection
		local raycast = Workspace:Raycast(airBlade.Position, direction * 3, RaycastParams.new())
		if raycast and raycast.Instance then
			local hitPart = raycast.Instance
			-- Skip if it's our own sword effects or player characters
			if (string.find(hitPart.Name, "Sword")) ~= nil or (string.find(hitPart.Name, "Slash")) ~= nil or (string.find(hitPart.Name, "Air")) ~= nil or Players:GetPlayerFromCharacter(hitPart.Parent) ~= nil then
				return nil
			end
			-- Create impact effect
			self:createAirBladeImpact(raycast.Position)
			-- Remove air blade
			moveConnection:Disconnect()
			airBlade:Destroy()
			local _swordEffects = self.swordEffects
			local _airBlade = airBlade
			local index = (table.find(_swordEffects, _airBlade) or 0) - 1
			if index > -1 then
				table.remove(self.swordEffects, index + 1)
			end
		end
	end
	function ThreeSwordStyleAbility:createAirBladeImpact(position)
		print("💥 Air blade impact!")
		-- Create impact explosion
		local impact = Instance.new("Part")
		impact.Name = "AirBladeImpact"
		impact.Shape = Enum.PartType.Ball
		impact.Size = Vector3.new(3, 3, 3)
		impact.Color = Color3.fromRGB(255, 255, 255)
		impact.Material = Enum.Material.Neon
		impact.Transparency = 0.3
		impact.CanCollide = false
		impact.Anchored = true
		impact.Position = position
		impact.Parent = Workspace
		local _exp = self.swordEffects
		table.insert(_exp, impact)
		-- Impact expansion
		local impactTween = TweenService:Create(impact, TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Size = Vector3.new(12, 12, 12),
			Transparency = 1,
		})
		impactTween:Play()
		-- Create impact sparkles
		self:createSlashImpact(position)
		-- Remove impact after animation
		impactTween.Completed:Connect(function()
			impact:Destroy()
			local index = (table.find(self.swordEffects, impact) or 0) - 1
			if index > -1 then
				table.remove(self.swordEffects, index + 1)
			end
		end)
	end
	function ThreeSwordStyleAbility:createEnvironmentalSwordEffects()
		print("⚔️ Creating environmental sword effects")
		-- Create sword atmosphere
		self:createSwordAtmosphere()
		-- Create floating sword energy
		self:createFloatingSwordEnergy()
		-- Create ground sword marks
		self:createGroundSwordMarks()
	end
	function ThreeSwordStyleAbility:createSwordAtmosphere()
		-- Change lighting to sharp metallic atmosphere
		local originalAmbient = Lighting.Ambient
		local originalOutdoorAmbient = Lighting.OutdoorAmbient
		local originalBrightness = Lighting.Brightness
		local lightingTween = TweenService:Create(Lighting, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Ambient = Color3.fromRGB(150, 160, 200),
			OutdoorAmbient = Color3.fromRGB(170, 180, 220),
			Brightness = 1.8,
		})
		lightingTween:Play()
		-- Restore lighting after duration
		task.delay(3, function()
			local restoreLightingTween = TweenService:Create(Lighting, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Ambient = originalAmbient,
				OutdoorAmbient = originalOutdoorAmbient,
				Brightness = originalBrightness,
			})
			restoreLightingTween:Play()
		end)
	end
	function ThreeSwordStyleAbility:createFloatingSwordEnergy()
		-- Create floating sword energy orbs
		for i = 0, 7 do
			task.delay(i * 0.2, function()
				local randomPosition = Vector3.new(math.random(-30, 30), math.random(5, 20), math.random(-30, 30))
				local swordEnergy = Instance.new("Part")
				swordEnergy.Name = "FloatingSwordEnergy"
				swordEnergy.Shape = Enum.PartType.Block
				swordEnergy.Size = Vector3.new(0.5, 3, 0.5)
				swordEnergy.Color = Color3.fromRGB(200, 220, 255)
				swordEnergy.Material = Enum.Material.Neon
				swordEnergy.Transparency = 0.4
				swordEnergy.CanCollide = false
				swordEnergy.Anchored = true
				swordEnergy.Position = randomPosition
				swordEnergy.Parent = Workspace
				local _exp = self.swordEffects
				table.insert(_exp, swordEnergy)
				-- Add energy light
				local energyLight = Instance.new("PointLight")
				energyLight.Color = Color3.fromRGB(200, 220, 255)
				energyLight.Brightness = 2
				energyLight.Range = 10
				energyLight.Parent = swordEnergy
				-- Floating and spinning animation
				local _exp_1 = TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
				local _object = {}
				local _left = "Position"
				local _vector3 = Vector3.new(0, 3, 0)
				_object[_left] = randomPosition + _vector3
				local floatTween = TweenService:Create(swordEnergy, _exp_1, _object)
				floatTween:Play()
				local _exp_2 = TweenInfo.new(1.5, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1)
				local _object_1 = {}
				local _left_1 = "CFrame"
				local _cFrame = swordEnergy.CFrame
				local _arg0 = CFrame.Angles(0, math.pi * 2, 0)
				_object_1[_left_1] = _cFrame * _arg0
				local spinTween = TweenService:Create(swordEnergy, _exp_2, _object_1)
				spinTween:Play()
			end)
		end
	end
	function ThreeSwordStyleAbility:createGroundSwordMarks()
		-- Create sword slash marks on the ground
		for i = 0, 5 do
			task.delay(i * 0.3, function()
				local randomPosition = Vector3.new(math.random(-25, 25), 0.1, math.random(-25, 25))
				local swordMark = Instance.new("Part")
				swordMark.Name = "GroundSwordMark"
				swordMark.Shape = Enum.PartType.Block
				swordMark.Size = Vector3.new(math.random(8, 15), 0.2, 1)
				swordMark.Color = Color3.fromRGB(255, 255, 255)
				swordMark.Material = Enum.Material.Neon
				swordMark.Transparency = 0.5
				swordMark.CanCollide = false
				swordMark.Anchored = true
				swordMark.Position = randomPosition
				local _cFrame = swordMark.CFrame
				local _arg0 = CFrame.Angles(0, math.random() * math.pi * 2, 0)
				swordMark.CFrame = _cFrame * _arg0
				swordMark.Parent = Workspace
				local _exp = self.swordEffects
				table.insert(_exp, swordMark)
				-- Fading animation
				local fadeTween = TweenService:Create(swordMark, TweenInfo.new(3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					Transparency = 1,
				})
				fadeTween:Play()
				fadeTween.Completed:Connect(function()
					swordMark:Destroy()
					local index = (table.find(self.swordEffects, swordMark) or 0) - 1
					if index > -1 then
						table.remove(self.swordEffects, index + 1)
					end
				end)
			end)
		end
	end
	function ThreeSwordStyleAbility:cleanupEffects()
		print("🧹 Cleaning up Three Sword Style effects")
		-- Clean up all sword effects
		for _, effect in self.swordEffects do
			if effect.Parent then
				-- Fade out effect
				local fadeTween = TweenService:Create(effect, TweenInfo.new(1, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeTween:Play()
				fadeTween.Completed:Connect(function()
					return effect:Destroy()
				end)
			end
		end
		self.swordEffects = {}
		-- Disconnect any remaining connections
		if self.swordConnection then
			self.swordConnection:Disconnect()
			self.swordConnection = nil
		end
	end
end
return {
	ThreeSwordStyleAbility = ThreeSwordStyleAbility,
}
