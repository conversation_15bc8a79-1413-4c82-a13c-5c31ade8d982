-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Workspace = _services.Workspace
local RunService = _services.RunService
local WeatherSoundSystem
do
	WeatherSoundSystem = setmetatable({}, {
		__tostring = function()
			return "WeatherSoundSystem"
		end,
	})
	WeatherSoundSystem.__index = WeatherSoundSystem
	function WeatherSoundSystem.new(...)
		local self = setmetatable({}, WeatherSoundSystem)
		return self:constructor(...) or self
	end
	function WeatherSoundSystem:constructor()
	end
	function WeatherSoundSystem:playWeatherAmbient(weatherType, intensity)
		print(`🔊 Starting {weatherType} ambient sound at intensity {intensity}`)
		local soundKey = self:getWeatherSoundKey(weatherType, intensity)
		local config = self.WEATHER_SOUNDS[soundKey]
		if not config then
			print(`⚠️ No sound configuration for weather: {weatherType}`)
			return nil
		end
		-- Stop existing ambient sound for this weather type
		self:stopWeatherAmbient(weatherType)
		-- Create and configure the sound
		local sound = Instance.new("Sound")
		sound.Name = `{weatherType}_ambient`
		sound.SoundId = config.soundId
		sound.Volume = 0
		sound.PlaybackSpeed = math.random(config.pitchRange[1], config.pitchRange[2])
		sound.Looped = config.looping
		sound.Parent = Workspace
		-- Configure 3D sound properties (using modern Roblox audio system)
		if config.is3D then
			sound.RollOffMode = config.rollOffMode
			-- Note: Spatial audio properties are now handled by SoundGroups in modern Roblox
			-- The sound will use default spatial audio behavior
		end
		-- Calculate target volume based on intensity
		local targetVolume = math.clamp(config.baseVolume * intensity, 0, 1)
		-- Play the sound
		local playSuccess = { pcall(function()
			return sound:Play()
		end) }
		if not playSuccess[1] then
			print(`⚠️ Failed to play {weatherType} sound`)
			sound:Destroy()
			return nil
		end
		-- Fade in the sound
		if config.fadeInDuration > 0 then
			self:fadeSound(sound, 0, targetVolume, config.fadeInDuration)
		else
			sound.Volume = targetVolume
		end
		-- Store the ambient sound
		local _ambientSounds = self.ambientSounds
		local _weatherType = weatherType
		_ambientSounds[_weatherType] = sound
		print(`✅ Playing {weatherType} ambient sound at {targetVolume} volume`)
	end
	function WeatherSoundSystem:fadeSound(sound, fromVolume, toVolume, duration, onComplete)
		local soundId = sound.Name
		-- Clear any existing fade for this sound
		local existingConnection = self.fadeConnections[soundId]
		if existingConnection then
			existingConnection:Disconnect()
		end
		local startTime = tick()
		local volumeDiff = toVolume - fromVolume
		local fadeConnection = RunService.Heartbeat:Connect(function()
			if not sound.Parent then
				self.fadeConnections[soundId] = nil
				return nil
			end
			local elapsed = tick() - startTime
			local progress = math.min(elapsed / duration, 1)
			-- Use smooth easing for better audio experience
			local easedProgress = progress * progress * (3 - 2 * progress)
			sound.Volume = fromVolume + volumeDiff * easedProgress
			if progress >= 1 then
				self.fadeConnections[soundId] = nil
				if onComplete then
					onComplete()
				end
			end
		end)
		self.fadeConnections[soundId] = fadeConnection
	end
	function WeatherSoundSystem:stopWeatherAmbient(weatherType)
		local _ambientSounds = self.ambientSounds
		local _weatherType = weatherType
		local sound = _ambientSounds[_weatherType]
		if not sound or not sound.Parent then
			return nil
		end
		local soundKey = self:getWeatherSoundKey(weatherType, 1)
		local config = self.WEATHER_SOUNDS[soundKey]
		local _result = config
		if _result ~= nil then
			_result = _result.fadeOutDuration
		end
		local _condition = _result
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 2
		end
		local fadeOutDuration = _condition
		print(`🔇 Stopping {weatherType} ambient sound`)
		-- Fade out and destroy
		self:fadeSound(sound, sound.Volume, 0, fadeOutDuration, function()
			if sound.Parent then
				sound:Destroy()
			end
			local _ambientSounds_1 = self.ambientSounds
			local _weatherType_1 = weatherType
			_ambientSounds_1[_weatherType_1] = nil
		end)
	end
	function WeatherSoundSystem:getWeatherSoundKey(weatherType, intensity)
		repeat
			if weatherType == "rain" then
				if intensity < 0.3 then
					return "rain_light"
				end
				if intensity < 0.7 then
					return "rain_medium"
				end
				return "rain_heavy"
			end
			if weatherType == "heavy_rain" then
				return "rain_heavy"
			end
			if weatherType == "storm" then
				return if intensity > 0.6 then "rain_heavy" else "rain_medium"
			end
			if weatherType == "thunderstorm" then
				return "rain_heavy"
			end
			if weatherType == "snow" then
				return "snow_light"
			end
			if weatherType == "blizzard" then
				return "blizzard"
			end
			if weatherType == "wind" then
				if intensity < 0.4 then
					return "wind_light"
				end
				if intensity < 0.7 then
					return "wind_medium"
				end
				return "wind_strong"
			end
			if weatherType == "thunder" then
				return if intensity > 0.7 then "thunder_close" else "thunder_distant"
			end
			return "rain_light"
		until true
	end
	function WeatherSoundSystem:playLayeredWeatherAmbient(weatherType, intensity)
		print(`🔊 Starting layered {weatherType} ambient sounds at intensity {intensity}`)
		-- Stop existing sounds first
		self:stopWeatherAmbient(weatherType)
		-- Play primary weather sound
		self:playWeatherAmbient(weatherType, intensity)
		-- Add atmospheric layers based on weather type
		repeat
			local _fallthrough = false
			if weatherType == "rain" then
				_fallthrough = true
			end
			if _fallthrough or weatherType == "heavy_rain" then
				-- Add wind layer for rain
				if intensity > 0.4 then
					self:playWeatherAmbient("wind", intensity * 0.6)
				end
				break
			end
			if weatherType == "storm" then
				_fallthrough = true
			end
			if _fallthrough or weatherType == "thunderstorm" then
				-- Add wind and occasional thunder
				self:playWeatherAmbient("wind", intensity * 0.8)
				if weatherType == "thunderstorm" then
					-- Schedule random thunder sounds
					self:scheduleThunderSounds(intensity)
				end
				break
			end
			if weatherType == "blizzard" then
				-- Add strong wind for blizzard
				self:playWeatherAmbient("wind", intensity * 0.9)
				break
			end
		until true
		print(`✅ Layered {weatherType} ambient sounds started`)
	end
	function WeatherSoundSystem:scheduleThunderSounds(intensity)
		local thunderInterval = math.max(5, 20 - intensity * 15)
		local playThunder
		playThunder = function()
			if self.ambientSounds.thunderstorm ~= nil then
				self:playWeatherAmbient("thunder", intensity)
				-- Schedule next thunder
				task.wait(thunderInterval + math.random() * 10)
				playThunder()
			end
		end
		-- Start thunder sequence
		task.spawn(function()
			task.wait(math.random() * 5)
			playThunder()
		end)
	end
	function WeatherSoundSystem:stopAllWeatherSounds()
		print("🔇 Stopping all weather sounds")
		-- Stop all ambient sounds
		local _exp = self.ambientSounds
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(_, weatherType)
			self:stopWeatherAmbient(weatherType)
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		-- Clear fade connections
		local _exp_1 = self.fadeConnections
		-- ▼ ReadonlyMap.forEach ▼
		local _callback_1 = function(connection)
			if connection.Connected then
				connection:Disconnect()
			end
		end
		for _k, _v in _exp_1 do
			_callback_1(_v, _k, _exp_1)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.fadeConnections)
		-- Clear any remaining active sounds
		local _exp_2 = self.activeSounds
		-- ▼ ReadonlyMap.forEach ▼
		local _callback_2 = function(sounds)
			-- ▼ ReadonlyArray.forEach ▼
			local _callback_3 = function(sound)
				if sound.Parent then
					sound:Stop()
					sound:Destroy()
				end
			end
			for _k, _v in sounds do
				_callback_3(_v, _k - 1, sounds)
			end
			-- ▲ ReadonlyArray.forEach ▲
		end
		for _k, _v in _exp_2 do
			_callback_2(_v, _k, _exp_2)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.activeSounds)
		print("✅ All weather sounds stopped")
	end
	WeatherSoundSystem.activeSounds = {}
	WeatherSoundSystem.ambientSounds = {}
	WeatherSoundSystem.fadeConnections = {}
	WeatherSoundSystem.WEATHER_SOUNDS = {
		rain_light = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.4,
			pitchRange = { 0.95, 1.05 },
			looping = true,
			fadeInDuration = 4,
			fadeOutDuration = 5,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 400,
			minDistance = 10,
		},
		rain_medium = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.6,
			pitchRange = { 0.9, 1.1 },
			looping = true,
			fadeInDuration = 3,
			fadeOutDuration = 4,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 500,
			minDistance = 10,
		},
		rain_heavy = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.8,
			pitchRange = { 0.8, 1.0 },
			looping = true,
			fadeInDuration = 2,
			fadeOutDuration = 3,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 600,
			minDistance = 10,
		},
		thunder_distant = {
			soundId = "rbxassetid://138143457",
			baseVolume = 0.5,
			pitchRange = { 0.6, 0.8 },
			looping = false,
			fadeInDuration = 0,
			fadeOutDuration = 2,
			is3D = true,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 3000,
			minDistance = 200,
		},
		thunder_close = {
			soundId = "rbxassetid://138143457",
			baseVolume = 0.9,
			pitchRange = { 0.9, 1.3 },
			looping = false,
			fadeInDuration = 0,
			fadeOutDuration = 1,
			is3D = true,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 2000,
			minDistance = 100,
		},
		wind_light = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.25,
			pitchRange = { 0.7, 0.9 },
			looping = true,
			fadeInDuration = 6,
			fadeOutDuration = 8,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 300,
			minDistance = 10,
		},
		wind_medium = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.4,
			pitchRange = { 0.6, 0.8 },
			looping = true,
			fadeInDuration = 4,
			fadeOutDuration = 6,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 400,
			minDistance = 10,
		},
		wind_strong = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.7,
			pitchRange = { 0.4, 0.6 },
			looping = true,
			fadeInDuration = 2,
			fadeOutDuration = 3,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 500,
			minDistance = 10,
		},
		snow_light = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.2,
			pitchRange = { 0.8, 1.0 },
			looping = true,
			fadeInDuration = 8,
			fadeOutDuration = 10,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 200,
			minDistance = 10,
		},
		blizzard = {
			soundId = "rbxassetid://131961136",
			baseVolume = 0.8,
			pitchRange = { 0.3, 0.5 },
			looping = true,
			fadeInDuration = 3,
			fadeOutDuration = 4,
			is3D = false,
			rollOffMode = Enum.RollOffMode.Linear,
			maxDistance = 600,
			minDistance = 10,
		},
	}
end
-- Legacy alias for backward compatibility
local WeatherSoundHelper = WeatherSoundSystem
return {
	WeatherSoundSystem = WeatherSoundSystem,
	WeatherSoundHelper = WeatherSoundHelper,
}
