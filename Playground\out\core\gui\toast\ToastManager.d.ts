import * as React from "@rbxts/react";
import { ToastData } from "./Toast";
interface ToastManagerProps {
}
interface ToastManagerInstance {
    addToast: (toast: Omit<ToastData, "id">) => void;
}
export declare class ToastService {
    private static instance?;
    static setInstance(instance: ToastManagerInstance): void;
    static showToast(toast: Omit<ToastData, "id">): void;
    static showSuccess(title: string, message?: string, duration?: number): void;
    static showError(title: string, message?: string, duration?: number): void;
    static showWarning(title: string, message?: string, duration?: number): void;
    static showInfo(title: string, message?: string, duration?: number): void;
}
export declare function ToastManager(props: ToastManagerProps): React.ReactElement;
export {};
