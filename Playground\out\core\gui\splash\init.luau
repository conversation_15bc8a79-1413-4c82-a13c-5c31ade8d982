-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
exports.SplashScreen = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "splash", "SplashScreen").SplashScreen
exports.SplashScreenManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "splash", "SplashScreenManager").SplashScreenManager
exports.useSplashScreen = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "splash", "useSplashScreen").useSplashScreen
return exports
