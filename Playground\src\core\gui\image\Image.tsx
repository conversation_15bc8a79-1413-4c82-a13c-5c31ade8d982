import * as React from "@rbxts/react";
import { BORDER_RADIUS } from "../../design";

interface ImageProps {
	image: string;
	size?: UDim2;
	position?: UDim2;
	anchorPoint?: Vector2;
	layoutOrder?: number;
	imageColor?: Color3;
	imageTransparency?: number;
	scaleType?: Enum.ScaleType;
	backgroundColor?: Color3;
	backgroundTransparency?: number;
	cornerRadius?: number;
}

export function Image(props: ImageProps): React.ReactElement {
	const size = props.size ?? new UDim2(0, 24, 0, 24);
	const imageTransparency = props.imageTransparency ?? 0;
	const backgroundTransparency = props.backgroundTransparency ?? 1;
	const cornerRadius = props.cornerRadius ?? BORDER_RADIUS.sm;

	return (
		<imagelabel
			Image={props.image}
			Size={size}
			Position={props.position}
			AnchorPoint={props.anchorPoint}
			LayoutOrder={props.layoutOrder}
			ImageColor3={props.imageColor}
			ImageTransparency={imageTransparency}
			ScaleType={props.scaleType ?? Enum.ScaleType.Fit}
			BackgroundColor3={props.backgroundColor}
			BackgroundTransparency={backgroundTransparency}
			BorderSizePixel={0}
		>
			<uicorner CornerRadius={new UDim(0, cornerRadius)} />
		</imagelabel>
	);
}
