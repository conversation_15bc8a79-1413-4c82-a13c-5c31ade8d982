import { TweenService, Workspace } from "@rbxts/services";
import { findBodyJoints as coreJointFinder, restoreBodyJoints as coreRestoreJoints } from "../../../../core";

export function findBodyJoints(character: Model): Map<string, Motor6D | undefined> {
	// Use Core framework CharacterJointManager - exact same behavior
	return coreJointFinder(character);
}

export function animateFullBodyPunch(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>): void {
	// Get character for movement
	const character = rightShoulder.Parent?.Parent as Model;
	const humanoidRootPart = character?.FindFirstChild("HumanoidRootPart") as Part;

	// PHASE 1: Wind-up (0.2s) - Pull back for power
	createWindUpAnimation(rightShoulder, bodyJoints, humanoidRootPart);

	// PHASE 2: Explosive punch (0.3s) - After wind-up
	task.delay(0.2, () => {
		createExplosivePunch(rightShoulder, bodyJoints, humanoidRootPart);
	});

	print("✅ Epic punch animation sequence initiated");
}

export function createWindUpAnimation(
	rightShoulder: Motor6D,
	bodyJoints: Map<string, Motor6D | undefined>,
	humanoidRootPart?: Part,
): void {
	const originalC0 = rightShoulder.C0;

	// Wind-up: Pull right arm back dramatically
	const windUpTween = TweenService.Create(
		rightShoulder,
		new TweenInfo(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
		{ C0: originalC0.mul(CFrame.Angles(math.rad(-30), math.rad(-20), math.rad(-10))) }, // Pull back
	);
	windUpTween.Play();

	// Left arm forward for balance during wind-up
	const leftShoulder = bodyJoints.get("LeftShoulder");
	if (leftShoulder) {
		const leftOriginal = leftShoulder.C0;
		const leftWindUp = TweenService.Create(
			leftShoulder,
			new TweenInfo(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
			{ C0: leftOriginal.mul(CFrame.Angles(math.rad(20), math.rad(15), math.rad(10))) },
		);
		leftWindUp.Play();
	}

	// Torso twist back for wind-up
	const waist = bodyJoints.get("Waist") || bodyJoints.get("RootJoint");
	if (waist) {
		const waistOriginal = waist.C0;
		const waistWindUp = TweenService.Create(
			waist,
			new TweenInfo(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
			{ C0: waistOriginal.mul(CFrame.Angles(math.rad(10), math.rad(-25), 0)) }, // Twist back
		);
		waistWindUp.Play();
	}

	// Character step back slightly
	if (humanoidRootPart) {
		const originalCFrame = humanoidRootPart.CFrame;
		const backStep = originalCFrame.LookVector.mul(-1); // Step back 1 stud

		const stepBackTween = TweenService.Create(
			humanoidRootPart,
			new TweenInfo(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
			{ CFrame: originalCFrame.add(backStep) },
		);
		stepBackTween.Play();
	}

	print("✅ Wind-up animation started");
}

export function createExplosivePunch(
	rightShoulder: Motor6D,
	bodyJoints: Map<string, Motor6D | undefined>,
	humanoidRootPart?: Part,
): void {
	const originalC0 = rightShoulder.C0;

	// EXPLOSIVE punch forward with overshoot
	const punchTween = TweenService.Create(
		rightShoulder,
		new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{ C0: originalC0.mul(CFrame.Angles(math.rad(130), math.rad(15), math.rad(5))) }, // Massive extension
	);
	punchTween.Play();

	// Left arm swing back dramatically
	const leftShoulder = bodyJoints.get("LeftShoulder");
	if (leftShoulder) {
		const leftOriginal = leftShoulder.C0;
		const leftSwing = TweenService.Create(
			leftShoulder,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ C0: leftOriginal.mul(CFrame.Angles(math.rad(-80), math.rad(-50), math.rad(-30))) }, // Extreme pullback
		);
		leftSwing.Play();
	}

	// Explosive torso rotation and lean
	const waist = bodyJoints.get("Waist") || bodyJoints.get("RootJoint");
	if (waist) {
		const waistOriginal = waist.C0;
		const explosiveTorso = TweenService.Create(
			waist,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ C0: waistOriginal.mul(CFrame.Angles(math.rad(-25), math.rad(50), 0)) }, // Extreme lean and twist
		);
		explosiveTorso.Play();
	}

	// Head snap forward with punch
	const neck = bodyJoints.get("Neck");
	if (neck) {
		const neckOriginal = neck.C0;
		const headSnap = TweenService.Create(
			neck,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ C0: neckOriginal.mul(CFrame.Angles(math.rad(-30), math.rad(25), 0)) }, // Dramatic head follow
		);
		headSnap.Play();
	}

	// Explosive forward lunge
	if (humanoidRootPart) {
		const originalCFrame = humanoidRootPart.CFrame;
		const explosiveLunge = originalCFrame.LookVector.mul(5); // 5 studs forward!

		const lungeTween = TweenService.Create(
			humanoidRootPart,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ CFrame: originalCFrame.add(explosiveLunge) },
		);
		lungeTween.Play();

		// Don't return to original position - let player move freely after punch
	}

	print("✅ EXPLOSIVE punch animation executed!");
}

export function animateCrossPunch(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>): void {
	// Get character for movement
	const character = rightShoulder.Parent?.Parent as Model;
	const humanoidRootPart = character?.FindFirstChild("HumanoidRootPart") as Part;
	const leftShoulder = bodyJoints.get("LeftShoulder");

	if (!leftShoulder) {
		print("❌ Left shoulder not found for cross punch, falling back to single punch");
		animateFullBodyPunch(rightShoulder, bodyJoints);
		return;
	}

	// PHASE 1: Cross arms (0.3s) - Whitebeard style
	createCrossArmsWindUp(rightShoulder, leftShoulder, bodyJoints, humanoidRootPart);

	// PHASE 2: Explosive double punch (0.4s) - After cross
	task.delay(0.3, () => {
		createExplosiveDoublePunch(rightShoulder, leftShoulder, bodyJoints, humanoidRootPart);
	});

	print("✅ One Piece cross punch animation sequence initiated");
}

export function createCrossArmsWindUp(
	rightShoulder: Motor6D,
	leftShoulder: Motor6D,
	bodyJoints: Map<string, Motor6D | undefined>,
	humanoidRootPart?: Part,
): void {
	const rightOriginal = rightShoulder.C0;
	const leftOriginal = leftShoulder.C0;

	// Cross arms horizontally in front - Whitebeard style
	const rightCrossTween = TweenService.Create(
		rightShoulder,
		new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
		{ C0: rightOriginal.mul(CFrame.Angles(math.rad(0), math.rad(-45), math.rad(-45))) }, // Cross right arm to left horizontally
	);

	const leftCrossTween = TweenService.Create(
		leftShoulder,
		new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
		{ C0: leftOriginal.mul(CFrame.Angles(math.rad(0), math.rad(45), math.rad(45))) }, // Cross left arm to right horizontally
	);

	rightCrossTween.Play();
	leftCrossTween.Play();

	// Torso preparation
	const waist = bodyJoints.get("Waist") || bodyJoints.get("RootJoint");
	if (waist) {
		const waistOriginal = waist.C0;
		const waistPrep = TweenService.Create(
			waist,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
			{ C0: waistOriginal.mul(CFrame.Angles(math.rad(5), 0, 0)) }, // Slight lean back
		);
		waistPrep.Play();
	}

	// Head preparation
	const neck = bodyJoints.get("Neck");
	if (neck) {
		const neckOriginal = neck.C0;
		const headPrep = TweenService.Create(
			neck,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
			{ C0: neckOriginal.mul(CFrame.Angles(math.rad(-10), 0, 0)) }, // Look down slightly
		);
		headPrep.Play();
	}

	print("✅ Cross arms wind-up started");
}

export function createExplosiveDoublePunch(
	rightShoulder: Motor6D,
	leftShoulder: Motor6D,
	bodyJoints: Map<string, Motor6D | undefined>,
	humanoidRootPart?: Part,
): void {
	const rightOriginal = rightShoulder.C0;
	const leftOriginal = leftShoulder.C0;

	// LEFT punch first (0.3s) - Horizontal punch to the left
	const leftPunchTween = TweenService.Create(
		leftShoulder,
		new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{ C0: leftOriginal.mul(CFrame.Angles(math.rad(0), math.rad(-90), math.rad(-90))) }, // Horizontal left punch
	);
	leftPunchTween.Play();

	// RIGHT punch with delay (0.5s delay, then 0.3s animation)
	task.delay(0.5, () => {
		const rightPunchTween = TweenService.Create(
			rightShoulder,
			new TweenInfo(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ C0: rightOriginal.mul(CFrame.Angles(math.rad(0), math.rad(90), math.rad(90))) }, // Horizontal right punch
		);
		rightPunchTween.Play();
	});

	// Play double punch sounds with proper timing
	const character = rightShoulder.Parent?.Parent as Model;
	if (character) {
		// Left punch sound (immediate)
		const leftPunchSound = new Instance("Sound");
		leftPunchSound.SoundId = "rbxassetid://84539241826189";
		leftPunchSound.Volume = 0.8;
		leftPunchSound.PlaybackSpeed = 0.9; // Slightly lower pitch for left
		leftPunchSound.Parent = character.FindFirstChild("HumanoidRootPart") || Workspace;
		leftPunchSound.Play();

		// Right punch sound with 0.5s delay to match animation
		task.delay(0.5, () => {
			const rightPunchSound = new Instance("Sound");
			rightPunchSound.SoundId = "rbxassetid://84539241826189";
			rightPunchSound.Volume = 0.8;
			rightPunchSound.PlaybackSpeed = 1.1; // Slightly higher pitch for right
			rightPunchSound.Parent = character.FindFirstChild("HumanoidRootPart") || Workspace;
			rightPunchSound.Play();

			// Clean up sounds
			leftPunchSound.Ended.Connect(() => leftPunchSound.Destroy());
			rightPunchSound.Ended.Connect(() => rightPunchSound.Destroy());
		});
	}

	// Explosive torso movement
	const waist = bodyJoints.get("Waist") || bodyJoints.get("RootJoint");
	if (waist) {
		const waistOriginal = waist.C0;
		const explosiveTorso = TweenService.Create(
			waist,
			new TweenInfo(0.4, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ C0: waistOriginal.mul(CFrame.Angles(math.rad(-20), 0, 0)) }, // Lean forward dramatically
		);
		explosiveTorso.Play();
	}

	// Head follow the punches
	const neck = bodyJoints.get("Neck");
	if (neck) {
		const neckOriginal = neck.C0;
		const headFollow = TweenService.Create(
			neck,
			new TweenInfo(0.4, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ C0: neckOriginal.mul(CFrame.Angles(math.rad(-25), 0, 0)) }, // Look forward intensely
		);
		headFollow.Play();
	}

	// Forward lunge
	if (humanoidRootPart) {
		const originalCFrame = humanoidRootPart.CFrame;
		const doubleLunge = originalCFrame.LookVector.mul(4); // 4 studs forward

		const lungeTween = TweenService.Create(
			humanoidRootPart,
			new TweenInfo(0.4, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{ CFrame: originalCFrame.add(doubleLunge) },
		);
		lungeTween.Play();

		// Don't return to original position - let player move freely after cross punch
	}

	print("✅ EXPLOSIVE double cross punch executed!");
}

export function restoreBodyJoints(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>): void {
	// Use Core framework restoreBodyJoints - exact same behavior
	coreRestoreJoints(bodyJoints);

	print("✅ All body joints restored");
}
