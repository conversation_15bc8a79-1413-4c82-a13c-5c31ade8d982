export interface DebugLine {
    from: Vector3;
    to: Vector3;
    color: Color3;
    duration: number;
    createdAt: number;
}
export interface DebugSphere {
    position: Vector3;
    radius: number;
    color: Color3;
    duration: number;
    createdAt: number;
}
export interface DebugText {
    position: Vector3;
    text: string;
    color: Color3;
    duration: number;
    createdAt: number;
}
