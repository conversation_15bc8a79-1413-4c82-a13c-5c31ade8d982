export interface WeatherConfig {
    emissionRate: number;
    lifetime: NumberRange;
    speed: NumberRange;
    acceleration: Vector3;
    size: NumberSequence;
    transparency: NumberSequence;
    color: ColorSequence;
    texture: string;
    shape: Enum.ParticleEmitterShape;
    shapeInOut: Enum.ParticleEmitterShapeInOut;
    shapeStyle: Enum.ParticleEmitterShapeStyle;
    windAffectsDrag: boolean;
    drag: number;
    lightEmission: number;
    lightInfluence: number;
    orientation: Enum.ParticleOrientation;
    rate: number;
    rotSpeed: NumberRange;
    rotation: NumberRange;
    spreadAngle: Vector2;
    velocityInheritance: number;
    zOffset: number;
}
export declare class WeatherSystem {
    private static activeEmitters;
    private static weatherParts;
    private static rainConnections;
    /**
     * Create REALISTIC rain using proper Roblox particle techniques
     */
    static createRealisticRain(intensity: number, windSpeed?: number): ParticleEmitter[];
    /**
     * Create ground impact effects for rain
     */
    private static startRainGroundEffects;
    /**
     * Create REALISTIC snow particle effects using proper techniques
     */
    static createSnowEffect(intensity: number, windSpeed?: number): ParticleEmitter[];
    /**
     * Create storm effects with heavy rain and wind
     */
    static createStormEffect(intensity: number): ParticleEmitter[];
    /**
     * Create thunderstorm effects with lightning and heavy rain
     */
    static createThunderstormEffect(intensity: number): ParticleEmitter[];
    /**
     * Create fog/mist effects
     */
    static createFogEffect(intensity: number): ParticleEmitter[];
    /**
     * Create sandstorm effects
     */
    static createSandstormEffect(intensity: number): ParticleEmitter[];
    /**
     * Apply particle configuration to a ParticleEmitter
     */
    private static applyParticleConfig;
    /**
     * Clear specific weather effects
     */
    static clearWeatherEffect(weatherType: string): void;
    /**
     * Clear all active weather effects
     */
    static clearAllWeatherEffects(): void;
    /**
     * Update weather intensity for active effects
     */
    static updateWeatherIntensity(weatherType: string, newIntensity: number): void;
    /**
     * Get active weather types
     */
    static getActiveWeatherTypes(): string[];
    /**
     * Check if specific weather type is active
     */
    static isWeatherActive(weatherType: string): boolean;
    /**
     * Get number of active emitters for a weather type
     */
    static getEmitterCount(weatherType: string): number;
    /**
     * Create weather effect based on type and intensity
     */
    static createWeatherEffect(weatherType: string, intensity: number, windSpeed?: number): ParticleEmitter[];
}
export declare const WeatherParticleHelper: typeof WeatherSystem;
