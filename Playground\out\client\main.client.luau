-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local createRoot = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react-roblox").createRoot
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local BottomLeftGrid = TS.import(script, script.Parent, "gui", "BottomLeftGrid").BottomLeftGrid
local ActionBarDemo = TS.import(script, script.Parent, "gui", "ActionBarDemo").ActionBarDemo
local MovementExample = TS.import(script, script.Parent, "movement", "MovementExample").MovementExample
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local initializeDebugSystem = _core.initializeDebugSystem
local initializeClientCore = _core.initializeClientCore
local SplashScreen = _core.SplashScreen
local useSplashScreen = _core.useSplashScreen
local ToastManager = _core.ToastManager
local ToastService = _core.ToastService
local version = "v1.3.5"
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
-- Create a ScreenGui with properties to make it visible
local screenGui = Instance.new("ScreenGui", playerGui)
screenGui.ResetOnSpawn = false
screenGui.IgnoreGuiInset = true
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
screenGui.DisplayOrder = 100
screenGui.Name = "MainReactGUI"
print(`🎮 [{tick()}] Main React ScreenGui created with DisplayOrder: {screenGui.DisplayOrder}`)
-- Main App Component with Splash Screen
local function MainApp()
	local _binding = useSplashScreen()
	local state = _binding.state
	local startLoading = _binding.startLoading
	local hide = _binding.hide
	local setupDefaultTasks = _binding.setupDefaultTasks
	local manager = _binding.manager
	local hasStarted, setHasStarted = React.useState(false)
	-- Setup and start loading when component mounts
	React.useEffect(function()
		if not hasStarted then
			-- Setup default core framework loading tasks
			setupDefaultTasks()
			-- Add custom game loading tasks
			manager:addLoadingTask({
				name = "Loading Game Systems...",
				weight = 2,
				task = TS.async(function()
					-- Initialize debug system for development
					initializeDebugSystem()
					-- Initialize client core
					local clientCoreResult = TS.await(initializeClientCore())
					if clientCoreResult:isError() then
						warn(`Failed to initialize client core: {clientCoreResult:getError().message}`)
						error(clientCoreResult:getError().message)
					end
				end),
			})
			manager:addLoadingTask({
				name = "Initializing Movement System...",
				weight = 1,
				task = function()
					-- Initialize movement example for testing
					MovementExample.new()
				end,
			})
			manager:addLoadingTask({
				name = "Finalizing Client Setup...",
				weight = 1,
				task = TS.async(function()
					print(`🔥 Playground client loaded! [{version}]`)
					print(`🎙️ Voice system available! Use voiceDemo methods in console`)
					-- Show success toast when loading is complete
					task.delay(1, function()
						ToastService:showSuccess("Welcome to Playground!", `Client v{version} loaded successfully`)
					end)
				end),
			})
			-- Start the loading process
			startLoading()
			setHasStarted(true)
		end
	end, { hasStarted, setupDefaultTasks, manager, startLoading })
	local handleLoadingComplete = React.useCallback(function()
		-- Called when loading is complete and splash screen should hide
		hide()
		print("🎉 Splash screen loading completed!")
	end, { hide })
	return React.createElement(React.Fragment, nil, React.createElement(SplashScreen, {
		isVisible = state.isVisible,
		loadingProgress = state.loadingProgress,
		loadingText = state.loadingText,
		onLoadingComplete = handleLoadingComplete,
	}), React.createElement(ToastManager), not state.isVisible and (React.createElement(React.Fragment, nil, React.createElement(BottomLeftGrid, {
		onTestClick = function()
			print("Test button clicked!")
			ToastService:showInfo("Test Button", "You clicked the test button!")
		end,
		onHelloClick = function()
			print("Hello button clicked!")
			ToastService:showSuccess("Hello!", "Hello button was clicked!")
		end,
	}), React.createElement(ActionBarDemo))))
end
local root = createRoot(screenGui)
-- Render the main app with splash screen
root:render(React.createElement(MainApp))
-- All initialization is now handled by the splash screen system
