import { Effect<PERSON><PERSON><PERSON><PERSON>er, FrameAnimationHelper } from "../../../../core";
import { createKneeBendStance, createArmCrossingPose } from "../animations/WhitebeardPoses";

import type { QuakeAbility } from "../QuakeAbility";

export function createQuakeSphere(ability: QuakeAbility, character: Model): void {
	const rightHand = character.FindFirstChild("RightHand") as Part;
	if (!rightHand) return;

	// Create white quake sphere using Core framework - exact same properties
	ability.quakeEffect = EffectPartBuilder.create()
		.shape(Enum.PartType.Ball)
		.size(new Vector3(0.3, 0.3, 0.3)) // Start smaller like <PERSON><PERSON>ard's bubble
		.color(Color3.fromRGB(255, 255, 255)) // Pure white like <PERSON>beard's quake bubble
		.material(Enum.Material.ForceField) // ForceField for bubble-like appearance
		.transparency(0.3) // Semi-transparent bubble effect
		.withLight(8, 2, Color3.fromRGB(255, 255, 255)) // Subtle white glow
		.spawn();

	ability.quakeEffect.Name = "QuakeSphere";

	// Sphere expansion animation using Core framework - exact same behavior
	ability.quakeConnection = FrameAnimationHelper.createConnection((elapsed: number) => {
		if (ability.quakeEffect && rightHand.IsDescendantOf(game.Workspace)) {
			const handPosition = rightHand.Position;

			// Expand from 0.3 to 2.2 over 2 seconds with easing - exact same formula
			const progress = math.min(elapsed / 2, 1);
			const easeProgress = 1 - math.pow(1 - progress, 3); // Ease out cubic
			const size = 0.3 + (2.2 - 0.3) * easeProgress; // Max size 2.2 instead of 3

			// No pulsing - steady growth - exact same as original
			ability.quakeEffect.Size = new Vector3(size, size, size);
			ability.quakeEffect.CFrame = new CFrame(handPosition);

			// Increase intensity as it grows - exact same formula as original
			const light = ability.quakeEffect.FindFirstChild("PointLight") as PointLight;
			if (light) {
				light.Brightness = 2 + progress * 4; // Grows brighter as it expands like Whitebeard's bubble
			}
		}
	});
}

export function createSequentialQuakeSpheres(ability: QuakeAbility, character: Model): void {
	const rightHand = character.FindFirstChild("RightHand") as Part;
	const leftHand = character.FindFirstChild("LeftHand") as Part;
	if (!rightHand || !leftHand) return;

	// ENHANCED CROSS PUNCH ANIMATION SEQUENCE
	print("🥊 Starting enhanced cross punch animation with knee bend and arm crossing");

	// Phase 1: Knee bend and stance preparation (0.3s)
	createKneeBendStance(character);

	// Phase 2: Cross arms and create spheres (0.5s delay)
	task.delay(0.3, () => {
		createArmCrossingPose(character);

		// Create LEFT hand sphere using Core framework - exact same properties
		ability.leftQuakeEffect = EffectPartBuilder.create()
			.shape(Enum.PartType.Ball)
			.size(new Vector3(0.2, 0.2, 0.2)) // Start very small like Whitebeard's bubble
			.color(Color3.fromRGB(255, 255, 255)) // Pure white like Whitebeard's quake bubble
			.material(Enum.Material.ForceField) // ForceField for bubble-like appearance
			.transparency(0.4) // More transparent initially
			.withLight(6, 1, Color3.fromRGB(255, 255, 255)) // Subtle white glow
			.spawn();

		ability.leftQuakeEffect.Name = "LeftQuakeSphere";
	});

	// Phase 3: Create RIGHT hand sphere during arm crossing (0.8s delay for right punch timing)
	task.delay(0.8, () => {
		if (rightHand.IsDescendantOf(game.Workspace)) {
			print("🥊 Creating right hand Whitebeard sphere");

			// Create RIGHT hand sphere using Core framework - exact same properties
			ability.quakeEffect = EffectPartBuilder.create()
				.shape(Enum.PartType.Ball)
				.size(new Vector3(0.2, 0.2, 0.2)) // Start very small like Whitebeard's bubble
				.color(Color3.fromRGB(255, 255, 255)) // Pure white like Whitebeard's quake bubble
				.material(Enum.Material.ForceField) // ForceField for bubble-like appearance
				.transparency(0.4) // More transparent initially
				.withLight(6, 1, Color3.fromRGB(255, 255, 255)) // Subtle white glow
				.spawn();

			ability.quakeEffect.Name = "RightQuakeSphere";
		}
	});

	// Sequential sphere expansion animation using Core framework - exact same behavior
	ability.quakeConnection = FrameAnimationHelper.createConnection((elapsed: number) => {
		if (leftHand.IsDescendantOf(game.Workspace)) {
			const leftHandPosition = leftHand.Position;

			// Expand left sphere first - exact same formula as original
			if (ability.leftQuakeEffect) {
				const progress = math.min(elapsed / 2, 1);
				const easeProgress = 1 - math.pow(1 - progress, 3);
				const size = 0.2 + (3.5 - 0.2) * easeProgress; // Larger like Whitebeard's bubble

				ability.leftQuakeEffect.Size = new Vector3(size, size, size);
				ability.leftQuakeEffect.CFrame = new CFrame(leftHandPosition);

				// Increase left sphere intensity - exact same formula as original
				const leftLight = ability.leftQuakeEffect.FindFirstChild("PointLight") as PointLight;
				if (leftLight) {
					leftLight.Brightness = 2 + progress * 4; // Grows brighter as it expands like Whitebeard's bubble
				}
			}

			// Expand right sphere after 0.5s delay - exact same logic as original
			if (ability.quakeEffect && elapsed > 0.5 && rightHand.IsDescendantOf(game.Workspace)) {
				const rightHandPosition = rightHand.Position;
				const rightProgress = math.min((elapsed - 0.5) / 2, 1);
				const rightEaseProgress = 1 - math.pow(1 - rightProgress, 3);
				const rightSize = 0.2 + (3.5 - 0.2) * rightEaseProgress; // Larger like Whitebeard's bubble

				ability.quakeEffect.Size = new Vector3(rightSize, rightSize, rightSize);
				ability.quakeEffect.CFrame = new CFrame(rightHandPosition);

				// Increase right sphere intensity - exact same formula as original
				const rightLight = ability.quakeEffect.FindFirstChild("PointLight") as PointLight;
				if (rightLight) {
					rightLight.Brightness = 2 + rightProgress * 4; // Grows brighter as it expands like Whitebeard's bubble
				}
			}
		}
	});
}
