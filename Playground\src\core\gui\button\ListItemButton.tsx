import * as React from "@rbxts/react";
import { COLORS, BORDER_RADIUS } from "../../design";

interface ListItemButtonProps {
	children?: React.ReactNode;
	onClick?: () => void;
	selected?: boolean;
	size?: UDim2;
	layoutOrder?: number;
}

export function ListItemButton(props: ListItemButtonProps): React.ReactElement {
	const [hovered, setHovered] = React.useState(false);

	const backgroundColor = props.selected ? COLORS.bg["surface-hover"] : hovered ? COLORS.bg.hover : COLORS.bg.surface;

	const size = props.size ?? new UDim2(1, -10, 0, 40);

	return (
		<textbutton
			Text=""
			BackgroundColor3={Color3.fromHex(backgroundColor)}
			Size={size}
			BorderSizePixel={0}
			LayoutOrder={props.layoutOrder}
			AutoButtonColor={false}
			Event={{
				MouseEnter: () => {
					if (!props.selected) {
						setHovered(true);
					}
				},
				MouseLeave: () => {
					setHovered(false);
				},
				Activated: () => {
					if (props.onClick) {
						props.onClick();
					}
				},
			}}
		>
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
			{props.children}
		</textbutton>
	);
}
