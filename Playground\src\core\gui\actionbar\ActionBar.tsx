import * as React from "@rbxts/react";
import { HorizontalFrame } from "../frame";
import { AbilitySlot } from "./AbilitySlot";

export interface AbilitySlotData {
	id: string;
	name: string;
	icon?: string;
	cooldownEndTime?: number;
	isOnCooldown?: boolean;
	totalCooldownDuration?: number; // Total cooldown duration in seconds
}

interface ActionBarProps {
	slots: AbilitySlotData[];
	onSlotClick: (slotIndex: number, abilityId: string) => void;
	layoutOrder?: number;
	position?: UDim2;
	anchorPoint?: Vector2;
	zIndex?: number;
}

export function ActionBar(props: ActionBarProps): React.ReactElement {
	const slotSize = 60;
	const slotSpacing = 8;

	return (
		<HorizontalFrame
			fitContent={true} // Auto-size to fit content
			backgroundTransparency={1} // Fully transparent background
			position={props.position ?? new UDim2(0.5, 0, 1, -80)}
			anchorPoint={props.anchorPoint ?? new Vector2(0.5, 0)}
			layoutOrder={props.layoutOrder}
			spacing={slotSpacing}
			padding={8}
			horizontalAlignment={Enum.HorizontalAlignment.Center}
			verticalAlignment={Enum.VerticalAlignment.Center}
			zIndex={props.zIndex}
		>
			{props.slots.map((slot, index) => (
				<AbilitySlot
					key={`slot-${index}`}
					slotNumber={index + 1}
					abilityData={slot}
					onClick={() => props.onSlotClick(index, slot.id)}
					layoutOrder={index}
					size={new UDim2(0, slotSize, 0, slotSize)}
				/>
			))}
		</HorizontalFrame>
	);
}
