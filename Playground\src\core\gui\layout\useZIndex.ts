import { useState, useEffect } from "@rbxts/react";
import { ZIndexManager } from "./ZIndexManager";

/**
 * React Hook for automatic Z-Index management
 * Ensures that UI elements automatically layer correctly
 */
export function useZIndex(elementId?: string, bringToFrontOnMount = true) {
	const [zIndex, setZIndex] = useState<number>(1000);

	useEffect(() => {
		// Get the next Z-Index when component mounts
		const newZIndex = bringToFrontOnMount
			? ZIndexManager.getNextZIndex(elementId)
			: ZIndexManager.getCurrentZIndex();

		setZIndex(newZIndex);

		// Cleanup when component unmounts
		return () => {
			if (elementId) {
				ZIndexManager.unregister(elementId);
			}
		};
	}, [elementId, bringToFrontOnMount]);

	// Function to bring this element to front manually
	const bringToFront = () => {
		const newZIndex = ZIndexManager.bringToFront(elementId ?? `element-${math.random()}`);
		setZIndex(newZIndex);
		return newZIndex;
	};

	return {
		zIndex,
		bringToFront,
		currentHighest: ZIndexManager.getCurrentZIndex(),
	};
}
