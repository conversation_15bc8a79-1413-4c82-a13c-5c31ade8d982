-- Compiled with roblox-ts v3.0.0
local ErrorSeverity = {
	LOW = "low",
	MEDIUM = "medium",
	HIGH = "high",
	CRITICAL = "critical",
}
local ErrorCategory = {
	NETWORK = "network",
	VALIDATION = "validation",
	SYSTEM = "system",
	USER_INPUT = "user_input",
	BUSINESS_LOGIC = "business_logic",
	EXTERNAL_SERVICE = "external_service",
}
return {
	ErrorSeverity = ErrorSeverity,
	ErrorCategory = ErrorCategory,
}
