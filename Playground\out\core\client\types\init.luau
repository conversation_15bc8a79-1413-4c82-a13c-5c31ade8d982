-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- UI-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "client", "types", "ui", "UITypes") or {} do
	exports[_k] = _v
end
-- Player-related types
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "client", "types", "player", "PlayerTypes") or {} do
	exports[_k] = _v
end
-- Main client state
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "client", "types", "ClientGameState") or {} do
	exports[_k] = _v
end
return exports
