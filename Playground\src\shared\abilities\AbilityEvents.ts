// Shared networking events for ability system

import { ReplicatedStorage, RunService } from "@rbxts/services";
import { CastAbilityRequest, CastAbilityResponse, AbilityEffectData } from "./AbilityTypes";

// Function to safely get RemoteEvents (wait for them to be created)
function getRemoteEvents(): Folder {
	if (RunService.IsServer()) {
		// Server should create the events, so we can find them directly
		return ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
	} else {
		// Client waits for server to create them
		return ReplicatedStorage.WaitForChild("RemoteEvents") as Folder;
	}
}

// Lazy initialization of RemoteEvents
let remoteEventsFolder: Folder | undefined;
let castAbilityEvent: RemoteEvent | undefined;
let abilityCastConfirmEvent: RemoteEvent | undefined;
let abilityEffectEvent: RemoteEvent | undefined;
let requestAbilitiesEvent: RemoteEvent | undefined;
let abilitiesDataEvent: RemoteEvent | undefined;

function initializeEvents(): void {
	if (!remoteEventsFolder) {
		remoteEventsFolder = getRemoteEvents();
	}

	if (!castAbilityEvent) {
		castAbilityEvent = remoteEventsFolder.WaitForChild("CastAbility") as RemoteEvent;
	}

	if (!abilityCastConfirmEvent) {
		abilityCastConfirmEvent = remoteEventsFolder.WaitForChild("AbilityCastConfirm") as RemoteEvent;
	}

	if (!abilityEffectEvent) {
		abilityEffectEvent = remoteEventsFolder.WaitForChild("AbilityEffect") as RemoteEvent;
	}

	if (!requestAbilitiesEvent) {
		requestAbilitiesEvent = remoteEventsFolder.WaitForChild("RequestAbilities") as RemoteEvent;
	}

	if (!abilitiesDataEvent) {
		abilitiesDataEvent = remoteEventsFolder.WaitForChild("AbilitiesData") as RemoteEvent;
	}
}

// Getter functions that initialize events on first access
export function getCastAbilityEvent(): RemoteEvent {
	initializeEvents();
	return castAbilityEvent!;
}

export function getAbilityCastConfirmEvent(): RemoteEvent {
	initializeEvents();
	return abilityCastConfirmEvent!;
}

export function getAbilityEffectEvent(): RemoteEvent {
	initializeEvents();
	return abilityEffectEvent!;
}

export function getRequestAbilitiesEvent(): RemoteEvent {
	initializeEvents();
	return requestAbilitiesEvent!;
}

export function getAbilitiesDataEvent(): RemoteEvent {
	initializeEvents();
	return abilitiesDataEvent!;
}

// Type-safe event firing functions
export namespace AbilityNetworking {
	// Client-side functions
	export function requestCastAbility(request: CastAbilityRequest): void {
		getCastAbilityEvent().FireServer(request);
	}

	export function requestAbilitiesData(): void {
		getRequestAbilitiesEvent().FireServer();
	}

	// Server-side functions (will be used in server code)
	export function confirmAbilityCast(player: Player, response: CastAbilityResponse): void {
		getAbilityCastConfirmEvent().FireClient(player, response);
	}

	export function replicateAbilityEffect(effectData: AbilityEffectData): void {
		getAbilityEffectEvent().FireAllClients(effectData);
	}

	export function sendAbilitiesData(player: Player, abilities: Record<string, unknown>): void {
		getAbilitiesDataEvent().FireClient(player, abilities);
	}
}
