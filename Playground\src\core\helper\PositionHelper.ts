export class PositionHelper {
	static setPosition(instance: Instance, position: Vector3, rotation?: Vector3): void {
		if (instance.IsA("BasePart")) {
			instance.Position = position;
			if (rotation) {
				instance.Rotation = rotation;
			}
		} else if (instance.IsA("Model")) {
			PositionHelper.ensurePrimaryPart(instance as Model, position, rotation);
		}
	}

	static ensurePrimaryPart(model: Model, position: Vector3, rotation?: Vector3): Part {
		if (!model.PrimaryPart) {
			const primaryPart = new Instance("Part");
			primaryPart.Name = "PrimaryPart";
			primaryPart.Anchored = true;
			primaryPart.Transparency = 1;
			primaryPart.CanCollide = false;
			primaryPart.Size = new Vector3(0.1, 0.1, 0.1); // Minimal size
			primaryPart.Parent = model;
			model.PrimaryPart = primaryPart;
		}

		const primaryPart = model.PrimaryPart as Part;
		primaryPart.Position = position;
		if (rotation) {
			primaryPart.Rotation = rotation;
		}

		return primaryPart;
	}

	static moveModel(model: Model, position: Vector3): void {
		if (model.PrimaryPart) {
			model.PrimaryPart.Position = position;
		} else {
			PositionHelper.ensurePrimaryPart(model, position);
		}
	}

	static rotateModel(model: Model, rotation: Vector3): void {
		if (model.PrimaryPart) {
			model.PrimaryPart.Rotation = rotation;
		}
	}

	static getPosition(instance: Instance): Vector3 {
		if (instance.IsA("BasePart")) {
			return instance.Position;
		} else if (instance.IsA("Model") && instance.PrimaryPart) {
			return instance.PrimaryPart.Position;
		}
		return new Vector3(0, 0, 0);
	}

	static getRotation(instance: Instance): Vector3 {
		if (instance.IsA("BasePart")) {
			return instance.Rotation;
		} else if (instance.IsA("Model") && instance.PrimaryPart) {
			return instance.PrimaryPart.Rotation;
		}
		return new Vector3(0, 0, 0);
	}

	// Utility methods for common positioning tasks
	static offsetPosition(instance: Instance, offset: Vector3): Vector3 {
		const currentPos = PositionHelper.getPosition(instance);
		const newPos = currentPos.add(offset);
		PositionHelper.setPosition(instance, newPos);
		return newPos;
	}

	static lookAt(instance: Instance, target: Vector3): void {
		const currentPos = PositionHelper.getPosition(instance);
		const direction = target.sub(currentPos).Unit;
		const lookCFrame = CFrame.lookAt(currentPos, target);

		if (instance.IsA("BasePart")) {
			instance.CFrame = lookCFrame;
		} else if (instance.IsA("Model") && instance.PrimaryPart) {
			instance.PrimaryPart.CFrame = lookCFrame;
		}
	}

	static randomizePosition(instance: Instance, range: Vector3): Vector3 {
		const currentPos = PositionHelper.getPosition(instance);
		const randomOffset = new Vector3(
			(math.random() - 0.5) * range.X,
			(math.random() - 0.5) * range.Y,
			(math.random() - 0.5) * range.Z,
		);
		const newPos = currentPos.add(randomOffset);
		PositionHelper.setPosition(instance, newPos);
		return newPos;
	}

	static snapToGrid(instance: Instance, gridSize = 4): Vector3 {
		const currentPos = PositionHelper.getPosition(instance);
		const snappedPos = new Vector3(
			math.floor(currentPos.X / gridSize) * gridSize,
			math.floor(currentPos.Y / gridSize) * gridSize,
			math.floor(currentPos.Z / gridSize) * gridSize,
		);
		PositionHelper.setPosition(instance, snappedPos);
		return snappedPos;
	}

	static isInRange(instance1: Instance, instance2: Instance, range: number): boolean {
		const pos1 = PositionHelper.getPosition(instance1);
		const pos2 = PositionHelper.getPosition(instance2);
		return pos1.sub(pos2).Magnitude <= range;
	}

	static getDistance(instance1: Instance, instance2: Instance): number {
		const pos1 = PositionHelper.getPosition(instance1);
		const pos2 = PositionHelper.getPosition(instance2);
		return pos1.sub(pos2).Magnitude;
	}

	// Simple obstacle avoidance for AI
	static canMoveTo(from: Vector3, to: Vector3, ignoreList?: Instance[]): boolean {
		const direction = to.sub(from);
		const distance = direction.Magnitude;

		if (distance < 0.1) return true;

		const raycastParams = new RaycastParams();
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist;
		raycastParams.FilterDescendantsInstances = ignoreList || [];

		const result = game.Workspace.Raycast(from, direction, raycastParams);

		// If we hit something before reaching the target, path is blocked
		return !result || result.Distance >= distance;
	}

	static findClearPath(from: Vector3, to: Vector3, ignoreList?: Instance[]): Vector3 {
		// If direct path is clear, use it
		if (PositionHelper.canMoveTo(from, to, ignoreList)) {
			return to;
		}

		// Try simple avoidance by going around obstacles
		const direction = to.sub(from).Unit;
		const rightDirection = new Vector3(-direction.Z, 0, direction.X); // Perpendicular right
		const leftDirection = new Vector3(direction.Z, 0, -direction.X); // Perpendicular left

		const avoidanceDistance = 5; // studs to move sideways

		// Try going right around obstacle
		const rightTarget = from.add(rightDirection.mul(avoidanceDistance));
		if (PositionHelper.canMoveTo(from, rightTarget, ignoreList)) {
			return rightTarget;
		}

		// Try going left around obstacle
		const leftTarget = from.add(leftDirection.mul(avoidanceDistance));
		if (PositionHelper.canMoveTo(from, leftTarget, ignoreList)) {
			return leftTarget;
		}

		// If all else fails, return original target
		return to;
	}

	static hasLineOfSight(from: Vector3, to: Vector3, ignoreList?: Instance[]): boolean {
		const direction = to.sub(from);
		const distance = direction.Magnitude;

		if (distance < 0.1) return true;

		const raycastParams = new RaycastParams();
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist;
		raycastParams.FilterDescendantsInstances = ignoreList || [];

		const result = game.Workspace.Raycast(from, direction, raycastParams);

		// Line of sight is clear if we don't hit anything or hit something beyond the target
		return !result || result.Distance >= distance;
	}
}
