import { ClientAbilitySlot, VoiceChatParticipant } from "./ui/UITypes";
import { PlayerAbility } from "./player/PlayerTypes";

// Client-specific state interface
export interface ClientGameState {
	ui: {
		debugPanel: {
			isOpen: boolean;
			activeTab: string;
		};
		worldTestingPanel: {
			isOpen: boolean;
		};
		actionBar: {
			isVisible: boolean;
			abilities: ClientAbilitySlot[];
		};
		voiceChat: {
			isOpen: boolean;
			participants: VoiceChatParticipant[];
		};
	};
	player: {
		abilities: PlayerAbility[];
		cooldowns: Map<string, number>;
		position: Vector3;
		health: number;
		maxHealth: number;
	};
	world: {
		weather: string;
		timeOfDay: number;
		gravity: number;
	};
	network: {
		ping: number;
		isConnected: boolean;
		lastServerUpdate: number;
	};
}

// State action interface
export interface ClientStateAction<T = unknown> {
	type: string;
	payload: T;
}

// State update function type
export type ClientStateUpdater<T = unknown> = (state: ClientGameState, payload: T) => ClientGameState;
