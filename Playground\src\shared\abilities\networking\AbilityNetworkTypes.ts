// Request to cast an ability
export interface CastAbilityRequest {
	abilityId: string;
	targetPosition?: Vector3;
	targetDirection?: Vector3;
	targetPlayer?: number; // Player UserId
	timestamp: number;
}

// Response after ability cast attempt
export interface CastAbilityResponse {
	success: boolean;
	abilityId: string;
	cooldownEndTime: number;
	errorMessage?: string;
}

// Data for ability effects
export interface AbilityEffectData {
	abilityId: string;
	casterUserId: number;
	startPosition: Vector3;
	direction: Vector3;
	timestamp: number;
	effectId: string;
	targetPosition?: Vector3;
	targetUserId?: number;
}
