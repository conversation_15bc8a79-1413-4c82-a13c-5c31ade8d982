export class Result<T, E> {
	private constructor(
		private readonly _value?: T,
		private readonly _error?: E,
		private readonly _isSuccess: boolean = true,
	) {}

	public static ok<T, E>(value: T): Result<T, E> {
		return new Result<T, E>(value, undefined, true);
	}

	public static err<T, E>(errorValue: E): Result<T, E> {
		return new Result<T, E>(undefined, errorValue, false);
	}

	public isOk(): boolean {
		return this._isSuccess;
	}

	public isError(): boolean {
		return !this._isSuccess;
	}

	public getValue(): T {
		if (!this._isSuccess) {
			error("Cannot access value of error result");
		}
		return this._value!;
	}

	public getError(): E {
		if (this._isSuccess) {
			error("Cannot access error of success result");
		}
		return this._error!;
	}

	public map<U>(fn: (value: T) => U): Result<U, E> {
		if (this._isSuccess) {
			return Result.ok(fn(this._value!));
		}
		return Result.err(this._error!);
	}

	public mapError<F>(fn: (error: E) => F): Result<T, F> {
		if (this._isSuccess) {
			return Result.ok(this._value!);
		}
		return Result.err(fn(this._error!));
	}

	public flatMap<U>(fn: (value: T) => Result<U, E>): Result<U, E> {
		if (this._isSuccess) {
			return fn(this._value!);
		}
		return Result.err(this._error!);
	}

	public unwrapOr(defaultValue: T): T {
		return this._isSuccess ? this._value! : defaultValue;
	}

	public unwrapOrElse(fn: (error: E) => T): T {
		return this._isSuccess ? this._value! : fn(this._error!);
	}

	public match<U>(onOk: (value: T) => U, onError: (error: E) => U): U {
		return this._isSuccess ? onOk(this._value!) : onError(this._error!);
	}
}
