import * as React from "@rbxts/react";
interface LoadingSpinnerProps {
    size?: number;
    color?: string;
    speed?: number;
}
export declare const LoadingSpinner: React.MemoExoticComponent<(props: LoadingSpinnerProps) => React.ReactElement>;
interface LoadingIndicatorProps {
    text?: string;
    size?: "sm" | "md" | "lg";
    variant?: "spinner" | "dots" | "pulse";
    color?: string;
}
export declare const LoadingIndicator: React.MemoExoticComponent<(props: LoadingIndicatorProps) => React.ReactElement>;
export {};
