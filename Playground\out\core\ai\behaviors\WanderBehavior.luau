-- Compiled with roblox-ts v3.0.0
local WanderBehavior
do
	WanderBehavior = setmetatable({}, {
		__tostring = function()
			return "WanderBehavior"
		end,
	})
	WanderBehavior.__index = WanderBehavior
	function WanderBehavior.new(...)
		local self = setmetatable({}, WanderBehavior)
		return self:constructor(...) or self
	end
	function WanderBehavior:constructor()
		self.name = "Wander"
		self.priority = 2
	end
	function WanderBehavior:canExecute(context)
		return not context.target
	end
	function WanderBehavior:execute(context)
		local wanderTarget = context.blackboard.wanderTarget
		local _condition = (context.blackboard.wanderTime)
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local wanderTime = _condition
		if not wanderTarget or wanderTime <= 0 then
			self:setNewWanderTarget(context)
			return {
				success = true,
				completed = false,
			}
		end
		local distance = (context.position - wanderTarget).Magnitude
		if distance <= 3 then
			context.blackboard.wanderTarget = nil
			context.blackboard.wanderTime = 0
			return {
				success = true,
				completed = true,
				nextBehavior = "Idle",
			}
		end
		self:moveTowards(context, wanderTarget)
		context.blackboard.wanderTime = wanderTime - context.deltaTime
		return {
			success = true,
			completed = false,
		}
	end
	function WanderBehavior:onEnter(context)
		print(`🚶 {context.entityId} is wandering`)
	end
	function WanderBehavior:setNewWanderTarget(context)
		local wanderRadius = 15
		local randomAngle = math.random() * math.pi * 2
		local randomDistance = math.random() * wanderRadius
		local x = context.position.X + math.cos(randomAngle) * randomDistance
		local z = context.position.Z + math.sin(randomAngle) * randomDistance
		context.blackboard.wanderTarget = Vector3.new(x, context.position.Y, z)
		context.blackboard.wanderTime = 5 + math.random() * 5
	end
	function WanderBehavior:moveTowards(context, targetPosition)
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(targetPosition)
			end
		end
	end
end
return {
	WanderBehavior = WanderBehavior,
}
