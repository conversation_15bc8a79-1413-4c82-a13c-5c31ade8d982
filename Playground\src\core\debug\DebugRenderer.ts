import { Workspace, Players } from "@rbxts/services";
import { COLORS, BORDER_RADIUS } from "../design";
import { ZIndexManager } from "../gui/layout/ZIndexManager";
import { DebugLine, DebugSphere, DebugText } from "./types/DebugPrimitives";

// Re-export types for backward compatibility
export * from "./types/DebugPrimitives";

export class DebugRenderer {
	private lines: DebugLine[] = [];
	private spheres: DebugSphere[] = [];
	private texts: DebugText[] = [];
	private debugFolder?: Folder;
	private guiFolder?: ScreenGui;
	private isVisible = false;

	constructor() {
		this.setupDebugFolder();
		this.setupGUI();
	}

	public show(): void {
		this.isVisible = true;
		if (this.debugFolder) {
			this.debugFolder.Parent = Workspace;
		}
		if (this.guiFolder) {
			this.guiFolder.Enabled = true;
		}
	}

	public hide(): void {
		this.isVisible = false;
		if (this.debugFolder) {
			this.debugFolder.Parent = undefined;
		}
		if (this.guiFolder) {
			this.guiFolder.Enabled = false;
		}
	}

	public drawLine(from: Vector3, to: Vector3, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		this.lines.push({
			from,
			to,
			color,
			duration,
			createdAt: tick(),
		});
	}

	public drawSphere(position: Vector3, radius = 1, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		this.spheres.push({
			position,
			radius,
			color,
			duration,
			createdAt: tick(),
		});
	}

	public drawText(position: Vector3, text: string, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		this.texts.push({
			position,
			text,
			color,
			duration,
			createdAt: tick(),
		});
	}

	public render(): void {
		if (!this.isVisible || !this.debugFolder) return;

		// Clear old debug objects
		this.debugFolder.GetChildren().forEach((child) => child.Destroy());

		const currentTime = tick();

		// Render lines
		this.lines = this.lines.filter((line) => {
			if (currentTime - line.createdAt > line.duration) {
				return false;
			}

			this.createLinePart(line);
			return true;
		});

		// Render spheres
		this.spheres = this.spheres.filter((sphere) => {
			if (currentTime - sphere.createdAt > sphere.duration) {
				return false;
			}

			this.createSpherePart(sphere);
			return true;
		});

		// Render 3D text
		this.texts = this.texts.filter((text) => {
			if (currentTime - text.createdAt > text.duration) {
				return false;
			}

			this.create3DText(text);
			return true;
		});
	}

	private setupDebugFolder(): void {
		this.debugFolder = new Instance("Folder");
		this.debugFolder.Name = "DebugVisuals";
		// Don't parent it yet - will be parented when shown
	}

	private setupGUI(): void {
		const player = Players.LocalPlayer;
		if (!player) return;

		const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;

		this.guiFolder = new Instance("ScreenGui");
		this.guiFolder.Name = "DebugGUI";
		this.guiFolder.ResetOnSpawn = false;
		this.guiFolder.Enabled = false;
		this.guiFolder.DisplayOrder = 50; // Lower than MainReactGUI to ensure debug overlays stay in background
		this.guiFolder.Parent = playerGui;

		print(`🔧 [${tick()}] DebugGUI ScreenGui created with DisplayOrder: ${this.guiFolder.DisplayOrder}`);
	}

	private createLinePart(line: DebugLine): void {
		if (!this.debugFolder) return;

		const distance = line.from.sub(line.to).Magnitude;
		const midpoint = line.from.add(line.to).div(2);
		const direction = line.to.sub(line.from).Unit;

		const part = new Instance("Part");
		part.Name = "DebugLine";
		part.Size = new Vector3(0.1, 0.1, distance);
		part.Material = Enum.Material.Neon;
		part.Color = line.color;
		part.Anchored = true;
		part.CanCollide = false;
		part.Transparency = 0.3;
		part.CFrame = CFrame.lookAt(midpoint, midpoint.add(direction));
		part.Parent = this.debugFolder;
	}

	private createSpherePart(sphere: DebugSphere): void {
		if (!this.debugFolder) return;

		const part = new Instance("Part");
		part.Name = "DebugSphere";
		part.Shape = Enum.PartType.Ball;
		part.Size = new Vector3(sphere.radius * 2, sphere.radius * 2, sphere.radius * 2);
		part.Material = Enum.Material.ForceField;
		part.Color = sphere.color;
		part.Position = sphere.position;
		part.Anchored = true;
		part.CanCollide = false;
		part.Transparency = 0.7;
		part.Parent = this.debugFolder;
	}

	private create3DText(text: DebugText): void {
		if (!this.debugFolder) return;

		// Create a part to hold the text
		const part = new Instance("Part");
		part.Name = "DebugText";
		part.Size = new Vector3(0.1, 0.1, 0.1);
		part.Position = text.position;
		part.Anchored = true;
		part.CanCollide = false;
		part.Transparency = 1;
		part.Parent = this.debugFolder;

		// Create BillboardGui for 3D text
		const billboard = new Instance("BillboardGui");
		billboard.Size = UDim2.fromOffset(200, 50);
		billboard.StudsOffset = new Vector3(0, 2, 0);
		billboard.Parent = part;

		// Create text label
		const label = new Instance("TextLabel");
		label.Size = UDim2.fromScale(1, 1);
		label.BackgroundTransparency = 1;
		label.Text = text.text;
		label.TextColor3 = text.color;
		label.TextScaled = true;
		label.Font = Enum.Font.SourceSansBold;
		label.TextStrokeTransparency = 0;
		label.TextStrokeColor3 = Color3.fromRGB(0, 0, 0);
		label.Parent = billboard;
	}

	private unifiedDebugFrame?: Frame;
	private unifiedDebugLabels: Map<string, TextLabel> = new Map();

	public createUnifiedDebugPanel(): void {
		if (!this.guiFolder) return;

		print(`🔧 [${tick()}] DebugRenderer: Creating UnifiedDebugFrame`);

		// Remove existing unified frame if it exists
		if (this.unifiedDebugFrame) {
			print(`🔧 [${tick()}] DebugRenderer: Destroying existing UnifiedDebugFrame`);
			this.unifiedDebugFrame.Destroy();
		}

		// Create the main unified frame
		this.unifiedDebugFrame = new Instance("Frame");
		this.unifiedDebugFrame.Name = "UnifiedDebugFrame";
		this.unifiedDebugFrame.BackgroundColor3 = Color3.fromHex(COLORS.bg.base);
		this.unifiedDebugFrame.BackgroundTransparency = 0.05;
		this.unifiedDebugFrame.BorderSizePixel = 0;
		this.unifiedDebugFrame.AutomaticSize = Enum.AutomaticSize.Y; // Auto-resize height based on content
		this.unifiedDebugFrame.ClipsDescendants = true; // Prevent text overflow

		// Use debug overlay ZIndex
		const zIndex = ZIndexManager.getDebugOverlayZIndex("debug-unified");
		this.unifiedDebugFrame.ZIndex = zIndex;
		print(`🔧 [${tick()}] DebugRenderer: Applied z-index ${zIndex} to UnifiedDebugFrame`);
		this.unifiedDebugFrame.Parent = this.guiFolder;

		// Add corner radius using design system
		const corner = new Instance("UICorner");
		corner.CornerRadius = new UDim(0, BORDER_RADIUS.md);
		corner.Parent = this.unifiedDebugFrame;

		// Add border using design system
		const stroke = new Instance("UIStroke");
		stroke.Color = Color3.fromHex(COLORS.border.l2);
		stroke.Thickness = 1;
		stroke.Parent = this.unifiedDebugFrame;

		// Add padding using UIPadding
		const padding = new Instance("UIPadding");
		padding.PaddingTop = new UDim(0, 12);
		padding.PaddingBottom = new UDim(0, 12);
		padding.PaddingLeft = new UDim(0, 16);
		padding.PaddingRight = new UDim(0, 16);
		padding.Parent = this.unifiedDebugFrame;

		// Add UIListLayout for vertical stacking of sections
		const listLayout = new Instance("UIListLayout");
		listLayout.SortOrder = Enum.SortOrder.LayoutOrder;
		listLayout.FillDirection = Enum.FillDirection.Vertical;
		listLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left;
		listLayout.VerticalAlignment = Enum.VerticalAlignment.Top;
		listLayout.Padding = new UDim(0, 8); // Spacing between sections
		listLayout.Parent = this.unifiedDebugFrame;

		print(`🔧 Unified debug panel created with z-index: ${zIndex}`);
	}

	public addDebugSection(sectionName: string, text: string, layoutOrder: number): TextLabel | undefined {
		if (!this.unifiedDebugFrame) {
			this.createUnifiedDebugPanel();
		}

		// Remove existing section if it exists
		const existingLabel = this.unifiedDebugLabels.get(sectionName);
		if (existingLabel) {
			existingLabel.Destroy();
		}

		// Add separator line between sections (except for the first one)
		if (layoutOrder > 1) {
			const separatorName = sectionName + "Separator";
			const existingSeparator = this.unifiedDebugLabels.get(separatorName);
			if (existingSeparator) {
				existingSeparator.Destroy();
			}

			const separator = new Instance("Frame");
			separator.Name = separatorName;
			separator.Size = new UDim2(1, 0, 0, 1); // Full width, 1px height
			separator.BackgroundColor3 = Color3.fromHex(COLORS.border.l2);
			separator.BackgroundTransparency = 0.5;
			separator.BorderSizePixel = 0;
			separator.ZIndex = this.unifiedDebugFrame!.ZIndex + 1;
			separator.LayoutOrder = layoutOrder - 0.5; // Place before the section
			separator.Parent = this.unifiedDebugFrame;

			this.unifiedDebugLabels.set(separatorName, separator as any); // Store for cleanup - separator is Frame, not TextLabel
		}

		const label = new Instance("TextLabel");
		label.Name = sectionName + "Label";
		label.Size = new UDim2(1, 0, 0, 0); // Full width, auto height
		label.AutomaticSize = Enum.AutomaticSize.Y; // Auto-resize height based on text
		label.BackgroundTransparency = 1;
		label.Text = text;
		label.TextColor3 = Color3.fromHex(COLORS.text.main);
		label.TextSize = 14;
		label.Font = Enum.Font.RobotoMono; // Use monospace font for better alignment
		label.TextXAlignment = Enum.TextXAlignment.Left;
		label.TextYAlignment = Enum.TextYAlignment.Top;
		label.TextWrapped = true; // Enable text wrapping to prevent overflow
		label.ZIndex = this.unifiedDebugFrame!.ZIndex + 1;
		label.LayoutOrder = layoutOrder;
		label.Parent = this.unifiedDebugFrame;

		print(
			`🔧 DebugRenderer: Added section "${sectionName}" with z-index ${label.ZIndex} (parent: ${this.unifiedDebugFrame!.ZIndex})`,
		);
		this.unifiedDebugLabels.set(sectionName, label);
		return label;
	}

	public updateDebugSection(sectionName: string, text: string): void {
		const label = this.unifiedDebugLabels.get(sectionName);
		if (label) {
			label.Text = text;
		}
	}

	public setUnifiedDebugPosition(position: UDim2, size: UDim2): void {
		if (this.unifiedDebugFrame) {
			this.unifiedDebugFrame.Position = position;
			this.unifiedDebugFrame.Size = size;
		}
	}

	// Keep the old method for backward compatibility, but mark as deprecated
	public createGUIElement(name: string, position: UDim2, size: UDim2, text: string): TextLabel | undefined {
		// For backward compatibility, redirect to unified system
		if (!this.unifiedDebugFrame) {
			this.createUnifiedDebugPanel();
			this.setUnifiedDebugPosition(position, size);
		}

		// Determine layout order based on name
		let layoutOrder = 1;
		if (name === "Performance") layoutOrder = 1;
		else if (name === "AIInfo") layoutOrder = 2;
		else if (name === "PlayerInfo") layoutOrder = 3;

		return this.addDebugSection(name, text, layoutOrder);
	}

	public updateGUIElement(name: string, text: string): void {
		// Use the unified system
		this.updateDebugSection(name, text);
	}

	public drawPath(points: Vector3[], color = Color3.fromRGB(255, 255, 0), duration = 0.1): void {
		for (let i = 0; i < points.size() - 1; i++) {
			this.drawLine(points[i], points[i + 1], color, duration);
		}

		// Draw waypoint markers
		points.forEach((point, index) => {
			this.drawSphere(point, 0.5, color, duration);
			this.drawText(point, `${index}`, color, duration);
		});
	}

	public drawVisionCone(
		position: Vector3,
		direction: Vector3,
		range: number,
		angle: number,
		color = Color3.fromRGB(0, 255, 0),
		duration = 0.1,
	): void {
		const angleRad = math.rad(angle / 2);
		const steps = 8;

		// Draw vision cone lines
		for (let i = 0; i <= steps; i++) {
			const currentAngle = -angleRad + (angleRad * 2 * i) / steps;
			const rotatedDirection = direction.mul(range);

			// Simple 2D rotation around Y axis
			const x = rotatedDirection.X * math.cos(currentAngle) - rotatedDirection.Z * math.sin(currentAngle);
			const z = rotatedDirection.X * math.sin(currentAngle) + rotatedDirection.Z * math.cos(currentAngle);
			const endPoint = position.add(new Vector3(x, rotatedDirection.Y, z));

			this.drawLine(position, endPoint, color, duration);
		}

		// Draw arc at the end
		for (let i = 0; i < steps; i++) {
			const angle1 = -angleRad + (angleRad * 2 * i) / steps;
			const angle2 = -angleRad + (angleRad * 2 * (i + 1)) / steps;

			const x1 = direction.X * range * math.cos(angle1) - direction.Z * range * math.sin(angle1);
			const z1 = direction.X * range * math.sin(angle1) + direction.Z * range * math.cos(angle1);
			const point1 = position.add(new Vector3(x1, direction.Y * range, z1));

			const x2 = direction.X * range * math.cos(angle2) - direction.Z * range * math.sin(angle2);
			const z2 = direction.X * range * math.sin(angle2) + direction.Z * range * math.cos(angle2);
			const point2 = position.add(new Vector3(x2, direction.Y * range, z2));

			this.drawLine(point1, point2, color, duration);
		}
	}

	public cleanup(): void {
		this.lines = [];
		this.spheres = [];
		this.texts = [];

		// Clear unified debug system
		this.unifiedDebugLabels.clear();
		if (this.unifiedDebugFrame) {
			this.unifiedDebugFrame.Destroy();
			this.unifiedDebugFrame = undefined;
		}

		if (this.debugFolder) {
			this.debugFolder.Destroy();
		}

		if (this.guiFolder) {
			this.guiFolder.Destroy();
		}
	}
}
