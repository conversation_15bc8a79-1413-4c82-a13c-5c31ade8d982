-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local ReplicatedStorage = _services.ReplicatedStorage
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local PlayerDataManager = _core.PlayerDataManager
local DataStoreHelper = _core.DataStoreHelper
local ServerDataStoreService
do
	ServerDataStoreService = setmetatable({}, {
		__tostring = function()
			return "ServerDataStoreService"
		end,
	})
	ServerDataStoreService.__index = ServerDataStoreService
	function ServerDataStoreService.new(...)
		local self = setmetatable({}, ServerDataStoreService)
		return self:constructor(...) or self
	end
	function ServerDataStoreService:constructor()
		self.playerDataManager = PlayerDataManager:getInstance()
		self.dataStoreHelper = DataStoreHelper:getInstance()
		self:setupRemoteEvents()
		self:setupPlayerEvents()
	end
	function ServerDataStoreService:getInstance()
		if not ServerDataStoreService.instance then
			ServerDataStoreService.instance = ServerDataStoreService.new()
		end
		return ServerDataStoreService.instance
	end
	function ServerDataStoreService:setupRemoteEvents()
		-- Create RemoteEvents folder if it doesn't exist
		local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
		if not remoteEventsFolder then
			remoteEventsFolder = Instance.new("Folder")
			remoteEventsFolder.Name = "RemoteEvents"
			remoteEventsFolder.Parent = ReplicatedStorage
		end
		self.remoteEvents = remoteEventsFolder
		-- Create DataStore testing RemoteEvent
		local dataStoreEvent = Instance.new("RemoteEvent")
		dataStoreEvent.Name = "DATA_STORE_TESTING"
		dataStoreEvent.Parent = self.remoteEvents
		-- Handle client requests
		dataStoreEvent.OnServerEvent:Connect(function(player, request)
			self:handleDataStoreRequest(player, request)
		end)
	end
	function ServerDataStoreService:setupPlayerEvents()
		Players.PlayerAdded:Connect(function(player)
			-- Player data is automatically loaded by PlayerDataManager
			print(`🔄 Player {player.Name} joined - data loading handled automatically`)
		end)
		Players.PlayerRemoving:Connect(function(player)
			-- Player data is automatically saved by PlayerDataManager
			print(`💾 Player {player.Name} leaving - data saving handled automatically`)
		end)
	end
	ServerDataStoreService.handleDataStoreRequest = TS.async(function(self, player, request)
		local _exitType, _returns = TS.try(function()
			-- Basic input validation
			local _request = request
			if typeof(_request) ~= "table" then
				local errorResponse = {
					action = "error",
					result = {
						success = false,
						error = "Request must be a table",
					},
				}
				self:sendErrorResponse(player, errorResponse)
				return TS.TRY_RETURN, {}
			end
			local _binding = request
			local action = _binding.action
			local data = _binding.data
			-- Validate action
			if typeof(action) ~= "string" then
				local errorResponse = {
					action = "error",
					result = {
						success = false,
						error = "Action must be a string",
					},
				}
				self:sendErrorResponse(player, errorResponse)
				return TS.TRY_RETURN, {}
			end
			local result
			repeat
				if action == "loadPlayerData" then
					result = TS.await(self.playerDataManager:onPlayerJoin(player))
					break
				end
				if action == "updateGameData" then
					local gameData = data
					result = TS.await(self.playerDataManager:updatePlayerGameData(player.UserId, gameData.gameDataUpdates or {
						testValue = tick(),
					}))
					break
				end
				if action == "updateSettings" then
					local settingsData = data
					result = TS.await(self.playerDataManager:updatePlayerSettings(player.UserId, {
						musicVolume = if settingsData.musicVolume ~= nil then settingsData.musicVolume else 0.6,
						graphics = (settingsData.graphics) or "High",
					}))
					break
				end
				if action == "getStats" then
					local cacheSize = self.dataStoreHelper:getCacheSize()
					local loadedPlayers = self.playerDataManager:getLoadedPlayerCount()
					local isPlayerLoaded = self.playerDataManager:isPlayerLoaded(player.UserId)
					result = {
						success = true,
						data = {
							cacheSize = cacheSize,
							loadedPlayers = loadedPlayers,
							isPlayerLoaded = isPlayerLoaded,
						},
					}
					break
				end
				do
					result = {
						success = false,
						error = `Unknown action: {action}`,
					}
					break
				end
			until true
			-- Send result back to client
			local responseEvent = self.remoteEvents:FindFirstChild("DATA_STORE_RESPONSE")
			local response = {
				action = action,
				result = result,
			}
			if not responseEvent then
				local newResponseEvent = Instance.new("RemoteEvent")
				newResponseEvent.Name = "DATA_STORE_RESPONSE"
				newResponseEvent.Parent = self.remoteEvents
				newResponseEvent:FireClient(player, response)
			else
				responseEvent:FireClient(player, response)
			end
		end, function(error)
			print(`❌ DataStore request error: {error}`)
			-- Send error response
			local responseEvent = self.remoteEvents:FindFirstChild("DATA_STORE_RESPONSE")
			if responseEvent then
				local errorResponse = {
					action = request.action,
					result = {
						success = false,
						error = tostring(error),
					},
				}
				responseEvent:FireClient(player, errorResponse)
			end
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function ServerDataStoreService:sendErrorResponse(player, errorResponse)
		local responseEvent = self.remoteEvents:FindFirstChild("DATA_STORE_RESPONSE")
		if not responseEvent then
			local newResponseEvent = Instance.new("RemoteEvent")
			newResponseEvent.Name = "DATA_STORE_RESPONSE"
			newResponseEvent.Parent = self.remoteEvents
			newResponseEvent:FireClient(player, errorResponse)
		else
			responseEvent:FireClient(player, errorResponse)
		end
	end
	function ServerDataStoreService:cleanup()
		self.playerDataManager:cleanup()
		self.dataStoreHelper:cleanup()
	end
end
return {
	ServerDataStoreService = ServerDataStoreService,
}
