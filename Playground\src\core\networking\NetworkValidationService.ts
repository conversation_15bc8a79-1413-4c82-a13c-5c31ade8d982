import { Players } from "@rbxts/services";
import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { EventName, PlayerId } from "../foundation/types/BrandedTypes";
import { Error, createError } from "../foundation/types/RobloxError";
import { NetworkError } from "./errors/NetworkError";

interface RateLimitEntry {
	calls: number;
	windowStart: number;
}

export class NetworkValidationService extends BaseService {
	private rateLimitMap = new Map<string, RateLimitEntry>();
	private authenticatedPlayers = new Set<number>();

	constructor() {
		super("NetworkValidationService");
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		this.startCleanupTask();
		return Result.ok(undefined);
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.rateLimitMap.clear();
		this.authenticatedPlayers.clear();
		return Result.ok(undefined);
	}

	public checkRateLimit(
		player: Player,
		eventName: EventName,
		rateLimit: { maxCalls: number; windowMs: number },
	): Result<void, NetworkError> {
		const key = `${player.UserId}_${eventName}`;
		const now = tick() * 1000;

		let entry = this.rateLimitMap.get(key);
		if (!entry || now - entry.windowStart > rateLimit.windowMs) {
			entry = { calls: 0, windowStart: now };
			this.rateLimitMap.set(key, entry);
		}

		entry.calls++;

		if (entry.calls > rateLimit.maxCalls) {
			this.logWarning(`Rate limit exceeded for player ${player.Name} on event ${eventName}`);
			return Result.err(new NetworkError("Rate limit exceeded"));
		}

		return Result.ok(undefined);
	}

	public validateAuthentication(player: Player): Result<void, NetworkError> {
		if (!this.authenticatedPlayers.has(player.UserId)) {
			return Result.err(new NetworkError("Player not authenticated"));
		}
		return Result.ok(undefined);
	}

	public authenticatePlayer(playerId: PlayerId): void {
		this.authenticatedPlayers.add(playerId);
		this.logInfo(`Player ${playerId} authenticated`);
	}

	public deauthenticatePlayer(playerId: PlayerId): void {
		this.authenticatedPlayers.delete(playerId);
		this.logInfo(`Player ${playerId} deauthenticated`);
	}

	public validatePlayerPosition(
		player: Player,
		reportedPosition: Vector3,
		maxDistance: number = 100,
	): Result<void, NetworkError> {
		const character = player.Character;
		if (!character) {
			return Result.err(new NetworkError("Player has no character"));
		}

		const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as BasePart;
		if (!humanoidRootPart) {
			return Result.err(new NetworkError("Player has no HumanoidRootPart"));
		}

		const actualPosition = humanoidRootPart.Position;
		const distance = actualPosition.sub(reportedPosition).Magnitude;

		if (distance > maxDistance) {
			this.logWarning(`Position validation failed for player ${player.Name}: distance ${distance}`);
			return Result.err(new NetworkError("Invalid player position"));
		}

		return Result.ok(undefined);
	}

	public validatePlayerExists(playerId: PlayerId): Result<Player, NetworkError> {
		const player = Players.GetPlayerByUserId(playerId);
		if (!player) {
			return Result.err(new NetworkError(`Player with ID ${playerId} not found`));
		}
		return Result.ok(player);
	}

	private startCleanupTask(): void {
		task.spawn(() => {
			while (this.isInitialized && !this.isShutdown) {
				this.cleanupExpiredEntries();
				task.wait(60); // Cleanup every minute
			}
		});
	}

	private cleanupExpiredEntries(): void {
		const now = tick() * 1000;
		const expiredKeys: string[] = [];

		for (const [key, entry] of this.rateLimitMap) {
			if (now - entry.windowStart > 300000) {
				// 5 minutes
				expiredKeys.push(key);
			}
		}

		for (const key of expiredKeys) {
			this.rateLimitMap.delete(key);
		}

		if (expiredKeys.size() > 0) {
			this.logDebug(`Cleaned up ${expiredKeys.size()} expired rate limit entries`);
		}
	}
}
