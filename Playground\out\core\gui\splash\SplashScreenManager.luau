-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local SplashScreenManager
do
	SplashScreenManager = setmetatable({}, {
		__tostring = function()
			return "SplashScreenManager"
		end,
	})
	SplashScreenManager.__index = SplashScreenManager
	function SplashScreenManager.new(...)
		local self = setmetatable({}, SplashScreenManager)
		return self:constructor(...) or self
	end
	function SplashScreenManager:constructor()
		self.loadingTasks = {}
		self.currentProgress = 0
		self.currentText = "Initializing..."
		self.isVisible = false
	end
	function SplashScreenManager:getInstance()
		if not self.instance then
			self.instance = SplashScreenManager.new()
		end
		return self.instance
	end
	function SplashScreenManager:addLoadingTask(task)
		local _loadingTasks = self.loadingTasks
		local _task = task
		table.insert(_loadingTasks, _task)
	end
	function SplashScreenManager:addLoadingTasks(tasks)
		for _, task in tasks do
			local _exp = self.loadingTasks
			table.insert(_exp, task)
		end
	end
	function SplashScreenManager:onStateChanged(callback)
		self.onStateChange = callback
	end
	SplashScreenManager.startLoading = TS.async(function(self)
		self.isVisible = true
		self.currentProgress = 0
		self.currentText = "Initializing Core Framework..."
		self:notifyStateChange()
		local _exp = self.loadingTasks
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback = function(sum, task)
			return sum + task.weight
		end
		for _i = 1, #_exp do
			_result = _callback(_result, _exp[_i], _i - 1, _exp)
		end
		-- ▲ ReadonlyArray.reduce ▲
		local totalWeight = _result
		local completedWeight = 0
		for _, task in self.loadingTasks do
			self.currentText = task.name
			self:notifyStateChange()
			TS.try(function()
				-- Execute the task
				local result = task.task()
				if result ~= nil then
					TS.await(result)
				end
				-- Update progress
				completedWeight += task.weight
				self.currentProgress = completedWeight / totalWeight
				self:notifyStateChange()
				-- Small delay for visual feedback
				TS.await(self:delay(0.1))
			end, function(error)
				print(`❌ Loading task failed: {task.name} - {error}`)
				self.currentText = `Error loading: {task.name}`
				self:notifyStateChange()
				TS.await(self:delay(1))
			end)
		end
		-- Ensure we reach 100%
		self.currentProgress = 1
		self.currentText = "Loading Complete!"
		self:notifyStateChange()
		-- Wait a moment before hiding
		TS.await(self:delay(0.5))
	end)
	function SplashScreenManager:hide()
		self.isVisible = false
		self:notifyStateChange()
	end
	function SplashScreenManager:getState()
		return {
			isVisible = self.isVisible,
			loadingProgress = self.currentProgress,
			loadingText = self.currentText,
		}
	end
	function SplashScreenManager:setupDefaultCoreTasks()
		self:addLoadingTasks({ {
			name = "Initializing Physics System...",
			weight = 1,
			task = TS.async(function()
				-- Import and initialize physics
				local _binding = TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "PhysicsImpactHelper"))
				end))
				local PhysicsImpactHelper = _binding.PhysicsImpactHelper
				PhysicsImpactHelper:initialize()
			end),
		}, {
			name = "Loading Weather System...",
			weight = 1,
			task = TS.async(function()
				-- Import weather systems
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "WeatherController"))
				end))
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "WeatherParticleHelper"))
				end))
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "WeatherSoundHelper"))
				end))
			end),
		}, {
			name = "Initializing UI Framework...",
			weight = 1,
			task = TS.async(function()
				-- Import UI components
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager"))
				end))
				local _binding = TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager"))
				end))
				local ResponsiveManager = _binding.ResponsiveManager
				ResponsiveManager:getInstance()
			end),
		}, {
			name = "Loading Sound System...",
			weight = 1,
			task = TS.async(function()
				-- Import sound helpers
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper"))
				end))
			end),
		}, {
			name = "Initializing Animation System...",
			weight = 1,
			task = TS.async(function()
				-- Import animation systems
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "AnimationBuilder"))
				end))
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "LimbAnimator"))
				end))
			end),
		}, {
			name = "Loading Entity Management...",
			weight = 1,
			task = TS.async(function()
				-- Import entity systems
				TS.await(TS.Promise.new(function(resolve)
					resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager"))
				end))
			end),
		}, {
			name = "Finalizing Core Framework...",
			weight = 1,
			task = TS.async(function()
				-- Final initialization
				print("✅ Core Framework loaded successfully!")
			end),
		} })
	end
	function SplashScreenManager:notifyStateChange()
		if self.onStateChange then
			self.onStateChange(self:getState())
		end
	end
	function SplashScreenManager:delay(seconds)
		return TS.Promise.new(function(resolve)
			task.delay(seconds, resolve)
		end)
	end
end
return {
	SplashScreenManager = SplashScreenManager,
}
