import * as React from "@rbxts/react";
export interface BaseFrameProps {
    children?: React.ReactNode;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    backgroundColor?: string;
    backgroundTransparency?: number;
    padding?: number;
    zIndex?: number;
    autoSize?: Enum.AutomaticSize;
    fillDirection?: "auto" | "manual";
    responsive?: boolean;
    minSize?: UDim2;
    maxSize?: UDim2;
    responsiveMargin?: boolean;
}
