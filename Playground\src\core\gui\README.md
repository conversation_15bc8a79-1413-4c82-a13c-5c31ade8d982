# Core GUI Framework - Responsive Design Guide

This guide explains how to use the updated Core GUI framework with responsive design capabilities. The framework automatically adapts to different screen sizes and device types while preventing content overflow and positioning issues.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Responsive Manager](#responsive-manager)
3. [Dynamic Positioning](#dynamic-positioning)
4. [Preventing Overflow](#preventing-overflow)
5. [Responsive Design Tips](#responsive-design-tips)
6. [Component Examples](#component-examples)
7. [Debug System Usage](#debug-system-usage)
8. [Best Practices](#best-practices)

## Quick Start

```tsx
import { ResponsiveManager, ContainerFrame, Button } from "@roboxgames/core";

// Get responsive manager instance
const responsiveManager = ResponsiveManager.getInstance();

// Create responsive components
<ContainerFrame responsive={true} responsiveMargin={true}>
	<Button text="Responsive Button" responsive={true} />
</ContainerFrame>;
```

## Responsive Manager

The `ResponsiveManager` is a singleton class that handles all responsive calculations and device detection.

### Key Features

- **Screen Size Detection**: Automatically detects current viewport size
- **Device Type Classification**: Mobile, tablet, or desktop detection
- **Safe Area Insets**: Accounts for mobile control areas
- **Viewport Change Listeners**: Real-time responsiveness to screen changes

### Basic Usage

```tsx
const responsiveManager = ResponsiveManager.getInstance();

// Get current screen information
const screenSize = responsiveManager.getScreenSize();
const deviceType = responsiveManager.getDeviceType();
const isMobile = responsiveManager.isMobile();

// Listen for screen size changes
const unsubscribe = responsiveManager.onScreenSizeChange((newSize) => {
	print(`Screen changed to: ${newSize.width}x${newSize.height}`);
});
```

## Dynamic Positioning

### Using getRelativePosition()

Convert pixel positions to responsive scale values:

```tsx
const responsiveManager = ResponsiveManager.getInstance();

// Convert 100px from left, 50px from top to scale values
const position = responsiveManager.getRelativePosition({ x: 100, y: 50 });

<ContainerFrame position={position} anchorPoint={new Vector2(0, 0)} />;
```

### Safe Area Aware Positioning

Account for mobile control areas:

```tsx
const safeAreaInsets = responsiveManager.getSafeAreaInsets();
const bottomMargin = 20 + safeAreaInsets.bottom;

<ContainerFrame position={new UDim2(0.5, 0, 1, -bottomMargin)} anchorPoint={new Vector2(0.5, 1)} />;
```

### Responsive Margins

Use device-appropriate margins:

```tsx
const margin = responsiveManager.getResponsiveMargin(16);
// Returns: 12.8px on mobile, 16px on tablet, 19.2px on desktop

<ContainerFrame position={new UDim2(0, margin, 0, margin)} />;
```

## Preventing Overflow

### AutomaticSize for Content

Always use AutomaticSize for dynamic content:

```tsx
<VerticalFrame fitContent={true}>
	<Label text="Dynamic text content" autoSize={true} />
	<Button text="Auto-sized button" autoSize={true} />
</VerticalFrame>
```

### ScrollingFrame with AutomaticCanvasSize

For scrollable content:

```tsx
<ScrollingFrame
	size={new UDim2(1, 0, 0, 300)}
	automaticCanvasSize={Enum.AutomaticSize.Y}
	scrollingDirection={Enum.ScrollingDirection.Y}
>
	<VerticalFrame fitContent={true}>{/* Content that may exceed frame height */}</VerticalFrame>
</ScrollingFrame>
```

### Size Constraints

Prevent components from becoming too large or small:

```tsx
<ContainerFrame responsive={true} minSize={new UDim2(0, 200, 0, 100)} maxSize={new UDim2(0, 600, 0, 400)}>
	{/* Content with size limits */}
</ContainerFrame>
```

## Responsive Design Tips

### 1. Always Use Scale for Main Positioning

```tsx
// ✅ Good - Uses scale values
<ContainerFrame position={new UDim2(0.5, 0, 0.1, 0)} />

// ❌ Bad - Uses fixed pixel values
<ContainerFrame position={new UDim2(0, 400, 0, 100)} />
```

### 2. Use Offset for Fine-Tuning

```tsx
// ✅ Good - Scale for main position, offset for fine-tuning
<ContainerFrame position={new UDim2(0.5, -10, 1, -50)} />
```

### 3. Enable Responsive Properties

```tsx
<VerticalFrame
  responsive={true}
  responsiveMargin={true}
  spacing={8} // Will be adjusted based on device type
>
```

### 4. Listen for Screen Changes

```tsx
React.useEffect(() => {
	const unsubscribe = responsiveManager.onScreenSizeChange((screenSize) => {
		// Update component state based on new screen size
		setComponentSize(calculateNewSize(screenSize));
	});

	return unsubscribe; // Cleanup on unmount
}, []);
```

## Component Examples

### Responsive Bottom-Left Panel

```tsx
function ResponsiveBottomPanel() {
	const responsiveManager = ResponsiveManager.getInstance();
	const safeAreaInsets = responsiveManager.getSafeAreaInsets();

	const containerWidth = responsiveManager.isMobile() ? 100 : 120;
	const marginLeft = responsiveManager.getResponsiveMargin(16);
	const marginBottom = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.bottom;

	return (
		<VerticalFrame
			size={new UDim2(0, containerWidth, 0, 200)}
			position={new UDim2(0, marginLeft, 1, -marginBottom)}
			anchorPoint={new Vector2(0, 1)}
			responsive={true}
			responsiveMargin={true}
		>
			<Button text="Action 1" responsive={true} />
			<Button text="Action 2" responsive={true} />
		</VerticalFrame>
	);
}
```

### Responsive Action Bar

```tsx
function ResponsiveActionBar() {
	const responsiveManager = ResponsiveManager.getInstance();
	const safeAreaInsets = responsiveManager.getSafeAreaInsets();

	const containerWidth = responsiveManager.isMobile() ? 320 : 400;
	const bottomMargin = responsiveManager.getResponsiveMargin(40) + safeAreaInsets.bottom;

	return (
		<ContainerFrame
			size={new UDim2(0, containerWidth, 0, 80)}
			position={new UDim2(0.5, 0, 1, -bottomMargin)}
			anchorPoint={new Vector2(0.5, 0)}
			responsive={true}
			minSize={new UDim2(0, 280, 0, 60)}
			maxSize={new UDim2(0, 500, 0, 100)}
		>
			{/* Action bar content */}
		</ContainerFrame>
	);
}
```

### Responsive Grid

```tsx
<Grid rows={3} cols={4} responsive={true} maxWidth={600} cellType="button">
	{/* Grid items */}
</Grid>
```

## Debug System Usage

The Core framework includes a comprehensive debug system with responsive design capabilities. The debug panels automatically adapt to different screen sizes and prevent text overflow issues.

### Quick Setup

```tsx
import { initializeDebugSystem } from "@roboxgames/core/debug";

// Initialize debug system in your main client script
initializeDebugSystem();

// Debug system will be available with F3 toggle or debug panel controls
```

### Debug Panel Features

The debug system includes three main panels that stack responsively:

1. **Performance Monitor** - FPS, frame time, memory usage, network stats
2. **AI Debug Info** - AI agent states, behaviors, and debugging information
3. **Player Debug Info** - Player positions, health, equipment, and relationships

### Responsive Debug Panel Design

All debug panels now use the responsive framework to ensure proper display across devices:

```typescript
// Example: Creating a responsive debug panel
const responsiveManager = ResponsiveManager.getInstance();
const safeAreaInsets = responsiveManager.getSafeAreaInsets();

// Calculate responsive position and size
const panelWidth = responsiveManager.isMobile() ? 280 : 340;
const panelHeight = responsiveManager.isMobile() ? 150 : 180;
const rightMargin = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.right;
const topMargin = responsiveManager.getResponsiveMargin(10) + safeAreaInsets.top;

// Position in top-right corner with proper spacing
const position = new UDim2(1, -rightMargin - panelWidth, 0, topMargin);
const size = new UDim2(0, panelWidth, 0, panelHeight);
```

### Debug Panel Best Practices

#### ✅ Responsive Text Handling

```typescript
// Create debug labels with proper overflow prevention
const label = new Instance("TextLabel");
label.Size = new UDim2(1, 0, 0, 0); // Full width, auto height
label.AutomaticSize = Enum.AutomaticSize.Y; // Auto-resize height based on text
label.TextWrapped = true; // Enable text wrapping to prevent overflow
label.TextSize = 14; // Fixed size for readability
label.Font = Enum.Font.RobotoMono; // Monospace font for debug data
```

#### ✅ Proper Frame Layout

```typescript
// Create frames with automatic sizing and overflow prevention
const frame = new Instance("Frame");
frame.AutomaticSize = Enum.AutomaticSize.Y; // Auto-resize height based on content
frame.ClipsDescendants = true; // Prevent text overflow

// Add padding using UIPadding instead of manual positioning
const padding = new Instance("UIPadding");
padding.PaddingTop = new UDim(0, 12);
padding.PaddingBottom = new UDim(0, 12);
padding.PaddingLeft = new UDim(0, 16);
padding.PaddingRight = new UDim(0, 16);
padding.Parent = frame;

// Add UIListLayout for better text organization
const listLayout = new Instance("UIListLayout");
listLayout.SortOrder = Enum.SortOrder.LayoutOrder;
listLayout.FillDirection = Enum.FillDirection.Vertical;
listLayout.Padding = new UDim(0, 2); // Small spacing between lines
listLayout.Parent = frame;
```

#### ✅ Screen Resize Handling

```typescript
// Listen for screen size changes and recreate debug panels
const responsiveManager = ResponsiveManager.getInstance();

const unsubscribe = responsiveManager.onScreenSizeChange(() => {
	if (debugEnabled) {
		// Recreate debug panels with new responsive positioning
		recreateDebugPanels();
	}
});

// Don't forget to cleanup
// unsubscribe() when debug system is destroyed
```

### Custom Debug Panels

You can create your own debug panels using the same responsive principles:

```typescript
import { DebugRenderer } from "@roboxgames/core/debug";
import { ResponsiveManager } from "@roboxgames/core/gui/layout/ResponsiveManager";

class CustomDebugPanel {
	private renderer: DebugRenderer;
	private debugLabel?: TextLabel;

	constructor(renderer: DebugRenderer) {
		this.renderer = renderer;
		this.setupGUI();
	}

	private setupGUI(): void {
		const responsiveManager = ResponsiveManager.getInstance();
		const safeAreaInsets = responsiveManager.getSafeAreaInsets();

		// Calculate responsive position and size
		const panelWidth = responsiveManager.isMobile() ? 260 : 320;
		const panelHeight = responsiveManager.isMobile() ? 120 : 140;
		const leftMargin = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.left;
		const topMargin = responsiveManager.getResponsiveMargin(10) + safeAreaInsets.top;

		this.debugLabel = this.renderer.createGUIElement(
			"CustomDebug",
			new UDim2(0, leftMargin, 0, topMargin), // Top-left corner
			new UDim2(0, panelWidth, 0, panelHeight),
			"Custom Debug Info",
		);
	}

	public update(): void {
		if (!this.debugLabel) return;

		const info = ["=== CUSTOM DEBUG ===", `Custom Data: ${this.getCustomData()}`, `Timestamp: ${tick()}`];

		this.debugLabel.Text = info.join("\n");
	}

	private getCustomData(): string {
		// Your custom debug data logic here
		return "Sample Data";
	}
}
```

### Debug System Migration

If you have existing debug panels with fixed positioning, here's how to migrate:

#### Before (Fixed Positioning)

```typescript
// ❌ Old way - Fixed positioning that breaks on different screen sizes
this.debugLabel = this.renderer.createGUIElement(
	"Debug",
	UDim2.fromScale(1, 0).sub(UDim2.fromOffset(360, -200)), // Fixed offset
	UDim2.fromOffset(340, 160), // Fixed size
	"Debug Info",
);
```

#### After (Responsive Positioning)

```typescript
// ✅ New way - Responsive positioning that works on all devices
const responsiveManager = ResponsiveManager.getInstance();
const safeAreaInsets = responsiveManager.getSafeAreaInsets();

const panelWidth = responsiveManager.isMobile() ? 280 : 340;
const panelHeight = responsiveManager.isMobile() ? 140 : 160;
const rightMargin = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.right;
const topMargin = responsiveManager.getResponsiveMargin(10) + safeAreaInsets.top;

this.debugLabel = this.renderer.createGUIElement(
	"Debug",
	new UDim2(1, -rightMargin - panelWidth, 0, topMargin), // Responsive positioning
	new UDim2(0, panelWidth, 0, panelHeight), // Responsive sizing
	"Debug Info",
);
```

### Debug Panel Stacking

The debug system automatically stacks panels to prevent overlaps:

```typescript
// Performance Monitor: Top-right corner
const performanceY = topMargin;

// AI Debugger: Below Performance Monitor
const aiDebuggerY = topMargin + performanceMonitorHeight + spacing;

// Player Debugger: Below AI Debugger
const playerDebuggerY = topMargin + performanceMonitorHeight + spacing + aiDebuggerHeight + spacing;
```

This ensures all debug panels are visible and properly spaced on any screen size!

### ✅ Do's

1. **Use ResponsiveManager** for all positioning calculations
2. **Enable responsive props** on components that need adaptation
3. **Use AutomaticSize** for dynamic content
4. **Account for safe areas** on mobile devices
5. **Test on multiple screen sizes** during development
6. **Use scale values** for main positioning
7. **Add size constraints** to prevent extreme scaling

### ❌ Don'ts

1. **Don't use fixed pixel positions** for main layout
2. **Don't ignore safe area insets** on mobile
3. **Don't forget to cleanup** screen change listeners
4. **Don't hardcode device-specific values**
5. **Don't use absolute positioning** without responsive calculations
6. **Don't forget ClipsDescendants** for overflow prevention

### Testing Your Responsive Design

Use the built-in verification tools:

```tsx
import { runResponsiveTests } from "@roboxgames/core/gui/test/ResponsiveVerification";

// Run automated tests
runResponsiveTests();

// Or use the interactive test panel
<ResponsiveTest isOpen={true} onClose={() => setTestOpen(false)} />;
```

## Migration from Fixed Positioning

If you have existing components with fixed positioning, here's how to migrate:

### Before (Fixed)

```tsx
<ContainerFrame size={new UDim2(0, 400, 0, 200)} position={new UDim2(0, 50, 1, -250)} />
```

### After (Responsive)

```tsx
const responsiveManager = ResponsiveManager.getInstance();
const safeAreaInsets = responsiveManager.getSafeAreaInsets();
const width = responsiveManager.isMobile() ? 320 : 400;
const margin = responsiveManager.getResponsiveMargin(50);
const bottomOffset = responsiveManager.getResponsiveMargin(50) + safeAreaInsets.bottom;

<ContainerFrame
	size={new UDim2(0, width, 0, 200)}
	position={new UDim2(0, margin, 1, -bottomOffset)}
	responsive={true}
	responsiveMargin={true}
	minSize={new UDim2(0, 280, 0, 150)}
	maxSize={new UDim2(0, 500, 0, 300)}
/>;
```

This ensures your GUI works perfectly across all devices and screen sizes!
