-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function ContainerFrame(props)
	local _condition = props.backgroundColor
	if _condition == nil then
		_condition = COLORS.bg.base
	end
	local backgroundColor = _condition
	local _condition_1 = props.backgroundTransparency
	if _condition_1 == nil then
		_condition_1 = 0
	end
	local backgroundTransparency = _condition_1
	local _condition_2 = props.cornerRadius
	if _condition_2 == nil then
		_condition_2 = BORDER_RADIUS.md
	end
	local cornerRadius = _condition_2
	local _condition_3 = props.borderColor
	if _condition_3 == nil then
		_condition_3 = COLORS.border.l2
	end
	local borderColor = _condition_3
	local _condition_4 = props.borderThickness
	if _condition_4 == nil then
		_condition_4 = 1
	end
	local borderThickness = _condition_4
	local _condition_5 = props.borderTransparency
	if _condition_5 == nil then
		_condition_5 = 0
	end
	local borderTransparency = _condition_5
	-- Responsive manager for dynamic calculations
	local responsiveManager = ResponsiveManager:getInstance()
	-- Calculate responsive padding
	local _condition_6 = props.padding
	if _condition_6 == nil then
		_condition_6 = SIZES.padding
	end
	local basePadding = _condition_6
	local padding = if props.responsiveMargin then responsiveManager:getResponsiveMargin(basePadding) else basePadding
	-- Smart sizing: if fitContent is true or no size specified, use AutomaticSize
	local _condition_7 = props.fitContent
	if _condition_7 == nil then
		_condition_7 = props.size == nil
	end
	local fitContent = _condition_7
	local size = props.size or (if fitContent then UDim2.new(1, 0, 0, 0) else UDim2.new(1, 0, 1, 0))
	local autoSize = props.autoSize or (if fitContent then Enum.AutomaticSize.XY else Enum.AutomaticSize.None)
	return React.createElement("frame", {
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		BackgroundTransparency = backgroundTransparency,
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		BorderSizePixel = 0,
		ZIndex = props.zIndex,
		AutomaticSize = autoSize,
		ClipsDescendants = true,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, cornerRadius),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(borderColor),
		Thickness = borderThickness,
		Transparency = borderTransparency,
	}), React.createElement("uipadding", {
		PaddingTop = UDim.new(0, padding),
		PaddingBottom = UDim.new(0, padding),
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	}), (props.minSize or props.maxSize) and (React.createElement("uisizeconstraint", {
		MinSize = if props.minSize then Vector2.new(props.minSize.X.Scale * 1920 + props.minSize.X.Offset, props.minSize.Y.Scale * 1080 + props.minSize.Y.Offset) else nil,
		MaxSize = if props.maxSize then Vector2.new(props.maxSize.X.Scale * 1920 + props.maxSize.X.Offset, props.maxSize.Y.Scale * 1080 + props.maxSize.Y.Offset) else nil,
	})), props.children)
end
return {
	ContainerFrame = ContainerFrame,
}
