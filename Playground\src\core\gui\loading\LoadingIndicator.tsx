import * as React from "@rbxts/react";
import { RunService } from "@rbxts/services";
import { COLORS, SIZES, TYPOGRAPHY } from "../../design";

interface LoadingSpinnerProps {
	size?: number;
	color?: string;
	speed?: number; // Rotation speed in seconds per full rotation
}

export const LoadingSpinner = React.memo((props: LoadingSpinnerProps): React.ReactElement => {
	const size = props.size ?? 20;
	const color = props.color ?? COLORS.primary;
	const speed = props.speed ?? 1.5;

	// Memoize the spinner characters for smooth animation
	const spinnerChars = React.useMemo(() => ["◐", "◓", "◑", "◒"], []);
	const [currentIndex, setCurrentIndex] = React.useState(0);

	// Animated rotation effect
	React.useEffect(() => {
		let timeoutId: any;
		const animate = () => {
			setCurrentIndex((prev) => (prev + 1) % spinnerChars.size());
			timeoutId = task.delay((speed * 1000) / spinnerChars.size() / 1000, animate);
		};
		animate();

		return () => {
			if (timeoutId) {
				task.cancel(timeoutId);
			}
		};
	}, [speed, spinnerChars]);

	return (
		<textlabel
			Text={spinnerChars[currentIndex]}
			Size={new UDim2(0, size, 0, size)}
			BackgroundTransparency={1}
			TextColor3={Color3.fromHex(color)}
			TextSize={size}
			Font={TYPOGRAPHY.font}
			TextXAlignment={Enum.TextXAlignment.Center}
			TextYAlignment={Enum.TextYAlignment.Center}
		/>
	);
});

interface LoadingIndicatorProps {
	text?: string;
	size?: "sm" | "md" | "lg";
	variant?: "spinner" | "dots" | "pulse";
	color?: string;
}

export const LoadingIndicator = React.memo((props: LoadingIndicatorProps): React.ReactElement => {
	const sizeMap = {
		sm: { spinner: 16, text: SIZES.fontSize - 2 },
		md: { spinner: 20, text: SIZES.fontSize },
		lg: { spinner: 24, text: SIZES.fontSize + 2 },
	};

	const currentSize = sizeMap[props.size ?? "md"];
	const variant = props.variant ?? "spinner";
	const color = props.color ?? COLORS.primary;

	// Memoize loading content based on variant
	const loadingContent = React.useMemo(() => {
		switch (variant) {
			case "dots":
				return <DotsLoader color={color} size={currentSize.spinner} />;
			case "pulse":
				return <PulseLoader color={color} size={currentSize.spinner} />;
			default:
				return <LoadingSpinner color={color} size={currentSize.spinner} />;
		}
	}, [variant, color, currentSize.spinner]);

	if (!props.text) {
		return loadingContent;
	}

	return (
		<frame
			Size={new UDim2(0, 0, 0, currentSize.spinner + (props.text ? currentSize.text + 10 : 0))}
			BackgroundTransparency={1}
			AutomaticSize={Enum.AutomaticSize.X}
		>
			<uilistlayout
				FillDirection={Enum.FillDirection.Horizontal}
				SortOrder={Enum.SortOrder.LayoutOrder}
				VerticalAlignment={Enum.VerticalAlignment.Center}
				Padding={new UDim(0, 8)}
			/>

			{loadingContent}

			{props.text && (
				<textlabel
					Text={props.text}
					Size={new UDim2(0, 0, 0, currentSize.text)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex(COLORS.text.secondary)}
					TextSize={currentSize.text}
					Font={TYPOGRAPHY.font}
					TextXAlignment={Enum.TextXAlignment.Left}
					TextYAlignment={Enum.TextYAlignment.Center}
					AutomaticSize={Enum.AutomaticSize.X}
					LayoutOrder={2}
				/>
			)}
		</frame>
	);
});

// Dots animation loader
const DotsLoader = React.memo((props: { color: string; size: number }): React.ReactElement => {
	const [activeIndex, setActiveIndex] = React.useState(0);

	React.useEffect(() => {
		let timeoutId: any;
		const animate = () => {
			setActiveIndex((prev) => (prev + 1) % 3);
			timeoutId = task.delay(0.4, animate);
		};
		animate();
		
		return () => {
			if (timeoutId) {
				task.cancel(timeoutId);
			}
		};
	}, []);

	const dotSize = math.floor(props.size / 3);

	return (
		<frame Size={new UDim2(0, props.size, 0, dotSize)} BackgroundTransparency={1}>
			<uilistlayout
				FillDirection={Enum.FillDirection.Horizontal}
				SortOrder={Enum.SortOrder.LayoutOrder}
				HorizontalAlignment={Enum.HorizontalAlignment.Center}
				VerticalAlignment={Enum.VerticalAlignment.Center}
				Padding={new UDim(0, 2)}
			/>

			{[0, 1, 2].map((index) => (
				<frame
					key={index}
					Size={new UDim2(0, dotSize, 0, dotSize)}
					BackgroundColor3={Color3.fromHex(props.color)}
					BackgroundTransparency={activeIndex === index ? 0 : 0.6}
					BorderSizePixel={0}
					LayoutOrder={index}
				>
					<uicorner CornerRadius={new UDim(0.5, 0)} />
				</frame>
			))}
		</frame>
	);
});

// Pulse animation loader
const PulseLoader = React.memo((props: { color: string; size: number }): React.ReactElement => {
	const [scale, setScale] = React.useState(1);

	React.useEffect(() => {
		const tween = {
			start: 0.7,
			end: 1.3,
			duration: 1000,
		};

		let startTime = tick();
		const connection = RunService.Heartbeat.Connect(() => {
			const elapsed = tick() - startTime;
			const progress = (elapsed % (tween.duration / 1000)) / (tween.duration / 1000);
			const pingPong = progress <= 0.5 ? progress * 2 : (1 - progress) * 2;
			setScale(tween.start + (tween.end - tween.start) * pingPong);
		});

		return () => connection.Disconnect();
	}, []);

	return (
		<frame
			Size={new UDim2(scale, 0, scale, 0)}
			Position={new UDim2(0.5, 0, 0.5, 0)}
			AnchorPoint={new Vector2(0.5, 0.5)}
			BackgroundColor3={Color3.fromHex(props.color)}
			BackgroundTransparency={0.3}
			BorderSizePixel={0}
		>
			<uicorner CornerRadius={new UDim(0.5, 0)} />
		</frame>
	);
});