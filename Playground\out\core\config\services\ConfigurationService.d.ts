import { IConfigurationService } from "../interfaces/IConfigurationService";
import { BaseService } from "../../foundation/BaseService";
import { ConfigKey } from "../../foundation/types/BrandedTypes";
import { Result } from "../../foundation/types/Result";
import { Error } from "../../foundation/types/RobloxError";
import { ConfigurationValidationRule } from "../types/ConfigurationTypes";
export declare class ConfigurationService extends BaseService implements IConfigurationService {
    private config;
    private validationRules;
    constructor();
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    get<T>(key: ConfigKey, defaultValue?: T): Result<T, Error>;
    set<T>(key: ConfigKey, value: T): Result<void, Error>;
    has(key: ConfigKey): boolean;
    remove(key: Config<PERSON><PERSON>): Result<void, Error>;
    getAll(): Record<string, unknown>;
    clear(): void;
    addValidationRule<T>(key: Config<PERSON><PERSON>, rule: ConfigurationValidationRule<T>): void;
    private validateValue;
    private setupDefaultConfiguration;
    private setConfig;
    private setReadOnlyConfig;
}
