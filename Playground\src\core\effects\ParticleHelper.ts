import { EffectPartBuilder } from "./EffectPartBuilder";
import { EffectTweenBuilder } from "./EffectTweenBuilder";

export function createParticleExplosion(
	pos: Vector3,
	numParticles: number,
	color: Color3,
	velocityRange: [number, number],
	sizeRange: [number, number],
): void {
	for (let i = 0; i < numParticles; i++) {
		const particle = EffectPartBuilder.create()
			.shape(Enum.PartType.Ball)
			.size(
				new Vector3(
					math.random(sizeRange[0], sizeRange[1]),
					math.random(sizeRange[0], sizeRange[1]),
					math.random(sizeRange[0], sizeRange[1]),
				),
			)
			.color(color)
			.material(Enum.Material.Neon)
			.transparency(0.1)
			.position(pos)
			.spawn();

		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(math.huge, math.huge, math.huge);
		bodyVelocity.Velocity = new Vector3(math.random(-1, 1), math.random(-1, 1), math.random(-1, 1)).Unit.mul(
			math.random(velocityRange[0], velocityRange[1]),
		);
		bodyVelocity.Parent = particle;

		const bodyAngularVelocity = new Instance("BodyAngularVelocity");
		bodyAngularVelocity.MaxTorque = new Vector3(1000, 1000, 1000);
		bodyAngularVelocity.AngularVelocity = new Vector3(
			math.random(-15, 15),
			math.random(-15, 15),
			math.random(-15, 15),
		);
		bodyAngularVelocity.Parent = particle;

		task.delay(0.5, () => {
			EffectTweenBuilder.for(particle)
				.fade(1)
				.expand(new Vector3(0.1, 0.1, 0.1))
				.duration(2)
				.onComplete(() => particle.Destroy())
				.play();
		});

		task.delay(1, () => {
			bodyVelocity.Destroy();
			bodyAngularVelocity.Destroy();
		});
	}
}

export function createParticleStorm(
	pos: Vector3,
	numParticles: number,
	color: Color3,
	floatRange: [number, number],
): void {
	for (let i = 0; i < numParticles; i++) {
		const particle = EffectPartBuilder.create()
			.shape(Enum.PartType.Ball)
			.size(new Vector3(0.5, 0.5, 0.5))
			.color(color)
			.material(Enum.Material.Neon)
			.transparency(0.3)
			.position(pos.add(new Vector3(math.random(-15, 15), math.random(-5, 10), math.random(-15, 15))))
			.spawn();

		EffectTweenBuilder.for(particle)
			.move(particle.Position.add(new Vector3(0, math.random(floatRange[0], floatRange[1]), 0)))
			.duration(3)
			.easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			.play();

		task.delay(4, () => {
			EffectTweenBuilder.for(particle)
				.fade(1)
				.duration(1)
				.onComplete(() => particle.Destroy())
				.play();
		});
	}
}
