import { RemoteDataStoreResult } from "../base/DataStoreBase";

// Experience-related interfaces
export interface AddExperienceResult extends RemoteDataStoreResult {
	data?: {
		level: number;
		experience: number;
		leveledUp: boolean;
	};
}

export interface AddExperienceRequestData {
	amount: number;
}

// Achievement-related interfaces
export interface UnlockAchievementResult extends RemoteDataStoreResult {
	data?: boolean;
}

export interface UnlockAchievementRequestData {
	achievementId: string;
}
