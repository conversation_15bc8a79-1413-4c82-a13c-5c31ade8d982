-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitFor<PERSON>hild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local exports = {}
-- Base physics zone interfaces
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "interfaces", "base", "PhysicsZoneBase") or {} do
	exports[_k] = _v
end
-- Specific zone type interfaces
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "interfaces", "zones", "GravityZoneOptions") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "interfaces", "zones", "ForceZoneOptions") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "interfaces", "zones", "BarrierZoneOptions") or {} do
	exports[_k] = _v
end
return exports
