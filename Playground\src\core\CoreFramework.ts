import { ServiceContainer } from "./foundation/ServiceContainer";
import { Result } from "./foundation/types/Result";
import { ServiceLifecycle } from "./foundation/enums/ServiceLifecycle";
import { IService } from "./foundation/interfaces/IService";
import { NetworkService } from "./networking/NetworkService";
import { NetworkValidationService } from "./networking/NetworkValidationService";
import { StateManager } from "./state/StateManager";
import { BrandedTypes } from "./foundation/types/BrandedTypes";
import { Error, createError } from "./foundation/types/RobloxError";

export class CoreFramework {
	private static instance: CoreFramework;
	private container: ServiceContainer;
	private isInitialized = false;

	private constructor() {
		this.container = ServiceContainer.getInstance();
	}

	public static getInstance(): CoreFramework {
		if (!CoreFramework.instance) {
			CoreFramework.instance = new CoreFramework();
		}
		return CoreFramework.instance;
	}

	public async initialize(): Promise<Result<void, Error>> {
		if (this.isInitialized) {
			return Result.ok(undefined);
		}

		try {
			// Register core services
			const registrationResult = this.registerCoreServices();
			if (registrationResult.isError()) {
				return registrationResult;
			}

			// Initialize all services
			const initResult = await this.container.initialize();
			if (initResult.isError()) {
				return Result.err(createError(`Failed to initialize Core Framework: ${initResult.getError().message}`));
			}

			this.isInitialized = true;
			print("🚀 Core Framework initialized successfully!");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Core Framework initialization failed: ${error}`));
		}
	}

	public async shutdown(): Promise<Result<void, Error>> {
		if (!this.isInitialized) {
			return Result.ok(undefined);
		}

		const shutdownResult = await this.container.shutdown();
		if (shutdownResult.isError()) {
			return Result.err(createError(`Failed to shutdown Core Framework: ${shutdownResult.getError().message}`));
		}

		this.isInitialized = false;
		print("🛑 Core Framework shutdown complete");
		return Result.ok(undefined);
	}

	public getService<T extends IService>(serviceName: string): Result<T, Error> {
		if (!this.isInitialized) {
			return Result.err(createError("Core Framework is not initialized"));
		}

		const serviceResult = this.container.resolve<T>(serviceName);
		if (serviceResult.isError()) {
			return Result.err(
				createError(`Failed to resolve service '${serviceName}': ${serviceResult.getError().message}`),
			);
		}

		return Result.ok(serviceResult.getValue());
	}

	public getNetworkService(): Result<NetworkService, Error> {
		return this.getService<NetworkService>("NetworkService");
	}

	public getValidationService(): Result<NetworkValidationService, Error> {
		return this.getService<NetworkValidationService>("NetworkValidationService");
	}

	public createStateManager<TState>(initialState: TState, name?: string): StateManager<TState> {
		return new StateManager(initialState, name);
	}

	private registerCoreServices(): Result<void, Error> {
		// Register Network Validation Service
		const validationResult = this.container.register(
			"NetworkValidationService",
			() => new NetworkValidationService(),
			ServiceLifecycle.Singleton,
		);
		if (validationResult.isError()) {
			return validationResult.mapError((err) => createError(err.message));
		}

		// Register Network Service with validation dependency
		const networkResult = this.container.register(
			"NetworkService",
			() => {
				const networkService = new NetworkService();
				const validationService = this.container.resolve<NetworkValidationService>("NetworkValidationService");
				if (validationService.isOk()) {
					networkService.setValidationService(validationService.getValue());
				}
				return networkService;
			},
			ServiceLifecycle.Singleton,
			["NetworkValidationService"],
		);
		if (networkResult.isError()) {
			return networkResult.mapError((err) => createError(err.message));
		}

		return Result.ok(undefined);
	}
}

// Global initialization function for easy setup
export async function initializeCoreFramework(): Promise<Result<void, Error>> {
	const core = CoreFramework.getInstance();
	return await core.initialize();
}

// Global shutdown function
export async function shutdownCoreFramework(): Promise<Result<void, Error>> {
	const core = CoreFramework.getInstance();
	return await core.shutdown();
}

// Utility functions for common operations
export const Core = {
	getInstance: () => CoreFramework.getInstance(),
	getNetworkService: () => CoreFramework.getInstance().getNetworkService(),
	getValidationService: () => CoreFramework.getInstance().getValidationService(),
	createStateManager: <TState>(initialState: TState, name?: string) =>
		CoreFramework.getInstance().createStateManager(initialState, name),

	// Branded type helpers
	playerId: BrandedTypes.playerId,
	entityId: BrandedTypes.entityId,
	serviceName: BrandedTypes.serviceName,
	eventName: BrandedTypes.eventName,
	componentId: BrandedTypes.componentId,
	assetId: BrandedTypes.assetId,
	configKey: BrandedTypes.configKey,
};
