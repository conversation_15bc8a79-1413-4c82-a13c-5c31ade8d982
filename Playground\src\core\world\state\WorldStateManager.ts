import { WeatherController } from "./WeatherController";
import { TimeController } from "./TimeController";
import { GravityController } from "./GravityController";
import { WorldStateOptions } from "./interfaces/WorldStateUpdate";
import { WorldStateEvent } from "./interfaces/WorldStateEvent";

/**
 * WorldStateManager - Central manager for all world environmental states
 * Provides a unified interface for weather, time, gravity, and atmosphere management
 */
export class WorldStateManager {
	private static instance: WorldStateManager;
	private weatherController: WeatherController;
	private timeController: TimeController;
	private gravityController: GravityController;
	private eventListeners: Array<(event: WorldStateEvent) => void> = [];

	private constructor() {
		this.weatherController = WeatherController.getInstance();
		this.timeController = TimeController.getInstance();
		this.gravityController = GravityController.getInstance();
	}

	public static getInstance(): WorldStateManager {
		if (!WorldStateManager.instance) {
			WorldStateManager.instance = new WorldStateManager();
		}
		return WorldStateManager.instance;
	}

	/**
	 * Apply multiple world state changes at once
	 */
	public applyWorldState(options: WorldStateOptions, playerId?: number): void {
		const previousState = this.getCurrentState();

		// Apply weather changes
		if (options.weather) {
			this.weatherController.setWeather(options.weather);
			this.emitEvent("weather_change", previousState, options, playerId);
		}

		// Apply time changes
		if (options.time) {
			this.timeController.setTime(options.time);
			this.emitEvent("time_change", previousState, options, playerId);
		}

		// Apply gravity changes
		if (options.gravity) {
			this.gravityController.setGravity(options.gravity);
			this.emitEvent("gravity_change", previousState, options, playerId);
		}

		print(`🌍 World state updated by player ${playerId || "system"}`);
	}

	/**
	 * Get current state of all world systems
	 */
	public getCurrentState(): WorldStateOptions {
		return {
			weather: {
				type: this.weatherController.getCurrentWeather(),
				intensity: 0.5, // Default intensity
			},
			time: {
				timeOfDay: this.timeController.getCurrentTimeOfDay(),
				clockTime: this.timeController.getCurrentClockTime(),
			},
			gravity: {
				level: this.gravityController.getCurrentGravityLevel(),
				customValue: this.gravityController.getCurrentGravityMultiplier(),
			},
		};
	}

	// Individual controller access methods
	public getWeatherController(): WeatherController {
		return this.weatherController;
	}

	public getTimeController(): TimeController {
		return this.timeController;
	}

	public getGravityController(): GravityController {
		return this.gravityController;
	}

	/**
	 * Quick preset methods for common world states
	 */
	public setStormyNight(): void {
		this.applyWorldState({
			weather: { type: "thunderstorm", intensity: 0.8 },
			time: { timeOfDay: "night", transitionDuration: 3 },
			gravity: { level: "normal" },
		});
	}

	public setSunnyDay(): void {
		this.applyWorldState({
			weather: { type: "clear" },
			time: { timeOfDay: "noon", transitionDuration: 3 },
			gravity: { level: "normal" },
		});
	}

	public setWinterStorm(): void {
		this.applyWorldState({
			weather: { type: "blizzard", intensity: 0.9 },
			time: { timeOfDay: "dusk", transitionDuration: 2 },
			gravity: { level: "normal" },
		});
	}

	public setLowGravityDawn(): void {
		this.applyWorldState({
			weather: { type: "clear" },
			time: { timeOfDay: "dawn", transitionDuration: 4 },
			gravity: { level: "low" },
		});
	}

	public setSpaceMode(): void {
		this.applyWorldState({
			weather: { type: "clear" },
			time: { timeOfDay: "night", transitionDuration: 2 },
			gravity: { level: "zero" },
		});
	}

	/**
	 * Event system for listening to world state changes
	 */
	public addEventListener(listener: (event: WorldStateEvent) => void): void {
		this.eventListeners.push(listener);
	}

	public removeEventListener(listener: (event: WorldStateEvent) => void): void {
		const index = this.eventListeners.indexOf(listener);
		if (index !== -1) {
			this.eventListeners.remove(index);
		}
	}

	private emitEvent(
		eventType: WorldStateEvent["type"],
		previousState: WorldStateOptions,
		newState: WorldStateOptions,
		playerId?: number,
	): void {
		const event: WorldStateEvent = {
			type: eventType,
			previousState,
			newState,
			timestamp: tick(),
			playerId,
		};

		for (const listener of this.eventListeners) {
			task.spawn(() => listener(event));
		}
	}

	/**
	 * Reset all world systems to default state
	 */
	public resetToDefaults(): void {
		this.applyWorldState({
			weather: { type: "clear" },
			time: { timeOfDay: "noon", transitionDuration: 1 },
			gravity: { level: "normal" },
		});

		print("🌍 World state reset to defaults");
	}

	/**
	 * Cleanup all world state systems
	 */
	public cleanup(): void {
		this.weatherController.cleanup();
		this.timeController.cleanup();
		this.gravityController.cleanup();
		this.eventListeners = [];

		print("🌍 World state manager cleaned up");
	}

	/**
	 * Initialize the world state manager
	 */
	public static initialize(): WorldStateManager {
		const manager = WorldStateManager.getInstance();
		print("🌍 World State Manager initialized");
		return manager;
	}
}
