import { TweenService, Workspace } from "@rbxts/services";

export function findBodyJoints(character: Model): Map<string, Motor6D | undefined> {
	const joints = new Map<string, Motor6D | undefined>();

	const torso = character.FindFirstChild("Torso") as Part | undefined;
	const upperTorso = character.FindFirstChild("UpperTorso") as Part | undefined;

	if (torso) {
		// R6
		joints.set("RightShoulder", torso.FindFirstChild("Right Shoulder") as Motor6D);
		joints.set("LeftShoulder", torso.FindFirstChild("Left Shoulder") as Motor6D);
		joints.set("Neck", torso.FindFirstChild("Neck") as Motor6D);
		joints.set("LeftHip", torso.FindFirstChild("Left Hip") as Motor6D);
		joints.set("RightHip", torso.FindFirstChild("Right Hip") as Motor6D);
		const hrp = character.FindFirstChild("HumanoidRootPart") as Part;
		if (hrp) {
			joints.set("RootJoint", hrp.FindFirstChild("RootJoint") as Motor6D);
		}
	} else if (upperTorso) {
		// R15
		const parts = character.GetDescendants().filter((inst) => inst.IsA("Motor6D")) as Motor6D[];
		for (const motor of parts) {
			// Right Shoulder
			if (
				motor.Name === "RightShoulder" ||
				(motor.Part0?.Name === "UpperTorso" && motor.Part1?.Name === "RightUpperArm") ||
				(motor.Part1?.Name === "UpperTorso" && motor.Part0?.Name === "RightUpperArm")
			) {
				joints.set("RightShoulder", motor);
			}
			// Left Shoulder
			if (
				motor.Name === "LeftShoulder" ||
				(motor.Part0?.Name === "UpperTorso" && motor.Part1?.Name === "LeftUpperArm") ||
				(motor.Part1?.Name === "UpperTorso" && motor.Part0?.Name === "LeftUpperArm")
			) {
				joints.set("LeftShoulder", motor);
			}
			// Neck
			if (motor.Name === "Neck" && motor.Parent?.Name === "Head") {
				joints.set("Neck", motor);
			}
			// Waist
			if (
				motor.Name === "Waist" ||
				(motor.Part0?.Name === "LowerTorso" && motor.Part1?.Name === "UpperTorso") ||
				(motor.Part1?.Name === "LowerTorso" && motor.Part0?.Name === "UpperTorso")
			) {
				joints.set("Waist", motor);
			}
			// RootJoint
			if (
				motor.Name === "Root" ||
				(motor.Part0?.Name === "HumanoidRootPart" && motor.Part1?.Name === "LowerTorso") ||
				(motor.Part1?.Name === "HumanoidRootPart" && motor.Part0?.Name === "LowerTorso")
			) {
				joints.set("RootJoint", motor);
			}
			// Left Hip
			if (
				motor.Name === "LeftHip" ||
				(motor.Part0?.Name === "LowerTorso" && motor.Part1?.Name === "LeftUpperLeg") ||
				(motor.Part1?.Name === "LowerTorso" && motor.Part0?.Name === "LeftUpperLeg")
			) {
				joints.set("LeftHip", motor);
			}
			// Right Hip
			if (
				motor.Name === "RightHip" ||
				(motor.Part0?.Name === "LowerTorso" && motor.Part1?.Name === "RightUpperLeg") ||
				(motor.Part1?.Name === "LowerTorso" && motor.Part0?.Name === "RightUpperLeg")
			) {
				joints.set("RightHip", motor);
			}
			// Left Knee
			if (
				motor.Name === "LeftKnee" ||
				(motor.Part0?.Name === "LeftUpperLeg" && motor.Part1?.Name === "LeftLowerLeg") ||
				(motor.Part1?.Name === "LeftUpperLeg" && motor.Part0?.Name === "LeftLowerLeg")
			) {
				joints.set("LeftKnee", motor);
			}
			// Right Knee
			if (
				motor.Name === "RightKnee" ||
				(motor.Part0?.Name === "RightUpperLeg" && motor.Part1?.Name === "RightLowerLeg") ||
				(motor.Part1?.Name === "RightUpperLeg" && motor.Part0?.Name === "RightLowerLeg")
			) {
				joints.set("RightKnee", motor);
			}
			// Left Elbow
			if (
				motor.Name === "LeftElbow" ||
				(motor.Part0?.Name === "LeftUpperArm" && motor.Part1?.Name === "LeftLowerArm") ||
				(motor.Part1?.Name === "LeftUpperArm" && motor.Part0?.Name === "LeftLowerArm")
			) {
				joints.set("LeftElbow", motor);
			}
			// Right Elbow
			if (
				motor.Name === "RightElbow" ||
				(motor.Part0?.Name === "RightUpperArm" && motor.Part1?.Name === "RightLowerArm") ||
				(motor.Part1?.Name === "RightUpperArm" && motor.Part0?.Name === "RightLowerArm")
			) {
				joints.set("RightElbow", motor);
			}
		}
	}

	// Debug: Print found joints
	for (const [name, joint] of joints) {
		print(`🔍 Joint ${name}: ${joint ? "✅ Found" : "❌ Missing"}`);
	}

	return joints;
}

export function storeJointPositions(joints: Map<string, Motor6D | undefined>): void {
	print(`💾 Storing original positions for ${joints.size()} joints...`);
	for (const [name, joint] of joints) {
		if (joint) {
			const originalC0 = joint.C0;
			joint.SetAttribute(`Original_${name}`, originalC0);
			print(`✅ Stored original for ${name}: ${originalC0}`);
		} else {
			print(`❌ Cannot store original for ${name} - joint is undefined`);
		}
	}
}

export function restoreBodyJoints(joints: Map<string, Motor6D | undefined>): void {
	print(`🔄 Restoring ${joints.size()} joints...`);
	for (const [name, joint] of joints) {
		if (joint) {
			const original = joint.GetAttribute(`Original_${name}`) as CFrame | undefined;
			const current = joint.C0;
			if (original) {
				print(`🔄 Restoring ${name}: ${current} -> ${original}`);
				const info = new TweenInfo(0.8, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut);
				const tween = TweenService.Create(joint, info, { C0: original });
				tween.Play();
				tween.Completed.Connect(() => {
					print(`✅ ${name} restoration completed`);
					joint.SetAttribute(`Original_${name}`, undefined);
				});
			} else {
				print(`❌ No original stored for ${name}, current: ${current}`);
			}
		} else {
			print(`❌ Joint ${name} is undefined`);
		}
	}
}
export function getJoint(character: Model, jointName: string): Motor6D | undefined {
	const joints = findBodyJoints(character);
	return joints.get(jointName);
}
