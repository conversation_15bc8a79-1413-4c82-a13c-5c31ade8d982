-- Compiled with roblox-ts v3.0.0
local AttackBehavior
do
	AttackBehavior = setmetatable({}, {
		__tostring = function()
			return "AttackBehavior"
		end,
	})
	AttackBehavior.__index = AttackBehavior
	function AttackBehavior.new(...)
		local self = setmetatable({}, AttackBehavior)
		return self:constructor(...) or self
	end
	function AttackBehavior:constructor()
		self.name = "Attack"
		self.priority = 7
	end
	function AttackBehavior:canExecute(context)
		if not context.target or not context.targetPosition then
			return false
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		return distance <= 5
	end
	function AttackBehavior:execute(context)
		if not context.target or not context.targetPosition then
			return {
				success = false,
				completed = true,
			}
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		if distance > 5 then
			return {
				success = false,
				completed = true,
				nextBehavior = "Follow",
			}
		end
		self:performAttack(context)
		return {
			success = true,
			completed = false,
		}
	end
	function AttackBehavior:onEnter(context)
		print(`⚔️ {context.entityId} is attacking target`)
	end
	function AttackBehavior:performAttack(context)
		local _condition = (context.blackboard.lastAttackTime)
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local lastAttackTime = _condition
		local currentTime = tick()
		if currentTime - lastAttackTime >= 1 then
			print(`💥 {context.entityId} attacks!`)
			context.blackboard.lastAttackTime = currentTime
		end
	end
end
return {
	AttackBehavior = AttackBehavior,
}
