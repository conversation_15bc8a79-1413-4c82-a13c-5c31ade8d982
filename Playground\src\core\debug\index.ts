// Debug System - Visual debugging tools for development
import { DebugManager } from "./DebugManager";

export { DebugManager, type DebugConfig } from "./DebugManager";
export { DebugRenderer, type DebugLine, type DebugSphere, type DebugText } from "./DebugRenderer";
export { AIDebugger } from "./AIDebugger";
export { PlayerDebugger } from "./PlayerDebugger";
export { PerformanceMonitor, type PerformanceData } from "./PerformanceMonitor";

/**
 * Quick setup function to initialize debug system
 * Call this in your main client script to enable debugging
 */
export function initializeDebugSystem(): void {
	const debugManager = DebugManager.getInstance();
	debugManager.initialize();

	print("🔧 Debug System initialized!");
	print("🔍 Use the Debug Panel button to control debug features");
	print("📱 Click the 🔧 Debug button in the bottom-left corner");
}

/**
 * Quick debug utilities for external use
 */
export const DebugUtils = {
	// Draw a line in 3D space
	drawLine: (from: Vector3, to: Vector3, color?: Color3, duration?: number) => {
		DebugManager.getInstance().drawLine(from, to, color, duration);
	},

	// Draw a sphere in 3D space
	drawSphere: (position: Vector3, radius?: number, color?: Color3, duration?: number) => {
		DebugManager.getInstance().drawSphere(position, radius, color, duration);
	},

	// Draw text in 3D space
	drawText: (position: Vector3, text: string, color?: Color3, duration?: number) => {
		DebugManager.getInstance().drawText(position, text, color, duration);
	},

	// Log debug message
	log: (category: string, message: string, data?: unknown) => {
		DebugManager.getInstance().logDebug(category, message, data);
	},

	// Check if debug is enabled
	isEnabled: () => {
		return DebugManager.getInstance().isEnabled();
	},

	// Toggle debug overlay
	toggle: () => {
		DebugManager.getInstance().toggle();
	},
};
