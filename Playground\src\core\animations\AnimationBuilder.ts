import { TweenService } from "@rbxts/services";

export class AnimationBuilder {
	private joint: Motor6D;
	private tweenInfo: TweenInfo;
	private targetCFrame: CFrame;
	private delayTime = 0;
	private completionCallback?: () => void;

	constructor(joint: Motor6D) {
		this.joint = joint;
		this.tweenInfo = new TweenInfo(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out);
		this.targetCFrame = joint.C0; // Default to current
	}

	static forJoint(joint: Motor6D): AnimationBuilder {
		return new AnimationBuilder(joint);
	}

	to(relativeCFrame: CFrame): this {
		this.targetCFrame = this.joint.C0.mul(relativeCFrame);
		return this;
	}

	toAbsolute(absoluteCFrame: CFrame): this {
		this.targetCFrame = absoluteCFrame;
		return this;
	}

	duration(time: number): this {
		this.tweenInfo = new TweenInfo(time, this.tweenInfo.EasingStyle, this.tweenInfo.EasingDirection);
		return this;
	}

	easing(style: Enum.EasingStyle, direction: Enum.EasingDirection): this {
		this.tweenInfo = new TweenInfo(this.tweenInfo.Time, style, direction);
		return this;
	}

	delay(delay: number): this {
		this.delayTime = delay;
		return this;
	}

	onComplete(callback: () => void): this {
		this.completionCallback = callback;
		return this;
	}

	play(): Tween {
		const tween = TweenService.Create(this.joint, this.tweenInfo, { C0: this.targetCFrame });
		if (this.delayTime > 0) {
			task.delay(this.delayTime, () => tween.Play());
		} else {
			tween.Play();
		}
		if (this.completionCallback) {
			tween.Completed.Connect(this.completionCallback);
		}
		return tween;
	}
}
